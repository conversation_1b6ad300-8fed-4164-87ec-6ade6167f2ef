<template>
	<view class="content">
		<view class="head" style="background: none;" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="left" @tap="back">
					<view class="itemList">
						<view class="item">
							<button>
								<image style="width:20rpx;" :src="imgUrl+'/qdkbm/newimage/fhui/back.png'">
								</image>
							</button>
						</view>
					</view>
				</view>
				<view class="text" style="color: #000;">编辑信息</view>
			</view>
		</view>

		<!-- <u-action-sheet cancelText="取消" :round="10" @select="selectClick" safeAreaInsetBottom @close="closeNj"
			:closeOnClickOverlay="true" :closeOnClickAction="true" :actions="njlist" title="请选择年级" :show="shownianji">

		</u-action-sheet> -->

		<u-popup safeAreaInsetBottom :closeable="false" :show="shownianji" :round="10" mode="bottom" @close="closeNj">
			<view class="cityPart" style="padding: 0;">
				<view class="title">请选择年级</view>
				<view class="cityList njList">
					<view class="item" @tap="selectClick(item)" :class="selectedNj === item.name?'active':''"
						v-for="(item,index) in njlist" :key="index">
						{{item.name}}
					</view>
				</view>
			</view>
		</u-popup>

		<u-popup safeAreaInsetBottom closeable :show="show" :round="10" mode="bottom" @close="close">
			<view class="cityPart">
				<view class="title">请选择高考省份</view>
				<view class="cityList">
					<view class="item" @tap="changeProvince(item)" :class="selectedProvince.id === item.id?'active':''"
						v-for="(item,index) in provinces" :key="index">
						{{item.choiceContent}}
					</view>
				</view>
			</view>
		</u-popup>
		<view class="loading-mask" v-if="loading">
			<view class="loading-spinner"></view>
		</view>
		<view class="bg" :style="{'background-image':'url('+imgUrl+'/qdkbm/newimage/fhui/m-bg2.png)'}">
			<view class="studyPage">
				<view class="tits">
					<view class="left">
						学习信息
					</view>
					<view class="right">
						<text>本平台只提供普通高考类相关服务</text>
					</view>
				</view>
				<view class="form">
					<view class="form-item">
						<view class="label">
							用户昵称<text>*</text>
						</view>
						<view class="textInfo">
							<view class="input">
								<input type="text" v-model="username" placeholder="请输入用户昵称" placeholder-style="color:#7e7e7e;"
									maxlength="12" @input="validateUsername" />
								<view class="error-tip" v-if="usernameError">{{usernameError}}</view>
							</view>
						</view>
					</view>
					<view class="form-item">
						<view class="label">
							高考省份<text>*</text>
						</view>
						<view class="textInfo">
							<view class="select" @tap="showProvince">
								<view class="text" :class="selectedProvince.choiceContent?'active':''">
									{{selectedProvince.choiceContent?selectedProvince.choiceContent:'请选择'}}
								</view>
								<view class="more">
									<image :src="imgUrl+'/qdkbm/newimage/fhui/icon-downs.png'"></image>
								</view>
							</view>
						</view>
					</view>
					<view class="form-item">
						<view class="label">
							所属年级<text>*</text>
						</view>
						<view class="textInfo">
							<view class="select" @tap="showNj">
								<view class="text" :class="selectedNj?'active':''">{{selectedNj?selectedNj:'请选择'}}</view>
								<view class="more">
									<image :src="imgUrl+'/qdkbm/newimage/fhui/icon-downs.png'"></image>
								</view>
							</view>
						</view>
					</view>
					<view class="form-item">
						<view class="label">
							学生选科<text>*</text>
						</view>
						<view class="textInfo">
							<view v-if="selectedProvince.choiceContent === '浙江'">
								<!-- 浙江省选科模式为7选3 -->
								<view class="xkTitle">选科（7选3）</view>
								<view class="xkList">
									<view class="item" :class="item.selected ? 'active' : ''" @tap="toggleSecondarySubject"
										:data-subject="item" v-for="(item,index) in secondarySubjects" :key="index">
										{{item.name}}
									</view>
								</view>
							</view>
							<view v-else>
								<!-- 首选科目 -->
								<view class="xkTitle">首选科目（二选一）</view>
								<view class="xkList primary-subject">
									<view class="item" :class="item.selected ? 'active' : ''" @tap="togglePrimarySubject"
										:data-subject="item" v-for="(item,index) in secondarySubjects" :key="index" v-if="item.type === 'primary'">
										{{item.name}}
									</view>
								</view>

								<!-- 次选科目 -->
								<view class="xkTitle">次选科目（四选二）</view>
								<view class="xkList">
									<view class="item" :class="item.selected ? 'active' : ''" @tap="toggleSecondarySubject"
										:data-subject="item" v-for="(item,index) in secondarySubjects" :key="index" v-if="item.type === 'secondary'">
										{{item.name}}
									</view>
								</view>
							</view>

							<!-- 选科提示信息 -->
							<view class="subject-info" v-if="subjectError">
								<text class="error-tip">{{subjectError}}</text>
							</view>
						</view>
					</view>
					<view class="form-item">
						<view class="label">
							我的成绩<text>*</text>
						</view>
						<view class="textInfo">
							<view class="input">
								<input type="number" v-model="myscore" placeholder="请输入成绩" placeholder-style="color:#7e7e7e"
									maxlength="5" />
								<view class="more">
									分
								</view>
								<view class="error-tip" v-if="scoreError">{{scoreError}}</view>
								<!-- <view class="score-limit-tip" v-if="remainingModifications < scoreModificationLimit">
									今日还可修改分数 {{remainingModifications}} 次
								</view> -->
							</view>
						</view>
					</view>
					<view class="submit-item">
						<button @tap="submitInfo()" :loading="loading" :disabled="disabled">确认</button>
					</view>
				</view>
			</view>
		</view>

		<!-- <view class="container">
			<view class="info-section">
				<view class="section-title">学习信息（本平台只提供普通高考类相关服务）</view>


				<view class="info-item row-container">
					<view class="info-label">高考省份</view>
					<picker mode="selector" :range="provinces" @change="bindProvinceChange">
						<view class="info-value">{{province || '请选择'}}<text class="arrow">›</text></view>
					</picker>
				</view>


				<view class="info-item row-container">
					<view class="info-label">所属年级</view>
					<picker mode="selector" :range="grades" @change="bindGradeChange">
						<view class="info-value">{{grade || '请选择'}}<text class="arrow">›</text></view>
					</picker>
				</view>


				<view class="info-item">
					<view class="info-label">首选科目（二选一）</view>
					<view class="main-subject-container">
						<block v-for="(item,index) in mainSubjects" :key="index">
							<view class="main-subject-item" :class="mainSubject === item ? 'selected' : ''"
								@tap="selectMainSubject" :data-subject="item">
								{{item}}
							</view>
						</block>
					</view>
				</view>

				<view class="info-item">
					<view class="info-label">次选科目（四选二）</view>
					<view class="sub-subject-container">
						<block v-for="(item,index) in subSubjects" :key="index">
							<view class="sub-subject-item" :class="item.selected ? 'selected' : ''"
								@tap="toggleSubSubject" :data-subject="item">
								{{item.name}}
							</view>
						</block>
					</view>
				</view>


				<view class="info-item">
					<view class="info-label">我的成绩</view>
					<input type="number" placeholder="请输入分数" @input="bindScoreInput" :value="score" />
				</view>


				<button class="save-btn" @tap="saveInfo">保存信息</button>
			</view>
		</view> -->
	</view>
</template>

<script>
	import tabBar from '@/components/tabBar.vue'
	import provinces from '@/utils/data/provinces.js'
	export default {
		components: {
			tabBar
		},
		data() {
			return {
				loading: false,
				show: false,
				shownianji: false,
				selectedNj: "",
				njlist: [{
					id:1,
					name: '高一'
				}, {
					id:2,
					name: '高二'
				}, {
					id:3,
					name: '高三'
				}],
				selectedProvince: {},
				titleTop: 0,
				imgUrl: this.$base.uploadImgUrl,
				// 次选科目
				secondarySubjects: [],
				// 已选科目
				selectedSecondarySubjects: [],
				subjectError: "",
				score: '',
				provinces: provinces,
				disabled: false,
				province: '',
				myscore: "",
				username:"",
				usernameError:"",
				scoreError:"",
				// 分数修改相关
				scoreModificationLimit: 99, // 每天允许修改分数的最大次数（开发阶段设置为99次）
				remainingModifications: 99, // 剩余修改次数
				recommendedMajors:"",
			}

		},
		onLoad(options) {
			console.log('编辑页面加载数据:', options)
			let that = this
			that.loading = true

			// 初始化分数修改次数
			that.initScoreModificationCount();

			// 先从本地存储中获取用户信息
			let storedUserInfo = uni.getStorageSync('myInfo');
			console.log('本地存储的用户信息:', storedUserInfo);

			// 初始化默认省份和科目
			that.initSecondarySubjects('北京');

			// 尝试从参数中解析用户信息
			let paramUserInfo = null;
			if (options && options.item) {
				try {
					paramUserInfo = JSON.parse(decodeURIComponent(options.item));
					console.log('从参数解析的用户信息:', paramUserInfo);
				} catch (error) {
					console.error('解析参数数据出错:', error);
				}
			}

			// 优先使用参数中的用户信息，如果没有则使用本地存储的信息
			let userInfo = paramUserInfo || storedUserInfo || {};
			console.log('最终使用的用户信息:', userInfo);

			// 设置用户名
			that.username = userInfo.username || '';

			// 设置省份
			if (userInfo.province) {
				that.provinces.forEach((province) => {
					if (province.choiceContent == userInfo.province) {
						that.selectedProvince = province;
					}
				});

				// 初始化次选科目
				that.initSecondarySubjects(userInfo.province);
			}

			// 解析已选科目
			// 先检查secondSubject字段，如果有则优先使用
			if (userInfo.secondSubject) {
				console.log('使用secondSubject字段设置已选科目:', userInfo.secondSubject);
				const subjectArray = userInfo.secondSubject.split(',');

				// 分离首选科目和次选科目
				subjectArray.forEach((subjectName) => {
					// 检查是否为首选科目
					const primarySubject = that.secondarySubjects.find(s => s.name === subjectName && s.type === 'primary');
					if (primarySubject) {
						primarySubject.selected = true;
					} else {
						// 检查是否为次选科目
						const secondarySubject = that.secondarySubjects.find(s => s.name === subjectName && s.type === 'secondary');
						if (secondarySubject) {
							secondarySubject.selected = true;
							that.selectedSecondarySubjects.push(subjectName);
						}
					}
				});
			}
			// 如果没有secondSubject字段，则尝试使用subjects数组
			else if (userInfo.subjects && userInfo.subjects.length > 0) {
				console.log('使用subjects数组设置已选科目:', userInfo.subjects);
				// 分离首选科目和次选科目
				userInfo.subjects.forEach((subjectName) => {
					// 检查是否为首选科目
					const primarySubject = that.secondarySubjects.find(s => s.name === subjectName && s.type === 'primary');
					if (primarySubject) {
						primarySubject.selected = true;
					} else {
						// 检查是否为次选科目
						const secondarySubject = that.secondarySubjects.find(s => s.name === subjectName && s.type === 'secondary');
						if (secondarySubject) {
							secondarySubject.selected = true;
							that.selectedSecondarySubjects.push(subjectName);
						}
					}
				});
			}
			// 如果没有任何选科数据，设置默认选科为物理
			else {
				console.log('设置默认选科: 物理');
				// 默认选择物理作为首选科目
				const defaultSubject = that.secondarySubjects.find(s => s.name === '物理' && s.type === 'primary');
				if (defaultSubject) {
					defaultSubject.selected = true;
				}
			}

			// 设置年级
			if (userInfo.grade) {
				console.log('设置年级:', userInfo.grade);
				that.selectedNj = userInfo.grade;
			} else {
				// 设置默认年级为高三
				that.selectedNj = '高三';
				console.log('设置默认年级: 高三');
			}

			// 设置分数
			if (userInfo.score) {
				console.log('设置分数:', userInfo.score);
				that.myscore = userInfo.score;
			}
			//设置推荐专业
			if (userInfo.recommendedMajors) {
				console.log('设置推荐专业:', userInfo.recommendedMajors);
				that.recommendedMajors = userInfo.recommendedMajors;
			}

			// 保存原始数据到本地存储，以便在提交时使用
			uni.setStorageSync('editProfileOriginalData', userInfo);

			setTimeout(() => {
				that.loading = false;
			}, 600);
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top
		},
		watch: {
			myscore(newVal) {
				// 如果为空，允许
				if (!newVal) {
					this.scoreError = "";
					return;
				}

				// 确保newVal是字符串类型
				let strVal = String(newVal);

				// 移除非数字字符（不允许小数点）
				let formatted = strVal.replace(/[^\d]/g, '');

				// 检查数值范围
				let numValue = parseInt(formatted);
				if (!isNaN(numValue)) {
					// 确保大于0
					if (numValue <= 0) {
						this.scoreError = "输入的成绩必须大于0分";
					} else {
						this.scoreError = "";
					}

					// 确保不超过750
					if (numValue > 750) {
						formatted = "750";
					}
				}

				// 如果格式化后的值与当前值不同，更新它
				if (formatted !== strVal) {
					this.$nextTick(() => {
						this.myscore = formatted;
					});
				}
			}
		},
		methods: {
			submitInfo() {
				// 提交前再次验证用户名和成绩
				if (!this.username.trim()) {
					this.usernameError = "昵称不能为空";
					return;
				}

				const specialCharsRegex = /[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：""【】、；''，。、]/;
				if(specialCharsRegex.test(this.username)){
					this.usernameError = "昵称不能包含特殊字符";
					return;
				}

				if(this.username.length > 12){
					this.usernameError = "昵称长度不能超过12个字符";
					return;
				}

				// 验证成绩
				if(!this.myscore) {
					this.scoreError = "成绩不能为空";
					return;
				}

				const score = parseInt(this.myscore);
				if(score > 750){
					this.scoreError = "输入的成绩不能超过750分";
					return;
				}

				if(score <= 0){
					this.scoreError = "输入的成绩必须大于0分";
					return;
				}

				// 检查分数修改次数限制
				const currentScore = uni.getStorageSync('myInfo')?.score;
				if (currentScore && currentScore !== this.myscore) {
					// 用户正在修改分数
					const scoreModifications = this.getScoreModifications();

					// 检查今天的修改次数是否已达上限
					if (scoreModifications.count >= this.scoreModificationLimit) {
						uni.showToast({
							title: `每天最多只能修改分数${this.scoreModificationLimit}次，请明天再试`,
							icon: 'none',
							duration: 2000
						});
						return;
					}
				}

				this.disabled = true;
				this.loading = true;

				// 验证其他字段
				if (!this.selectedProvince.choiceContent) {
					this.scoreError = "";
					this.usernameError = "";
					uni.showToast({
						title: '请选择高考省份',
						icon: 'none'
					});
					this.disabled = false;
					this.loading = false;
					return;
				}

				if (!this.selectedNj) {
					this.scoreError = "";
					this.usernameError = "";
					uni.showToast({
						title: '请选择所属年级',
						icon: 'none'
					});
					this.disabled = false;
					this.loading = false;
					return;
				}

				// 验证选科
				const isZhejiang = this.selectedProvince.choiceContent === '浙江';
				const requiredSecondaryCount = isZhejiang ? 3 : 2;

				// 验证首选科目 (浙江省不需要首选科目)
				if (!isZhejiang && !this.secondarySubjects.find(s => s.selected && s.type === 'primary')) {
					this.subjectError = "请选择一门首选科目";
					this.disabled = false;
					this.loading = false;
					return;
				}

				// 验证次选科目
				const selectedSecondaryCount = this.secondarySubjects.filter(s => s.selected && s.type === 'secondary').length;
				if (selectedSecondaryCount !== requiredSecondaryCount) {
					if (isZhejiang) {
						this.subjectError = `浙江省选科模式为7选3，请选择3门科目`;
					} else {
						this.subjectError = `请选择${requiredSecondaryCount}门次选科目`;
					}
					this.disabled = false;
					this.loading = false;
					return;
				}

				// 合并首选和次选科目
				let allSelectedSubjects = [];
				if (isZhejiang) {
					// 浙江省是7选3模式，没有首选/次选的区分
					allSelectedSubjects = this.secondarySubjects.filter(s => s.selected).map(s => s.name);
				} else {
					// 其他省份有首选科目和次选科目
					const primarySubject = this.secondarySubjects.find(s => s.selected && s.type === 'primary');
					const secondarySubjects = this.secondarySubjects.filter(s => s.selected && s.type === 'secondary');

					if (primarySubject) {
						allSelectedSubjects = [primarySubject.name, ...secondarySubjects.map(s => s.name)];
					} else {
						allSelectedSubjects = [...secondarySubjects.map(s => s.name)];
					}
				}

				// 将所有选择的科目合并为一个字符串，第一个为首选科目（如果有），后面的为次选科目
				const combinedSubjects = allSelectedSubjects.join(',');

				// 所有验证通过，提交表单
				this.$apis.updateUserInfo({
					id: uni.getStorageSync('userId'),
					username: this.username,
					province: this.selectedProvince.choiceContent,
					grade: this.selectedNj,
					secondSubject: combinedSubjects, // 合并后的科目字符串
					score: this.myscore
				}).then((res) => {
					if (res.code == 0) {
						// 如果分数已修改，记录修改次数
						if (currentScore && currentScore !== this.myscore) {
							this.recordScoreModification();
						}

						uni.showToast({
							title: "保存成功",
							icon: 'success',
							duration: 1000
						});
						let myInfo = {
							score: this.myscore,
							province: this.selectedProvince.choiceContent,
							grade: this.selectedNj,
							subjects: allSelectedSubjects,
							username: this.username
						};
						setTimeout(() => {
							this.loading = false;
							this.disabled = false;
						}, 800);
						uni.setStorageSync('userName',this.username);
						uni.setStorageSync('myInfo', myInfo);
						setTimeout(() => {
							uni.navigateBack({
								delta: 1
							});
						}, 1000);
					} else {
						setTimeout(() => {
							this.loading = false;
							this.disabled = false;
						}, 800);
					}
				}).catch((err) => {
					setTimeout(() => {
						this.loading = false;
						this.disabled = false;
					}, 800);
				});
			},
			selectClick(item) {
				this.selectedNj = item.name
				this.closeNj()
			},
			changeProvince(item) {
				this.selectedProvince = item
				this.close()

				// 重置选科
				this.resetSubjects()

				// 初始化次选科目
				this.initSecondarySubjects(item.choiceContent)
			},
			// 初始化次选科目
			initSecondarySubjects(province) {
				if (province === '浙江') {
					// 浙江省是7选3模式，没有首选/次选的区分
					this.secondarySubjects = [
						{ id: 1, name: "物理", selected: false, type: "secondary" },
						{ id: 2, name: "化学", selected: false, type: "secondary" },
						{ id: 3, name: "生物", selected: false, type: "secondary" },
						{ id: 4, name: "政治", selected: false, type: "secondary" },
						{ id: 5, name: "历史", selected: false, type: "secondary" },
						{ id: 6, name: "地理", selected: false, type: "secondary" },
						{ id: 7, name: "技术", selected: false, type: "secondary" }
					];
				} else {
					// 其他省份保持原有的首选/次选模式，但使用同一个数组
					this.secondarySubjects = [
						// 首选科目（不默认选中，由数据决定）
						{ id: 1, name: "物理", selected: false, type: "primary" },
						{ id: 2, name: "历史", selected: false, type: "primary" },
						// 次选科目
						{ id: 3, name: "化学", selected: false, type: "secondary" },
						{ id: 4, name: "生物", selected: false, type: "secondary" },
						{ id: 5, name: "政治", selected: false, type: "secondary" },
						{ id: 6, name: "地理", selected: false, type: "secondary" }
					];
				}
			},

			// 重置选科
			resetSubjects() {
				// 重置首选科目
				this.secondarySubjects.forEach(item => {
					if (item.type === 'primary') {
						item.selected = false;
					}
				});

				// 重置次选科目
				this.secondarySubjects.forEach(item => {
					if (item.type === 'secondary') {
						item.selected = false;
					}
				});

				// 清除错误提示
				this.subjectError = "";
			},

			// 切换首选科目
			togglePrimarySubject(e) {
				const subject = e.currentTarget.dataset.subject;

				// 重置所有首选科目的选择状态
				this.secondarySubjects.forEach(item => {
					if (item.type === 'primary') {
						item.selected = (item.id === subject.id);
					}
				});

				// 清除错误提示
				this.subjectError = "";
			},

			// 切换次选科目
			toggleSecondarySubject(e) {
				const subject = e.currentTarget.dataset.subject;
				const isZhejiang = this.selectedProvince.choiceContent === '浙江';
				const maxSelection = isZhejiang ? 3 : 2;

				// 找到当前科目
				const currentSubject = this.secondarySubjects.find(item => item.id === subject.id);
				if (!currentSubject) return;

				// 如果已经选中，则取消选中
				if (currentSubject.selected) {
					currentSubject.selected = false;
					this.selectedSecondarySubjects = this.selectedSecondarySubjects.filter(name => name !== subject.name);
				}
				// 如果未选中，检查是否超过最大选择数量
				else {
					const selectedCount = this.secondarySubjects.filter(item => item.selected && item.type === 'secondary').length;
					if (selectedCount >= maxSelection) {
						if (isZhejiang) {
							this.subjectError = `浙江省选科模式为7选3，最多选择3门科目`;
						} else {
							this.subjectError = `请选择${maxSelection}门次选科目`;
						}
						return;
					}

					currentSubject.selected = true;
					this.selectedSecondarySubjects.push(subject.name);
				}

				// 清除错误提示
				this.subjectError = "";
			},
			validateUsername(e){
				const value = e.detail.value;
				// 检查是否为空
				if(!value.trim()) {
					this.usernameError = "昵称不能为空";
					return;
				}

				// 检查长度限制
				if(value.length > 12){
					this.usernameError = "昵称长度不能超过12个字符";
					return;
				}

				// 检查特殊字符
				const specialCharsRegex = /[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：""【】、；''，。、]/;
				if(specialCharsRegex.test(value)){
					this.usernameError = "昵称不能包含特殊字符";
					return;
				}

				this.usernameError = "";
			},
			// 移除validateScore方法，因为我们现在使用watch
			showProvince() {
				this.show = true
			},
			close() {
				this.show = false
			},
			showNj() {
				this.shownianji = true
			},
			closeNj() {
				this.shownianji = false
			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			},
			// 获取分数修改记录
			getScoreModifications() {
				// 从本地存储获取分数修改记录
				const scoreModData = uni.getStorageSync('scoreModifications');

				if (!scoreModData) {
					// 如果没有记录，创建初始记录
					return {
						date: this.getCurrentDate(),
						count: 0
					};
				}

				// 检查是否是今天的记录
				if (scoreModData.date !== this.getCurrentDate()) {
					// 如果不是今天的记录，重置计数
					return {
						date: this.getCurrentDate(),
						count: 0
					};
				}

				return scoreModData;
			},

			// 记录分数修改
			recordScoreModification() {
				const scoreModData = this.getScoreModifications();

				// 增加修改次数
				scoreModData.count += 1;

				// 更新剩余修改次数
				this.remainingModifications = Math.max(0, this.scoreModificationLimit - scoreModData.count);

				// 保存到本地存储
				uni.setStorageSync('scoreModifications', scoreModData);
			},

			// 初始化分数修改次数
			initScoreModificationCount() {
				const scoreModData = this.getScoreModifications();
				this.remainingModifications = Math.max(0, this.scoreModificationLimit - scoreModData.count);
			},

			// 获取当前日期（格式：YYYY-MM-DD）
			getCurrentDate() {
				const now = new Date();
				const year = now.getFullYear();
				const month = String(now.getMonth() + 1).padStart(2, '0');
				const day = String(now.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			},
			updateSelectedSubjects() {
				this.selectedSecondarySubjects = this.secondarySubjects.filter(s => s.selected && s.type === 'secondary').map(s => s.name);
			}
		}
	}
</script>
<style>
	page {
		background: #f5f5f5;
	}
</style>

<style scoped lang="scss">
	@-webkit-keyframes countloading {
		0% {
			transform: rotate(180deg)
		}

		100% {
			transform: rotate(0deg)
		}
	}

	@keyframes countloading {
		0% {
			transform: rotate(180deg)
		}

		100% {
			transform: rotate(0deg)
		}
	}


	// .progress-active-loading{animation:progress-active 2.4s cubic-bezier(.23,1,.32,1) infinite;background-color:#fff;height:100%;position:absolute;width:100%;z-index:999}
	@-webkit-keyframes progress-active {
		0% {
			opacity: .1;
			transform: translateX(-100%) scaleX(0)
		}

		20% {
			opacity: .5;
			transform: translateX(-100%) scaleX(0)
		}

		100% {
			opacity: 0;
			transform: translateX(0) scaleX(1)
		}
	}

	@keyframes progress-active {
		0% {
			opacity: .1;
			transform: translateX(-100%) scaleX(0)
		}

		20% {
			opacity: .5;
			transform: translateX(-100%) scaleX(0)
		}

		100% {
			opacity: 0;
			transform: translateX(0) scaleX(1)
		}
	}


	.loading-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;

		.loading-spinner {
			width: 50rpx;
			height: 50rpx;
			// border: 8rpx solid #c8a178;
			// border-top-color: transparent;
			// border-radius: 50%;
			animation: countloading 1s ease infinite;
			// animation:progress-active 2.4s cubic-bezier(.23,1,.32,1) infinite;
			background-image: url('https://pic.kefeichangduo.top/qdkbm/newimage/fhui/waiting.png');
			background-position: 50%;
			background-repeat: no-repeat;
			background-size: contain;
		}
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(360deg);
		}
	}

	.cityPart {
		padding: 0 25rpx;

		.title {
			padding: 30rpx 0;
			font-size: 28rpx;
			color: #333;
			display: flex;
			align-items: center;
			justify-content: center;
			font-weight: 700;
		}

		.cityList {
			padding-bottom: 30rpx;
			display: flex;
			flex-direction: row;
			flex-wrap: wrap;

			.item {
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0 10rpx;
				margin-bottom: 20rpx;
				width: calc(25% - 20rpx);
				background: #efefef;
				height: 70rpx;
				line-height: 70rpx;
				font-size: 28rpx;
				border-radius: 8rpx;

				&.active {
					color: #fff;
					background: #ff7e17;
				}
			}
			&.njList{
				.item{border-radius: 0;margin:0;
					font-size:32rpx;font-weight: 700;
					width: 100%;margin-bottom: 0;height:90rpx;line-height:90rpx;background:none;border-top:2rpx solid #e5e5e5;
					&:last-child{border-bottom: none;}
					&.active{
						color:#ff7e17;
					}
				}
			}
		}
	}

	.studyPage {
		margin: 0 40rpx;
		margin-top: 450rpx;
		padding: 0 30rpx;
		padding-bottom: 40rpx;
		background: #fff;
		border-radius: 24rpx;
		margin-bottom:80rpx;
		box-shadow: 0 0 15rpx 8rpx rgba(0, 0, 0, 0.06);

		.tits {
			padding: 25rpx 0;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-between;

			.left {
				font-size: 28rpx;
				font-weight: 700;
				color: #000;
			}

			.right {
				text {
					border: 2rpx solid #ff8918;
					border-radius: 10rpx;
					font-size: 24rpx;
					color: #ff8918;
					padding: 5rpx 15rpx;
				}
			}
		}

		.form {
			.xkList {
				margin: 0 -10rpx;
				display: flex;
				flex-direction: row;
				flex-wrap: wrap;

				.item {
					width: calc(25% - 20rpx);
					height: 80rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					background: #f1f1f1;
					color:#333;
					border-radius: 10rpx;
					font-size: 32rpx;
					margin: 0 10rpx;
					margin-bottom: 20rpx;

					&.active {
						background: #ff8918;
						color: #fff;
					}
				}
			}

			.submit-item {
				padding-top: 20rpx;

				button {
					height: 80rpx;
					line-height: 80rpx;
					border-radius: 80rpx;
					color: #fff;
					font-size: 32rpx;
					text-align: center;
					background-image: linear-gradient(180deg, #FF8510 0, #FFBD73 100%);

					&[disabled]:not([type]) {
						background-image: linear-gradient(180deg, #f5f5f5 0, #f5f5f5 100%) !important;
						color: #999 !important;
					}

					&::after {
						border: none;
					}
				}
			}

			.form-item {
				margin-bottom: 20rpx;

				.label {
					margin-bottom: 25rpx;
					font-size: 30rpx;
					display: flex;
					font-weight: 700;
					flex-direction: row;
					align-items: center;

					text {
						color: #ff573e;
						font-weight: 700;
						font-size: 30rpx;
						margin-left: 10rpx;
					}
				}

				.select {
					height: 80rpx;
					background: #f1f1f1;
					border-radius: 12rpx;
					position: relative;
					display: flex;
					flex-direction: row;
					justify-content: space-between;
					align-items: center;

					.text {
						font-size: 32rpx;
						color: #666;
						margin-left: 30rpx;
						&.active{
							color:#000;
						}
					}

					.more {
						margin-right: 20rpx;
						display: flex;
						align-items: center;
						justify-content: center;

						image {
							width: 32rpx;
							height: 32rpx;
						}
					}
				}

				.input {
					height: 80rpx;
					background: #f1f1f1;
					border-radius: 12rpx;
					position: relative;
					display: flex;
					flex-direction: row;
					justify-content: space-between;
					align-items: center;

					input {
						font-size: 32rpx;
						color: #000;
						margin-left: 30rpx;
					}

					.more {
						margin-right: 20rpx;
						font-size: 28rpx;
						color: #999;
					}
					.error-tip{
						margin-top: 10rpx;
						font-size: 24rpx;
						color: #ff573e;
					}
					.score-limit-tip{
						margin-top: 10rpx;
						font-size: 24rpx;
						color: #999;
					}
				}
			}
		}
	}

	.content {
		position: relative;

		.bg {
			position: absolute;
			left: 0;
			top: 0;
			background-repeat: no-repeat;
			background-size: 100% auto;
			width: 100%;
			background-color:#fffdfa;
			min-height: 100vh;
		}
	}

	.container {

		background-color: #f5f5f5;

	}

	.info-section {
		margin: 30rpx;
		padding: 30rpx;
		background-color: #fff;
		border-radius: 16rpx;
	}

	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 30rpx;
	}

	.info-item {
		margin-bottom: 30rpx;
	}

	.row-container {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.info-label {
		font-size: 28rpx;
		color: #666;
		white-space: nowrap;
	}

	.info-value {
		font-size: 28rpx;
		color: #333;
		padding: 20rpx;
		background-color: #f5f5f5;
		border-radius: 8rpx;
		flex: 1;
		margin-left: 20rpx;
	}

	.save-btn {
		margin-top: 40rpx;
		background-color: #4d6bfe;
		color: #fff;
		border-radius: 8rpx;
	}

	.main-subject-container {
		display: flex;
		flex-wrap: wrap;
		margin-top: 20rpx;
	}

	.main-subject-item {
		padding: 20rpx 40rpx;
		margin: 10rpx;
		border: 1rpx solid #4d6bfe;
		border-radius: 8rpx;
		color: #4d6bfe;
		font-size: 28rpx;
	}

	.main-subject-item.selected {
		background-color: #4d6bfe;
		color: #fff;
	}

	.sub-subject-container {
		display: flex;
		flex-wrap: wrap;
		margin-top: 20rpx;
	}

	.sub-subject-item {
		padding: 20rpx 32rpx;
		margin: 10rpx;
		border: 1rpx solid #4d6bfe;
		border-radius: 8rpx;
		color: #4d6bfe;
		font-size: 28rpx;
	}

	.sub-subject-item.selected {
		background-color: #4d6bfe;
		color: #fff;
	}

	.arrow {
		margin-left: 10rpx;
		color: #999;
	}

	.xkTitle {
		font-size: 28rpx;
		color: #666;
		margin: 20rpx 0 10rpx;
	}

	.primary-subject {
		margin-bottom: 30rpx;
	}

	.subject-info {
		margin-top: 10rpx;
	}

	.error-tip {
		color: #f56c6c;
		font-size: 24rpx;
	}
</style>