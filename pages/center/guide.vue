<template>
	<view class="content">
		<view class="head" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="left" @tap="back">
					<view class="itemList">
						<view class="item">
							<button>
								<image style="width:20rpx;"
									:src="imgUrl+'/qdkbm/newimage/fhui/back-light.png'">
								</image>
							</button>
						</view>
					</view>
				</view>
				<view class="text">使用须知</view>
			</view>
		</view>
		<view class="loading-mask" v-if="loading">
			<view class="loading-spinner"></view>
		</view>
		<view class="guide-container">
			<view class="desc">欢迎使用高考志愿填报助手小程序！为了保障您的权益并确保服务的正常使用，请仔细阅读以下内容。使用本小程序即表示您同意遵守以下条款和规定。
			</view>
			<view class="boxstrap">
				<view class="icon">
					<image :src="imgUrl+'/qdkbm/newimage/fhui/icon-laba.png'"></image>
				</view>
				<view class="item">
					<view ><text style="font-weight: 700;">一、账号使用</text></view>

					<view><text style="font-weight: 700;">1. 账号注册 </text></view>
					<view class="desc2"> - 您需要使用手机号或其他指定方式注册账号，并确保提供的信息真实、准确。</view>

					<view class="desc2"> - 每个手机号仅能注册一个账号，禁止重复注册。</view>
				</view>
				<view class="item">
					<view><text style="font-weight: 700;">2. 账号安全</text></view>
					<view class="desc2"> - 您需妥善保管账号和密码，避免泄露或共享给他人。</view>
					<view class="desc2"> - 如发现账号被盗或异常，请立即联系客服处理。</view>
				</view>
				<view class="item">
					<view><text style="font-weight: 700;">3. 账号注销</text></view>
					<view class="desc2"> - 您可以通过小程序内的注销功能申请注销账号，注销后数据将无法恢复。</view>
				</view>
				<view class="item">
					<view><text style="font-weight: 700;">二、隐私政策</text></view>

					<view><text style="font-weight: 700;">1. 信息收集</text></view>

					<view class="desc2">- 我们可能会收集您的个人信息（如手机号、高考分数、志愿选择等），以便为您提供更好的服务。</view>

					<view class="desc2">- 我们承诺不会将您的个人信息用于未经授权的用途。</view>
				</view>
				<view class="item">
					<view><text style="font-weight: 700;">2. 数据使用</text></view>

					<view class="desc2">- 您的数据将用于志愿推荐、数据分析及服务优化。</view>

					<view class="desc2">- 我们不会将您的个人信息出售或共享给第三方，除非获得您的明确同意或法律法规要求。</view>


				</view>
				<view class="item">
					<view><text style="font-weight: 700;">3. 数据安全</text></view>

					<view class="desc2">- 我们采用技术手段保护您的数据安全，防止未经授权的访问、泄露或篡改。</view>



				</view>
				<view class="item">
					<view><text style="font-weight: 700;">三、服务条款</text></view>

					<view><text style="font-weight: 700;">1. 服务内容</text></view>

					<view class="desc2">- 本小程序提供高考志愿填报建议、院校信息查询、录取概率预测等服务。</view>

					<view class="desc2"> - 所有服务仅供参考，最终决策需结合您的实际情况和官方信息。</view>


				</view>
				<view class="item">
					<view><text style="font-weight: 700;">2. 服务变更 </text></view>

					<view class="desc2">- 我们保留随时修改、暂停或终止部分或全部服务的权利，恕不另行通知。</view>

				</view>
				<view class="item">
					<view><text style="font-weight: 700;">3. 用户责任</text></view>

					<view class="desc2"> - 您需确保填写的信息真实、准确，并对您的填报决策负责。</view>


				</view>
				<view class="item">
					<view><text style="font-weight: 700;">四、免责声明</text></view>

					<view><text style="font-weight: 700;">1. 建议仅供参考</text> </view>

					<view class="desc2"> - 本小程序提供的志愿填报建议基于算法和历史数据，仅供参考，不保证100%准确性。</view>

					<view class="desc2"> - 您应结合自身情况、官方政策和院校信息做出最终决策。</view>

				</view>
				<view class="item">
					<view><text style="font-weight: 700;">2. 不可抗力</text></view>

					<view class="desc2">- 因不可抗力（如网络故障、自然灾害等）导致的服务中断或数据丢失，我们不承担责任。</view>

				</view>
				<view class="item">
					<view><text style="font-weight: 700;">3. 第三方责任 </text></view>

					<view class="desc2">- 如因第三方服务（如支付平台、数据分析服务）导致的损失，我们无法承担责任。</view>

				</view>
				<view class="item">
					<view><text style="font-weight: 700;">五、用户行为规范</text></view>

					<view><text style="font-weight: 700;">1. 禁止行为</text></view>

					<view class="desc2">- 禁止发布违法、虚假或恶意信息。</view>

					<view class="desc2"> - 禁止恶意攻击系统、刷数据或干扰其他用户正常使用。</view>

				</view>
				<view class="item">
					<view><text style="font-weight: 700;">2. 违规处理</text></view>

					<view class="desc2">- 如发现违规行为，我们有权暂停或终止您的账号使用，并保留追究法律责任的权利。</view>

				</view>
				<view class="item">
					<view><text style="font-weight: 700;">3. 文明使用 </text></view>

					<view class="desc2"> - 请遵守法律法规和社会公德，文明使用本小程序。</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import tabBar from '@/components/tabBar.vue'
	export default {
		components: {
			tabBar
		},
		data() {
			return {
				imgUrl:this.$base.uploadImgUrl,
				titleTop: 0,
				loading:true
			}

		},
		onLoad() {
			setTimeout(()=>{
				this.loading = false
			},800)
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top
		},
		methods: {
			back() {
				uni.navigateBack({
					delta: 1
				})
			}
		}
	}
</script>
<style>
	page {
		background: #FFFBF3;
	}
</style>
<style scoped lang="scss">
	@-webkit-keyframes countloading {
		0% {
			transform: rotate(180deg)
		}
	
		100% {
			transform: rotate(0deg)
		}
	}
	
	@keyframes countloading {
		0% {
			transform: rotate(180deg)
		}
	
		100% {
			transform: rotate(0deg)
		}
	}
	
	
	// .progress-active-loading{animation:progress-active 2.4s cubic-bezier(.23,1,.32,1) infinite;background-color:#fff;height:100%;position:absolute;width:100%;z-index:999}
	@-webkit-keyframes progress-active {
		0% {
			opacity: .1;
			transform: translateX(-100%) scaleX(0)
		}
	
		20% {
			opacity: .5;
			transform: translateX(-100%) scaleX(0)
		}
	
		100% {
			opacity: 0;
			transform: translateX(0) scaleX(1)
		}
	}
	
	@keyframes progress-active {
		0% {
			opacity: .1;
			transform: translateX(-100%) scaleX(0)
		}
	
		20% {
			opacity: .5;
			transform: translateX(-100%) scaleX(0)
		}
	
		100% {
			opacity: 0;
			transform: translateX(0) scaleX(1)
		}
	}
	
	
	.loading-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
	
		.loading-spinner {
			width: 50rpx;
			height: 50rpx;
			// border: 8rpx solid #c8a178;
			// border-top-color: transparent;
			// border-radius: 50%;
			animation: countloading 1s ease infinite;
			// animation:progress-active 2.4s cubic-bezier(.23,1,.32,1) infinite;
			background-image: url('https://pic.kefeichangduo.top/qdkbm/newimage/fhui/waiting.png');
			background-position: 50%;
			background-repeat: no-repeat;
			background-size: contain;
		}
	}
	
	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
	
		100% {
			transform: rotate(360deg);
		}
	}
	.guide-container {
		margin: 30rpx;

		.desc {
			color: #333;
			font-size: 28rpx;
			font-weight: 700;
			line-height: 44rpx;
			margin-bottom: 70rpx;
		}

		.boxstrap {
			background: #fff;
			padding: 30rpx;
			position:relative;
			border-radius: 20rpx;
			.icon{
				position: absolute;right:-20rpx;top:-130rpx;
				image{
					width:280rpx;height:280rpx;
				}
			}

			.item {
				margin-bottom: 40rpx;
				color: #333;
				font-size: 28rpx;
				line-height: 46rpx;

				.desc2 {
					margin-left: 20rpx;
				}
			}
		}

	}
</style>