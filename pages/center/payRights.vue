<template>
	<view class="content">
		<view class="head" style="background: none;" :style="'padding-top:' + (titleTop-10) + 'px'">
			<view class="header1">
				<view class="left" @tap="back">
					<view class="itemList">
						<view class="item">
							<button>
								<image style="width:20rpx;" :src="imgUrl+'/qdkbm/newimage/fhui/back.png'"></image>
							</button>
						</view>
					</view>
				</view>
				<view class="text" style="color:#333;">付费权益</view>
			</view>
		</view>
		<view class="loading-mask" v-if="loading">
			<view class="loading-spinner"></view>
		</view>
		<view class="bg" :style="{'background-image':'url('+imgUrl+'/qdkbm/newimage/fhui/member-bg.png)'}">
			<view class="qywrap" :style="{'padding-top':(titleTop2-15)+'px'}">
				<!-- 用户信息区域 -->
				<view class="qyInfo1">
					<view class="user-info">
						<image class="avatar" :src="imgUrl+'/qdkbm/newimage/fhui/user-img.png'"></image>
						<view class="user-details">
							<view class="name">
								{{userName?userName:''}}
								<image mode="widthFix" :src="imgUrl+'/qdkbm/newimage/fhui/icon-vip.png'"></image>
							</view>
							<view class="desc">查看您的付费权益</view>
						</view>
					</view>
					<!-- 权益兑换按钮 -->
					<view class="redemption-button" @tap="openRedemptionBox">
						<image :src="imgUrl+'/qdkbm/newimage/fhui/权益.png'"></image>
						<text>权益兑换</text>
					</view>
				</view>
				
				<view class="qyList">
					<!-- 规划报告卡片 -->
					<view class="item" :style="{'background-image':'url('+imgUrl+'/qdkbm/newimage/fhui/member-boxbg11.png)'}">
						<view class="box1">
							<view class="tit">规划报告</view>
							<!-- 体验版信息 -->
							<view class="version-block">
								<view class="version-header">
									<text class="version-name">体验版</text> 
								</view>
								<view class="qyitems">
									<view class="qyitem">
										<view class="text">可用/合计</view>
										<view class="num">
											{{qyInfo.trailLeftCount?qyInfo.trailLeftCount:0}}/{{qyInfo.trailCount?qyInfo.trailCount:0}}
											<text>次</text>
										</view>
									</view>
									<view class="qyitem">
										<view class="text">已使用</view>
										<view class="num">
											{{qyInfo.trailCount?Number(qyInfo.trailCount - qyInfo.trailLeftCount):0}}
											<text>次</text>
										</view>
									</view>
								</view>
							</view>

							<!-- 专业版信息 -->
							<view class="version-block">
								<view class="version-header">
									<text class="version-name">专业版</text> 
								</view>
								<view class="qyitems">
									<view class="qyitem">
										<view class="text">可用/合计</view>
										<view class="num">
											{{qyInfo.psLeftCount?qyInfo.psLeftCount:0}}/{{qyInfo.psCount?qyInfo.psCount:0}}
											<text>次</text>
										</view>
									</view>
									<view class="qyitem">
										<view class="text">已使用</view>
										<view class="num">
											{{qyInfo.psCount?Number(qyInfo.psCount - qyInfo.psLeftCount):0}}
											<text>次</text>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
					
					<!-- 会员卡片 -->
					<view class="item" :style="{'background-image':'url('+imgUrl+'/qdkbm/newimage/fhui/member-boxbg12.png)'}">
						<view class="box1">
							<view class="tit">会员<text>行业趋势及热门专业</text></view>
							<view class="version-block" v-if="!qyInfo.contentStartTime">
								<view class="version-header">
									<text class="version-name">今日免费</text> 
									<text class="version-time">获得时间: {{qyInfo.contentStartTime?qyInfo.contentStartTime:'-'}}</text>
								</view>
								<view class="qyitems">
									<view class="qyitem">
										<view class="text">可用/合计</view>
										<view class="num">
											{{qyInfo.contentLeftCount?qyInfo.contentLeftCount:0}}/{{qyInfo.contentCount?qyInfo.contentCount:0}}
											<text>次</text>
										</view>
									</view>
									<view class="qyitem">
										<view class="text">已使用</view>
										<view class="num">
											{{qyInfo.contentCount?Number(qyInfo.contentCount - qyInfo.contentLeftCount):0}}
											<text>次</text>
										</view>
									</view>
								</view>
							</view>
							<view class="membership-info">
								<view class="tit11">付费购买：<text>有效期至{{qyInfo.contentEndTime?qyInfo.contentEndTime:'-'}}</text></view>
								<view class="desc11">
									<text>剩余<text style="padding: 0 5rpx;background:none;margin:0;font-weight: 700;">{{day?day:0}}</text>天</text>
								</view>
							</view>
						</view>
					</view>
					
					<!-- 智能问答卡片 -->
					<view class="item" :style="{'background-image':'url('+imgUrl+'/qdkbm/newimage/fhui/member-boxbg13.png)'}">
						<view class="box1">
							<view class="tit">智能问答</view>
							<view class="qyitems">
								<view class="qyitem">
									<view class="text">可用/合计</view>
									<view class="num">
										{{qyInfo.askLeftCount?qyInfo.askLeftCount:0}}/{{qyInfo.askCount?qyInfo.askCount:0}}
										<text>次</text>
									</view>
								</view>
								<view class="qyitem">
									<view class="text">已使用</view>
									<view class="num">
										{{qyInfo.askCount?Number(qyInfo.askCount - qyInfo.askLeftCount):0}}
										<text>次</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<redemption-dialog :showbox="showRedemptionBox" @closebox="closeRedemptionBox" @submitRedemption="handleRedemption"></redemption-dialog>
	</view>
</template>

<script>
	import dayjs from 'dayjs'
	import relativeTime from 'dayjs/plugin/relativeTime'
	import redemptionDialog from '@/components/redemptionDialog.vue'
	dayjs.extend(relativeTime)
	export default {
		components: {
			redemptionDialog
		},
		data() {
			return {
				userId: "",
				loading:false,
				userName: "",
				day: 0,
				year: 0,
				qyInfo: {},
				imgUrl: this.$base.uploadImgUrl,
				titleTop: 0,
				titleTop2: 0,
				showRedemptionBox: false
			}

		},
		onLoad() {
			this.getmyrights()
			if (uni.getStorageSync('userId')) {
				this.userId = uni.getStorageSync('userId')
			}
			if (uni.getStorageSync('userName')) {
				this.userName = uni.getStorageSync('userName')
			}

		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top
			uni.createSelectorQuery().select('.head').boundingClientRect((res2) => {
				console.log(res2)
				this.titleTop2 = res2.height + this.titleTop // 将头部高度存储到titleTop2
			}).exec()
		},
		methods: {
			getmyrights() {
				this.loading = true
				this.$apis.qygetByUserId({
					userId: uni.getStorageSync('userId')
				}).then((res) => {
					if (res.code == 0) {
						this.qyInfo = res.data
						if (res.data && res.data.contentStartTime && res.data.contentEndTime) {
							const date1 = dayjs(res.data.contentStartTime).format(
								'YYYY/MM/DD HH:mm:ss');
							const date2 = dayjs(res.data.contentEndTime).format(
								'YYYY/MM/DD HH:mm:ss');
							let date3 = (new Date(date2)).getTime() - (new Date(
								date1)).getTime(); //时间差的毫秒数      
							let days = Math.floor(date3 / (24 * 3600 * 1000))
							console.log(dayjs(date2).diff(date1, 'month'))
							let years = Math.round((date3 / (24 * 3600 * 1000 * 365))*100) / 100
							let month = dayjs(date2).diff(date1,'month')
							console.log(month)
							this.day = days;
							this.year = Math.round(month/12 * 1000) / 1000;
							
							res.data.contentStartTime = dayjs(res.data.contentStartTime).format(
								'YYYY/MM/DD HH:mm:ss')
							res.data.contentEndTime = dayjs(res.data.contentEndTime).format(
								'YYYY/MM/DD HH:mm:ss')
						}
						if (res.data.trailUpdateTime) {
							res.data.trailUpdateTime = dayjs(res.data.trailUpdateTime).format(
								'YYYY/MM/DD HH:mm:ss')
						}
						if (res.data.psUpdateTime) {
							res.data.psUpdateTime = dayjs(res.data.psUpdateTime).format(
								'YYYY/MM/DD HH:mm:ss')
						}
						setTimeout(()=>{
							this.loading = false
						},600)
					}
				})
			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			},
			openRedemptionBox() {
				this.showRedemptionBox = true;
			},
			closeRedemptionBox(val) {
				this.showRedemptionBox = val;
			},
			handleRedemption(code) {
				// 处理兑换码逻辑
				console.log('兑换码:', code);
				
				// 显示加载提示
				uni.showLoading({
					title: '正在兑换...'
				});
				
				// 调用兑换接口
				this.$apis.redeemCode({
					code: code
				}).then(res => {
					uni.hideLoading();
					
					if (res.code == 0) {
						// 兑换成功
						uni.showToast({
							title: '兑换成功',
							icon: 'success'
						});
						
						// 刷新权益数据
						this.getmyrights();
					} else {
						// 兑换失败
						uni.showToast({
							title: res.msg || '兑换失败',
							icon: 'none'
						});
					}
					
					// 关闭弹窗
					this.closeRedemptionBox(false);
				}).catch(err => {
					uni.hideLoading();
					
					// 处理错误
					// uni.showToast({
					// 	title: '网络错误，请稍后再试',
					// 	icon: 'none'
					// });
					
					console.error('兑换码兑换失败:', err);
				});
			}
		}
	}
</script>

<style>
	page {
		background-color: #f5f5f5;
	}
</style>

<style scoped lang="scss">
	@-webkit-keyframes countloading {
		0% { transform: rotate(180deg) }
		100% { transform: rotate(0deg) }
	}
	
	@keyframes countloading {
		0% { transform: rotate(180deg) }
		100% { transform: rotate(0deg) }
	}
	
	@-webkit-keyframes progress-active {
		0% { opacity: .1; transform: translateX(-100%) scaleX(0) }
		20% { opacity: .5; transform: translateX(-100%) scaleX(0) }
		100% { opacity: 0; transform: translateX(0) scaleX(1) }
	}
	
	@keyframes progress-active {
		0% { opacity: .1; transform: translateX(-100%) scaleX(0) }
		20% { opacity: .5; transform: translateX(-100%) scaleX(0) }
		100% { opacity: 0; transform: translateX(0) scaleX(1) }
	}
	
	.loading-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
		
		.loading-spinner {
			width: 50rpx;
			height: 50rpx;
			animation: countloading 1s ease infinite;
			background-image: url('https://pic.kefeichangduo.top/qdkbm/newimage/fhui/waiting.png');
			background-position: 50%;
			background-repeat: no-repeat;
			background-size: contain;
		}
	}
	
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
	
	.qywrap {
		padding: 0 30rpx;
		padding-bottom: 40rpx;
		
		.qyInfo1 {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 20rpx;
			
			.user-info {
				display: flex;
				align-items: center;
				
				.avatar {
					width: 80rpx;
					height: 80rpx;
					margin-right: 15rpx;
				}
				
				.user-details {
					.name {
						font-size: 30rpx;
						font-weight: bold;
						margin-bottom: 5rpx;
						display: flex;
						align-items: center;
						
						image {
							width: 40rpx;
							margin-left: 10rpx;
						}
					}
					
					.desc {
						font-size: 24rpx;
						color: #666;
					}
				}
			}
		}
		
		.redemption-button {
			display: flex;
			align-items: center;
			background: linear-gradient(90deg, #FF8918, #FF5B03);
			border-radius: 30rpx;
			padding: 10rpx 20rpx;
			
			image {
				width: 30rpx;
				height: 30rpx;
				margin-right: 8rpx;
			}
			
			text {
				color: #fff;
				font-size: 24rpx;
			}
		}

		.qyList {
			.item {
				position: relative;
				background-repeat: no-repeat;
				background-size: 100% 100%;
				width: 100%;
				margin-bottom: 20rpx;
				border-radius: 15rpx;
				overflow: hidden;

				.box1 {
					padding: 25rpx 20rpx 20rpx 20rpx;
					position: relative;

					.tit {
						font-size: 30rpx;
						color: #209BFF;
						font-weight: 700;
						margin-bottom: 15rpx;
					}
					
					.version-block {
						margin-bottom: 15rpx;
						
						.version-header {
							display: flex;
							align-items: center;
							margin-bottom: 10rpx;
							
							.version-name {
								font-weight: 700;
								color: #000;
								font-size: 26rpx;
								margin-right: 15rpx;
							}
							
							.version-time {
								font-size: 22rpx;
								color: #666;
							}
						}
					}
					
					.membership-info {
						margin-top: 10rpx;
					}

					.tit11 {
						font-size: 28rpx;
						font-weight: 700;
						margin-bottom: 10rpx;
						display: flex;
						align-items: center;

						text {
							font-size: 22rpx;
							color: #666;
							margin-left: 15rpx;
							font-weight: 400;
						}
					}

					.desc11 {
						font-size: 22rpx;
						color: #666;
						display: flex;
						align-items: center;

						text {
							margin-right: 20rpx;
							border-radius: 80rpx;
							display: inline-flex;
							padding: 0 15rpx;
							height: 50rpx;
							line-height: 50rpx;
							background: #FF8918;
							color: #fff;
							font-size: 22rpx;
						}
					}

					.qyitems {
						display: flex;
						flex-direction: row;

						.qyitem {
							min-width: 250rpx;
							display: flex;
							margin-right: 15rpx;
							flex-direction: column;

							.text {
								color: #666;
								font-size: 22rpx;
								margin-bottom: 5rpx;
							}

							.num {
								font-size: 36rpx;
								font-weight: 700;
								color: #209BFF;

								text {
									font-size: 24rpx;
									margin-left: 10rpx;
								}
							}
						}
					}
				}
			}
		}
	}
</style>