<template>
	<view class="content">
		<view class="head" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="left" @tap="back">
					<view class="itemList">
						<view class="item">
							<button>
								<image style="width:20rpx;"
									:src="imgUrl+'/qdkbm/newimage/fhui/back-light.png'">
								</image>
							</button>
						</view>
					</view>
				</view>
				<view class="text">推广管理</view>
			</view>
		</view>
		<view class="loading-mask" v-if="loading">
			<view class="loading-spinner"></view>
		</view>
		<view class="intro">
			<view class="intros">
				<text>用户可申请成为推广员，获得用户消费的分润</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				imgUrl:this.$base.uploadImgUrl,
				loading:false,
				titleTop: 0
			}
		},
		onLoad() {
			this.loading = true
			setTimeout(()=>{
				this.loading = false
			},600)
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top
		},
		methods: {
			back() {
				uni.navigateBack({
					delta: 1
				})
			},
			handleContact() {
				wx.makePhoneCall({
					phoneNumber: '18133683362'
				});
			},

			// 处理复制微信号
			handleCopy(e) {
				const text = e.currentTarget.dataset.text;
				wx.setClipboardData({
					data: text,
					success: () => {
						wx.showToast({
							title: '微信号已复制',
							icon: 'success'
						});
					}
				});
			}
		}
	}
</script>
<style>
	page {
		background: #f5f5f5;
	}
</style>
<style scoped lang="scss">
	@-webkit-keyframes countloading {
		0% {
			transform: rotate(180deg)
		}
	
		100% {
			transform: rotate(0deg)
		}
	}
	
	@keyframes countloading {
		0% {
			transform: rotate(180deg)
		}
	
		100% {
			transform: rotate(0deg)
		}
	}
	
	
	// .progress-active-loading{animation:progress-active 2.4s cubic-bezier(.23,1,.32,1) infinite;background-color:#fff;height:100%;position:absolute;width:100%;z-index:999}
	@-webkit-keyframes progress-active {
		0% {
			opacity: .1;
			transform: translateX(-100%) scaleX(0)
		}
	
		20% {
			opacity: .5;
			transform: translateX(-100%) scaleX(0)
		}
	
		100% {
			opacity: 0;
			transform: translateX(0) scaleX(1)
		}
	}
	
	@keyframes progress-active {
		0% {
			opacity: .1;
			transform: translateX(-100%) scaleX(0)
		}
	
		20% {
			opacity: .5;
			transform: translateX(-100%) scaleX(0)
		}
	
		100% {
			opacity: 0;
			transform: translateX(0) scaleX(1)
		}
	}
	
	
	.loading-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
	
		.loading-spinner {
			width: 50rpx;
			height: 50rpx;
			// border: 8rpx solid #c8a178;
			// border-top-color: transparent;
			// border-radius: 50%;
			animation: countloading 1s ease infinite;
			// animation:progress-active 2.4s cubic-bezier(.23,1,.32,1) infinite;
			background-image: url('https://pic.kefeichangduo.top/qdkbm/newimage/fhui/waiting.png');
			background-position: 50%;
			background-repeat: no-repeat;
			background-size: contain;
		}
	}
	
	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
	
		100% {
			transform: rotate(360deg);
		}
	}
	.intro {
		background: #fff;
		border-radius: 16rpx;
		margin: 30rpx;

		.intros {
			padding: 50rpx 30rpx;
			font-size: 30rpx;
			font-weight: 700;
			color: #333;
		}
	}
</style>