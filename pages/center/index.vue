<template>
	<view class="content">
		<view class="head" style="background:none" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="left">
					<view class="itemList">
					</view>
				</view>
				<view class="text" style="color:#000">个人中心</view>
			</view>
		</view>
		<view class="bg" :style="{'background-image':'url('+imgUrl+'/qdkbm/newimage/fhui/member-bg.png)'}">
			<view class="memberPage" :style="{'padding-top':titleTop2+'px'}">
				<view class="member-info">
					<view class="pic">
						<image :src="imgUrl + '/qdkbm/newimage/fhui/user-img.png'"></image>
					</view>
					<view class="item-info">
						<view class="left">
							<view class="tit">
								{{userName?userName:'-'}}
								<!-- <text>个人主页</text> -->
							</view>
							<view class="desc">
								<view class="circle">ID</view> {{nickname?nickname:'-'}} ·
								{{myInfo.province?myInfo.province:'-'}}
							</view>
						</view>
						<view class="right">
							<button @tap="editAllInfo">
								<image :src="imgUrl+'/qdkbm/newimage/fhui/icon-edit.png'"></image>编辑
							</button>
						</view>
					</view>
				</view>
				<view class="member-card">
					<view class="member-card1">
						<view class="left">
							<view class="tit">我的成绩（{{myInfo.secondSubject?LabelFn(myInfo.secondSubject):'-'}}）</view>
							<view class="total">
								<text>{{myInfo.score?myInfo.score:'-'}}</text>分
							</view>
							<view class="nianji">
								<text>年级：{{myInfo.grade?myInfo.grade:'-'}}</text>
							</view>
						</view>
						<view class="right">
							<image :src="imgUrl+'/qdkbm/newimage/fhui/member-pic.png'"></image>
						</view>
					</view>
					<view class="member-card2">
						<view class="info1">


							<view class="left">
								当前位次 <view class="weici">
									<image :src="imgUrl+'/qdkbm/newimage/fhui/icon-zhu.png'"></image>{{ranking?ranking:"-"}}
								</view>
							</view>
							<view class="right">
								位次区间 <text>{{rankingRange?rankingRange:'-'}}</text>
							</view>
						</view>

						<view class="steps">
							<view class="line">
								<view style="width: 20%;" class="currentLine"></view>
							</view>
						</view>
					</view>

				</view>
				<view class="order-lists">
					<view class="item" v-for="(item,index) in settings" :key="index" @tap="handleSettingTap"
						:data-id="item.id">
						<view class="left">
							<image :src="imgUrl+'/qdkbm/newimage/fhui/member-icon'+Number(index+1)+'.png'"></image>
							{{item.name}}
						</view>
						<view class="right">
							<image :src="imgUrl+'/qdkbm/newimage/fhui/member-icon-right.png'"></image>
						</view>
					</view>

					<view class="item" v-if="token" @tap="signOut">
						<view class="left">
							<image :src="imgUrl+'/qdkbm/newimage/fhui/icon-signout.png'"></image>
							退出登录
						</view>
						<view class="right">
							<image :src="imgUrl+'/qdkbm/newimage/fhui/member-icon-right.png'"></image>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- <view class="container">
			<view class="user-card" v-if="isLogin">
				<view class="avatar-container" @tap="navigateToStudyInfo">
					<image class="avatar" :src="userInfo.avatar" mode="aspectFill"></image>
				</view>
				<view class="nickname-container" style="background-color:transparent">
					<text class="nickname">{{userInfo.nickname}}</text>
				</view>
				<view class="gender-container" style="background-color:transparent">
					<text class="gender">{{userInfo.gender}}</text>
				</view>
			</view>

			<view class="login-tip" v-else>
				<image class="avatar" src="https://pic.kefeichangduo.top/qdkbm/newimage/user-defaultpic.png"></image>
				<text class="tip-text">您还未登录，请先登录</text>
				<button class="login-btn" @tap="goToLogin">立即登录</button>
			</view>
			<view class="settings-section">
				<view class="settings-list">
					<block v-for="(item,index) in settings" :key="index">
						<view class="setting-item" @tap="handleSettingTap" :data-id="item.id">
							<view class="setting-label">{{item.name}}</view>
							<view class="setting-value">
								<image class="edit-icon" src="/static/img/arrow.png" mode="aspectFit"></image>
							</view>
						</view>
					</block>
				</view>
			</view>
		</view> -->
		<signout-dialog :showbox="showbox" @closebox="closebox" @signoutSystem="signoutSystem"></signout-dialog>
		<tabBar :current-page="3"></tabBar>
	</view>
</template>

<script>
	import signoutDialog from '@/components/signoutDialog.vue'
	import tabBar from '@/components/tabBar.vue'
	export default {
		components: {
			tabBar,
			signoutDialog
		},
		data() {
			return {
				showbox: false,
				myInfo: {
				grade: '高三',
				secondSubject: '物理'
			},
				token: "",
				weici: "-",
				qujian: "-",
				ranking: "-",
				rankingRange: "-",
				imgUrl: this.$base.uploadImgUrl,
				titleTop: 0,
				titleTop2: 0,
				isLogin: false,
				userInfo: {
					avatar: 'https://pic.kefeichangduo.top/qdkbm/newimage/user-defaultpic.png',
					nickname: '用户昵称'
				},
				settings: [{
						id: '1',
						name: '订单记录',
						needLogin: true,
						url: '/pages/center/orderRecord'
					},
					{
						id: '2',
						name: '使用须知',
						needLogin: false,
						url: '/pages/center/guide'
					},
					{
						id: '3',
						name: '官方客服',
						needLogin: false,
						url: '/pages/center/customer-service'
					},
					{
						id: '4',
						name: '付费权益',
						needLogin: true,
						url: '/pages/center/payRights'
					},
					// {
					// 	id: '5',
					// 	name: '推广管理',
					// 	needLogin: true,
					// 	url: '/pages/center/promotion'
					// }
				],
				userId: "",
				userName: "",
				nickname:""
			}

		},
		onLoad() {},
		onShow() {
			// 检查登录状态并获取token
			if (uni.getStorageSync('token')) {
				this.token = uni.getStorageSync('token')
				this.isLogin = true
				// 每次页面显示时都从接口获取最新数据
				this.getInfo()
			} else {
				this.isLogin = false
			}
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top
			uni.createSelectorQuery().select('.head').boundingClientRect((res2) => {
				console.log(res2)
				this.titleTop2 = res2.height + this.titleTop // 将头部高度存储到titleTop2
			}).exec()
		},
		methods: {
			closebox(val) {
				this.showbox = val
			},
			openbox() {
				this.showbox = true
			},
			signoutSystem() {
				let that = this
				uni.showToast({
					title: '退出成功',
					icon: 'success',
					duration: 800
				})
				setTimeout(() => {
					// 清除所有存储的用户信息
					uni.removeStorageSync('token')
					uni.removeStorageSync('userId')
					uni.removeStorageSync('userName')
					uni.removeStorageSync('nickname')
					uni.removeStorageSync('myInfo')
					uni.removeStorageSync('isVip')
					uni.removeStorageSync('askInfo')
					uni.removeStorageSync('tys')
					uni.removeStorageSync('openId')
					uni.removeStorageSync('tws')
					// 重置页面数据
					that.myInfo = {}
					that.token = ''
					that.userId = ''
					that.userName = ''
					that.nickname = ''
					that.ranking = '-'
					that.rankingRange = '-'
					that.userInfo = {}
					that.showbox = false
				}, 800);
			},

			signOut() {
				let that = this
				that.openbox()
			},
			LabelFn(val) {
				let str = ''
				if (Array.isArray(val)) {
					val.map((item) => {
						str += item.substring(0, 1)
					})
				} else if (typeof val === 'string') {
					// 如果是字符串，按逗号分割并取每个科目的第一个字符
					const subjects = val.split(',')
					subjects.forEach(subject => {
						if (subject && subject.trim()) {
							str += subject.trim().substring(0, 1)
						}
					})
				}
				return str
			},
			getInfo() {
				// 获取用户ID，仍然从storage中获取，因为这是用户标识
				if (uni.getStorageSync('userId')) {
					this.userId = uni.getStorageSync('userId')
				} else {
					console.log('未找到用户ID');
					return;
				}

				// 从API获取用户信息
				this.$apis.getUserInfo({
					id: this.userId
				}).then((res) => {
					if (res.code == 0 && res.data) {
						// 更新用户名称
						if (res.data.username) {
							this.userName = res.data.username
						}

						// 更新昵称
						if (res.data.nickname) {
							this.nickname = res.data.nickname
						}

						// 更新myInfo对象
						this.myInfo = {
							score: res.data.score || '-',
							province: res.data.province || '-',
							grade: res.data.grade || '高三',
							secondSubject: res.data.secondSubject || '物理',
							recommendedMajors: res.data.recommendedMajors || '',
							ranking: res.data.ranking || '-',
							rankingRange: res.data.rankingRange || '-'
						}

						// 更新排名信息
						if (res.data.ranking) {
							this.ranking = res.data.ranking
						}
						if (res.data.rankingRange) {
							this.rankingRange = res.data.rankingRange
						}

						console.log('从API获取的用户信息:', res.data);
					}
				}).catch(err => {
					console.error('获取用户信息失败:', err);
				});
			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			},
			chooseAvatar() {
				if (this.handleUnauthorized()) return;
				const that = this;
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: function(res) {
						const tempFilePath = res.tempFilePaths[0];
						const userInfo = that.userInfo;
						userInfo.avatar = tempFilePath;
						that.setData({
							userInfo: userInfo
						});
						uni.setStorageSync('userInfo', userInfo);
						uni.showToast({
							title: '头像已更新',
							icon: 'success',
							duration: 2000
						});
					}
				});
			},

			editAllInfo() {
				// if (this.handleUnauthorized()) return;
				let that = this
				// 确保myInfo中包含username
				that.myInfo.username = this.userName
				console.log(that.myInfo)
				if (that.token) {
					uni.navigateTo({
						url: '/pages/center/edit-profile?item=' + encodeURIComponent(JSON
							.stringify(that.myInfo))
					});
				} else {
					uni.showModal({
						title: '提示',
						content: '您还未登录，请先登录',
						confirmText: '去登录',
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/login/login'
								});
							}
						}
					});
				}
			},
			async handleSettingTap(e) {
				const id = e.currentTarget.dataset.id;
				const token = uni.getStorageSync('token')
				const setting = this.settings.find(item => item.id === id);
				if (setting && setting.url) {
					if (setting.needLogin) {
						if (token) {
							let myres = await this.$store.dispatch('gologinPage')
							console.log(myres)
							if (myres) {
								uni.navigateTo({
									url: setting.url
								});
							}
						} else {
							// 尝试自动登录
							let loginRes = await this.$store.dispatch('gologinPage')
							if (loginRes) {
								// 自动登录成功，跳转到目标页面
								uni.navigateTo({
									url: setting.url
								});
							} else {
								// 自动登录失败，不做任何处理
							}
						}
					} else {
						uni.navigateTo({
							url: setting.url
						});
					}

				}
			},
			checkLoginStatus() {
				let token = uni.getStorageSync('token');
				if (token) {
					this.isLogin = true
					this.getInfo()
				} else {
					this.isLogin = false
				}
			},
			async handleUnauthorized() {
				if (!this.isLogin) {
					// 尝试自动登录
					let loginRes = await this.$store.dispatch('gologinPage')
					if (loginRes) {
						// 自动登录成功
						this.isLogin = true
						this.getInfo()
						return false
					} else {
						// 自动登录失败，不做任何处理
						return true
					}
				}
				return false;
			},
			goToLogin() {
				uni.navigateTo({
					url: '/pages/login/login'
				});
			},
			navigateToStudyInfo() {
				let that = this
				console.log(that.myInfo)
				uni.navigateTo({
					url: '/pages/center/edit-profile?item=' + encodeURIComponent(JSON
						.stringify(that.myInfo))
				});
			}
		}
	}
</script>
<style>
	page {
		background: #fff;
	}
</style>
<style scoped lang="scss">
	.order-lists {
		background: #fff;
		border-radius: 24rpx;
		box-shadow: 0 8rpx 35rpx 6rpx rgba(0, 0, 0, 0.06);
		padding: 20rpx 30rpx;

		.item {
			padding: 25rpx 0;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-between;

			.left {
				display: flex;
				flex-direction: row;
				align-items: center;
				font-size: 28rpx;
				color: #000;

				image {
					width: 48rpx;
					height: 48rpx;
					margin-right: 15rpx;
				}
			}

			.right {
				image {
					width: 24rpx;
					height: 24rpx;
				}
			}
		}
	}

	.member-card {
		border-radius: 24rpx;
		margin: 20rpx 0;
		background-image: linear-gradient(180deg, #FFF4E6 0, #FFDBB3 100%);

		.member-card2 {
			background: #FFFAF5;
			border-radius: 0 0 24rpx 24rpx;
			padding: 0 40rpx;

			.steps {
				padding-bottom: 35rpx;

				.line {
					height: 6rpx;
					background: #FFE1B9;
					border-radius: 8rpx;
					position: relative;

					.currentLine {
						position: absolute;
						left: 0;
						top: 0;
						width: 100%;
						height: 6rpx;
						border-radius: 8rpx;
						background: #FF8918;
					}
				}
			}

			.info1 {
				padding: 30rpx 0 15rpx 0;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: space-between;

				.left {
					font-size: 24rpx;
					color: #666;
					display: flex;
					flex-direction: row;
					align-items: center;

					.weici {
						display: flex;
						flex-direction: row;
						align-items: center;
						margin-left: 10rpx;
						border-radius: 40rpx;
						padding: 0 20rpx;
						height: 40rpx;
						line-height: 40rpx;
						background: #FF8918;
						color: #fff;
						font-size: 24rpx;

						image {
							width: 24rpx;
							height: 24rpx;
							margin-right: 10rpx;
						}
					}
				}

				.right {
					color: #666;
					font-size: 24rpx;

					text {
						margin-left: 10rpx;
						font-size: 24rpx;
						color: #000;
						font-weight: 700;
					}
				}
			}

		}

		.member-card1 {
			overflow: hidden;
			padding: 20rpx 40rpx 25rpx 40rpx;
			display: flex;
			flex-direction: row;
			align-items: flex-end;
			justify-content: space-between;

			.left {
				.tit {
					font-size: 28rpx;
					color: #FF8918;
					margin-bottom: 10rpx;
				}

				.total {
					margin-bottom: 0rpx;
					font-size: 32rpx;
					color: #FF8918;
					font-weight: 700;

					text {
						font-size: 56rpx;
						margin-right: 20rpx;
					}
				}

				.nianji {
					text {
						display: inline-flex;
						color: #fff;
						font-size: 24rpx;
						padding: 0 20rpx;
						height: 40rpx;
						line-height: 40rpx;
						background-image: linear-gradient(180deg, #FF8510 0, #FFBD73 100%);
						border-radius: 40rpx;
					}
				}
			}

			.right {
				margin-bottom: -65rpx;

				image {
					width: 260rpx;
					height: 230rpx;
				}
			}
		}

	}

	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		position: relative;

		.bg {
			position: absolute;
			left: 0;
			top: 0;
			background-repeat: no-repeat;
			background-size: 100% auto;
			width: 100%;
			height: 100vh;

			.memberPage {
				padding: 0 40rpx;
				padding-bottom: 150rpx;

				.member-info {
					display: flex;
					flex-direction: row;
					align-items: center;

					.pic {
						margin-right: 20rpx;

						image {
							width: 160rpx;
							height: 160rpx;
							border-radius: 100%;
						}
					}

					.item-info {
						flex: 1;
						display: flex;
						justify-content: space-between;
						align-items: center;

						.left {
							.tit {
								margin-bottom: 20rpx;
								font-weight: 700;
								font-size: 32rpx;
								color: #000;
								display: flex;
								align-items: center;

								text {
									font-weight: 400;
									color: #FF8918;
									font-size: 28rpx;
									margin-left: 20rpx;
								}
							}

							.desc {
								color: #656565;
								font-size: 30rpx;
								display: flex;
								flex-direction: row;
								align-items: center;

								.circle {
									width: 36rpx;
									height: 36rpx;
									background: #FF8B1B;
									border-radius: 100%;
									font-size: 24rpx;
									margin-right: 15rpx;
									color: #fff;
									line-height: 36rpx;
									display: flex;
									align-items: center;
									justify-content: center;
								}
							}
						}

						.right {
							button {
								background-image: linear-gradient(180deg, #FF8510 0, #FFBD73 100%);
								padding: 0 25rpx;
								display: flex;
								align-items: center;
								justify-content: center;

								height: 64rpx;
								line-height: 64rpx;
								color: #fff;
								font-size: 28rpx;
								border-radius: 80rpx;

								image {
									width: 32rpx;
									height: 32rpx;
									margin-right: 10rpx;
								}

								&::after {
									border: none;
								}
							}
						}
					}
				}
			}
		}
	}

	.logo {
		height: 200rpx;
		width: 200rpx;
		margin-top: 200rpx;
		margin-left: auto;
		margin-right: auto;
		margin-bottom: 50rpx;
	}

	.text-area {
		display: flex;
		justify-content: center;
	}

	.title {
		font-size: 36rpx;
		color: #8f8f94;
	}

	.container {
		width: 100%;
	}

	/* 用户信息卡片 */
	.user-card {
		background-color: #FF8B1B;
		padding: 40rpx 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		color: #fff;
		box-shadow: 0 4rpx 12rpx rgba(77, 107, 254, 0.3);
	}

	.avatar-container {
		position: relative;
		margin-bottom: 20rpx;
	}

	.avatar {
		width: 150rpx;
		height: 150rpx;
		border-radius: 50%;
		border: 4rpx solid #fff;
	}

	.avatar-edit {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: rgba(0, 0, 0, 0.5);
		color: #fff;
		font-size: 22rpx;
		text-align: center;
		padding: 6rpx 0;
		border-bottom-left-radius: 75rpx;
		border-bottom-right-radius: 75rpx;
	}

	.nickname-container,
	.gender-container {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
		padding: 10rpx 20rpx;
		background-color: rgba(255, 255, 255, 0.2);
		border-radius: 30rpx;
	}

	.nickname {
		font-size: 36rpx;
		font-weight: bold;
		margin-right: 10rpx;
	}

	.gender {
		font-size: 28rpx;
		margin-right: 10rpx;
	}

	.edit-icon.small {
		width: 24rpx;
		height: 24rpx;
		margin-left: 10rpx;
	}

	/* 常用工具部分 */
	.tools-section {
		margin: 30rpx 20rpx;
		background-color: #fff;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.tools-grid {
		display: flex;
		flex-wrap: wrap;
		padding: 20rpx;
	}

	.tool-item {
		width: 33.33%;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 20rpx 0;
	}

	.tool-icon {
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 10rpx;
	}

	.tool-name {
		font-size: 26rpx;
		color: #333;
	}

	/* 工具类咨询部分 */
	.consult-section {
		margin: 30rpx 20rpx;
		background-color: #fff;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.more-link {
		font-size: 26rpx;
		color: #4d6bfe;
	}

	.consult-list {
		padding: 0 30rpx;
	}

	.consult-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.consult-item:last-child {
		border-bottom: none;
	}

	.consult-content {
		flex: 1;
		margin-right: 20rpx;
	}

	.consult-title {
		font-size: 30rpx;
		color: #333;
		font-weight: 500;
		margin-bottom: 10rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}

	.consult-desc {
		font-size: 26rpx;
		color: #999;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	.consult-tag {
		font-size: 22rpx;
		color: #4d6bfe;
		padding: 6rpx 16rpx;
		background-color: rgba(77, 107, 254, 0.1);
		border-radius: 20rpx;
		white-space: nowrap;
	}

	/* 使用统计部分 */
	.stats-section {
		margin: 30rpx 20rpx;
		background-color: #fff;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.stats-content {
		display: flex;
		justify-content: space-around;
		padding: 30rpx 0;
	}

	.stats-item {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.stats-value {
		font-size: 40rpx;
		color: #4d6bfe;
		font-weight: bold;
		margin-bottom: 10rpx;
	}

	.stats-label {
		font-size: 26rpx;
		color: #999;
	}

	/* 个人信息部分 */
	.info-section,
	.settings-section {
		margin: 30rpx 20rpx;
		background-color: #fff;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.section-title {
		font-size: 32rpx;
		font-weight: bold;
	}

	.edit-all {
		font-size: 26rpx;
		color: #4d6bfe;
	}

	.info-list,
	.settings-list {
		padding: 0 30rpx;
	}

	.info-item,
	.setting-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.info-item:last-child,
	.setting-item:last-child {
		border-bottom: none;
	}

	.info-label,
	.setting-label {
		color: #333;
		font-size: 30rpx;
	}

	.info-value,
	.setting-value {
		display: flex;
		align-items: center;
		color: #666;
		font-size: 30rpx;
	}

	.edit-icon {
		width: 32rpx;
		height: 32rpx;
		margin-left: 20rpx;
	}

	/* 设置按钮 */
	.setting-button {
		color: #4d6bfe;
		font-size: 28rpx;
		padding: 6rpx 20rpx;
		border: 1rpx solid #4d6bfe;
		border-radius: 30rpx;
	}

	/* 编辑弹窗 */
	.edit-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 1000;
	}

	.modal-mask {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
	}

	.modal-content {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #fff;
		border-top-left-radius: 20rpx;
		border-top-right-radius: 20rpx;
		overflow: hidden;
	}

	.modal-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.close-btn {
		font-size: 40rpx;
		color: #999;
		padding: 0 20rpx;
	}

	.modal-body {
		padding: 30rpx;
	}

	.edit-input {
		width: 100%;
		height: 80rpx;
		border: 1rpx solid #ddd;
		border-radius: 8rpx;
		padding: 0 20rpx;
		font-size: 30rpx;
	}

	.modal-footer {
		display: flex;
		padding: 20rpx 30rpx 40rpx;
	}

	.cancel-btn,
	.save-btn {
		flex: 1;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 40rpx;
		font-size: 30rpx;
		margin: 0 10rpx;
	}

	.cancel-btn {
		background-color: #f5f5f5;
		color: #666;
	}

	.save-btn {
		background-color: #4d6bfe;
		color: #fff;
	}

	/* 性别选择器 */
	.gender-options {
		display: flex;
		flex-direction: column;
		width: 100%;
	}

	.gender-option {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.gender-option:last-child {
		border-bottom: none;
	}

	.gender-option.selected {
		color: #4d6bfe;
	}

	.gender-check {
		width: 36rpx;
		height: 36rpx;
		border-radius: 50%;
		background-color: #4d6bfe;
		position: relative;
	}

	.gender-check::after {
		content: '';
		position: absolute;
		top: 10rpx;
		left: 12rpx;
		width: 12rpx;
		height: 6rpx;
		border-left: 4rpx solid #fff;
		border-bottom: 4rpx solid #fff;
		transform: rotate(-45deg);
	}

	/* 常见问题入口 */
	.faq-entrance {
		margin: 30rpx 20rpx;
		background-color: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.faq-entrance-content {
		display: flex;
		align-items: center;
	}

	.faq-entrance-icon {
		width: 80rpx;
		height: 80rpx;
		margin-right: 20rpx;
		background-color: rgba(77, 107, 254, 0.1);
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.faq-entrance-icon image {
		width: 50rpx;
		height: 50rpx;
	}

	.faq-entrance-text {
		display: flex;
		flex-direction: column;
	}

	.faq-entrance-title {
		font-size: 32rpx;
		color: #333;
		font-weight: 500;
		margin-bottom: 8rpx;
	}

	.faq-entrance-desc {
		font-size: 26rpx;
		color: #999;
	}

	.faq-entrance-arrow {
		width: 40rpx;
		height: 40rpx;
	}

	.faq-entrance-arrow image {
		width: 24rpx;
		height: 24rpx;
	}

	.login-tip {
		text-align: center;
		padding: 40rpx;
	}

	.tip-text {
		font-size: 28rpx;
		color: #666;
		display: block;
		margin: 20rpx 0;
	}

	.login-btn {
		background-color: #fe7f39;
		color: #fff;
		font-size: 28rpx;
		padding: 0;
		border-radius: 50rpx;
		width: 60%;
		height: 80rpx;
		line-height: 80rpx;
	}
</style>