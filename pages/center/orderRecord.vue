<template>
	
	<view class="content">
		<view class="head" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="left" @tap="back">
					<view class="itemList">
						<view class="item">
							<button>
								<image style="width:20rpx;"
									:src="imgUrl+'/qdkbm/newimage/fhui/back-light.png'">
								</image>
							</button>
						</view>
					</view>
				</view>
				<view class="text">订单列表</view>
			</view>
		</view>
		<mescroll-body :sticky="true" @init="initCallback" ref="mescrollRef" :down="downOption" :up="upOption"
					@down="downCallback" @up="upCallback">
		<view class="container">
			<view class="loading-mask" v-if="loading">
				<view class="loading-spinner"></view>
			</view>
			
			<!-- 订单列表 -->
			<view class="order-list">
				<block v-for="(item,index) in orderList" :key="index">
					<view class="order-item" :data-id="item.orderId">
						<view class="icon">
							<image :src="imgUrl+'/qdkbm/newimage/fhui/icon-weixin2.png'"></image>
						</view>
						<view class="myinfo">
							<view class="left">
								{{item.name}}
							</view>
							<view class="right">
								<view class="money">¥<text>{{item.price}}</text></view>
								<view class="date">{{item.time}}</view>
							</view>
						</view>
						<!-- <view class="order-content">
							<text class="order-title">{{item.title}}</text>
							<text class="order-price">￥{{item.price}}</text>
						</view>
						<view class="order-footer">
							<text class="order-time">{{item.createTime}}</text>
							<button v-if="item.status === 'pending'" style="margin:0;width:180rpx;font-size:24rpx"
								class="pay-btn" @tap="handlePay" :data-id="item.orderId">立即支付</button>
						</view> -->
					</view>
				</block>
			</view>
			<!-- 无订单提示 -->
			<!-- <view v-if="orderList.length === 0" class="empty-tip">
				<text>暂无订单记录</text>
			</view> -->
		</view>

	</mescroll-body>
	</view>

</template>
<script>
	import dayjs from 'dayjs'
	import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin],
		components: {
		},
		data() {
			return {
				mescroll: null,
				downOption: {
					use: false,
					auto: true,
				},
				upOption: {
					onScroll: true,
					use: true,
					auto: true,
					page: {
						num: 0,
						size: 10
					},
					noMoreSize: 10,
					empty: {
						tip: '暂无订单记录'
					}
				},
				imgUrl:this.$base.uploadImgUrl,
				titleTop: 0,
				loading:false,
				orderList: []
			}

		},
		onLoad() {
			// this.loadOrderList()
			
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top
		},
		methods: {
			initCallback(ele) {
				if (this.mescroll === null) this.mescroll = ele
			},
			downCallback(e) {
				this.mescroll.resetUpScroll();
			},
			upCallback(page) {
				let that = this
				var pageNum = page.num;
				var pageSize = page.size;
				
				// uni.showLoading({
				// 	title:'正在加载中...',
				// 	mask:true
				// })
				that.loading = true
				that.$apis.getOrder({
					pageNo:pageNum,
					pageSize:pageSize,
					status:10
				}).then((res) => {
					that.loading = false
					if (res.code == 0) {
						let arr = res.data.list;
						if(arr.length>0){
							arr.map((item)=>{
								if(item.items.length>0){
									item.name = item.items[0].spuName
									item.price = (Math.round ((item.items[0].payPrice / 100) * 100)) / 100
									item.time = dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss')
								}
							})
						}
						if (page.num === 1) that.orderList = [];
						that.orderList =  that.orderList.concat(arr);
						that.mescroll.endBySize(arr.length, res.data.total);
						
					} else {
						that.mescroll.endErr();
						// uni.hideLoading();
					}
				}).catch((err) => {
					that.mescroll.endErr();
					that.loading = false
					// uni.hideLoading();
				})
			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			},
			// 加载订单列表
			loadOrderList() {
				this.loading = true
				
				this.$apis.getOrder({
					id:uni.getStorageSync('userId'),
					pageNo:1,
					pageSize:100,
					sync:false
				}).then((res)=>{
					if(res.code == 0){
						res.data.list.map((item)=>{
							if(item.items.length>0){
								item.name = item.items[0].spuName
								item.price = (Math.round ((item.items[0].payPrice / 100) * 100)) / 100
								item.time = dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss')
							}
						})
						this.orderList = res.data.list
						setTimeout(()=>{
							this.loading = false
						},800)
					}
				})
				
			},
			// 处理支付
			handlePay(e) {
				const orderId = e.currentTarget.dataset.id;
				wx.showLoading({
					title: '支付中...'
				});
				// 这里可以添加实际的支付逻辑
				setTimeout(() => {
					wx.hideLoading();
					wx.showToast({
						title: '支付成功',
						icon: 'success'
					});
					this.loadOrderList();
				}, 2000);
			}
		}
	}
</script>
<style>
	page {
		background: #f5f5f5;
	}
</style>
<style scoped lang="scss">
	@-webkit-keyframes countloading {
		0% {
			transform: rotate(180deg)
		}
	
		100% {
			transform: rotate(0deg)
		}
	}
	
	@keyframes countloading {
		0% {
			transform: rotate(180deg)
		}
	
		100% {
			transform: rotate(0deg)
		}
	}
	
	
	// .progress-active-loading{animation:progress-active 2.4s cubic-bezier(.23,1,.32,1) infinite;background-color:#fff;height:100%;position:absolute;width:100%;z-index:999}
	@-webkit-keyframes progress-active {
		0% {
			opacity: .1;
			transform: translateX(-100%) scaleX(0)
		}
	
		20% {
			opacity: .5;
			transform: translateX(-100%) scaleX(0)
		}
	
		100% {
			opacity: 0;
			transform: translateX(0) scaleX(1)
		}
	}
	
	@keyframes progress-active {
		0% {
			opacity: .1;
			transform: translateX(-100%) scaleX(0)
		}
	
		20% {
			opacity: .5;
			transform: translateX(-100%) scaleX(0)
		}
	
		100% {
			opacity: 0;
			transform: translateX(0) scaleX(1)
		}
	}
	
	
	.loading-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
	
		.loading-spinner {
			width: 50rpx;
			height: 50rpx;
			// border: 8rpx solid #c8a178;
			// border-top-color: transparent;
			// border-radius: 50%;
			animation: countloading 1s ease infinite;
			// animation:progress-active 2.4s cubic-bezier(.23,1,.32,1) infinite;
			background-image: url('https://pic.kefeichangduo.top/qdkbm/newimage/fhui/waiting.png');
			background-position: 50%;
			background-repeat: no-repeat;
			background-size: contain;
		}
	}
	
	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
	
		100% {
			transform: rotate(360deg);
		}
	}
	.container {
	}
	page {
		background: #f5f5f5;
	}
	.order-list {
		margin: 30rpx;
	}
	.order-item {
		background: #fff;
		box-shadow: 0 0 12rpx rgba(0, 0, 0, 0.1);
		border-radius: 16rpx;
		padding: 25rpx;
		display: flex;flex-direction: row;
		align-items: center;
		margin-bottom: 25rpx;
		.icon{
			image{
				width:72rpx;height:72rpx;
			}
		}
		.myinfo{margin-left:20rpx;
			flex:1;display: flex;flex-direction: row;justify-content: space-between;align-items: center;
			.left{
				font-size: 28rpx;color:#000;font-weight: 700;
			}
			.right{text-align: right;min-width:240rpx;max-width:300rpx;
				display: flex;align-items: flex-end;justify-content: flex-end;flex-direction: column;
				.money{
					color:#FF8918;font-size:28rpx;margin-bottom: 6rpx;font-weight: 700;white-space: nowrap;
					text{
						font-size:40rpx;margin-left:10rpx;
					}
				}
				.date{color:#999;font-size: 24rpx;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 100%;}
			}
		}
	}
	.order-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.order-id {
		font-size: 28rpx;
		color: #666;
	}

	.order-status {
		font-size: 28rpx;
	}

	.order-status.completed {
		color: #67C23A;
	}

	.order-status.pending {
		color: #E6A23C;
	}

	.order-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.order-title {
		font-size: 32rpx;
		font-weight: 700;
		color: #333;
	}

	.order-price {
		font-size: 32rpx;
		color: #F56C6C;
	}

	.order-footer {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.order-time {
		font-size: 26rpx;
		color: #999;
	}

	.pay-btn {
		font-size: 28rpx;
		color: #fff;
		margin: 0;
		background: #4d6bfe;
		border-radius: 50rpx;
		padding: 0 30rpx;
		height: 60rpx;
		line-height: 60rpx;
	}

	.empty-tip {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		margin-top: 200rpx;
	}

	.empty-tip image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 40rpx;
	}

	.empty-tip text {
		font-size: 28rpx;
		color: #999;
	}
</style>