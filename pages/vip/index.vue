<template>
	<view class="vip-container">
		<view class="head" style="background: none;" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="left" @tap="back">
					<view class="itemList">
						<view class="item">
							<button>
								<image style="width:20rpx;"
									src="https://pic.kefeichangduo.top/qdkbm/newimage/fhui/back.png">
								</image>
							</button>
						</view>
					</view>
				</view>
				<view class="text" style="color:#f5f5f5">1</view>
			</view>
		</view>
		<view class="user-info" :style="{'background-image':'url('+imgUrl+'/qdkbm/newimage/fhui/member-head.png)'}">
			<image class="avatar" :src="userInfo.avatar" mode="aspectFill"></image>
			<view class="info">
				<view class="id">ID: {{userInfo.id}}
					<image mode="widthFix" :src="imgUrl+'/qdkbm/newimage/fhui/icon-vip.png'"></image>
				</view>
				<text class="promo">开通会员，即可解锁权益👀</text>
			</view>
		</view>
		<view class="mycontents">
			<view class="title">加入飞鸿VIP</view>

			<view class="plans">
				<!-- 第一个套餐左右排版 -->
				<view class="plan-item" :class="{'selected': selectedPlan === 0}" @tap="selectPlan(0,plans[0])">
					<view class="plan-content">
						<view class="left">
							<view class="plan-title">{{plans[0].title}}</view>
							<view class="plan-price">
								<text class="currency">¥</text>
								<text class="amount">{{plans[0].price}}</text>
							</view>
						</view>
						<view class="right" style="margin-left:50rpx;">
							<view class="plan-duration" style="font-size:40rpx">{{plans[0].duration}}</view>
						</view>
					</view>
				</view>

				<!-- 其他三个套餐 -->
				<view class="other-plans">
					<view v-for="(plan, index) in plans.slice(1)" :key="index" class="plan-item"
						:class="{'selected': selectedPlan === index + 1}" @tap="selectPlan(index + 1,plan)">
						<view class="plan-content">
							<view class="left">
								<view class="plan-title">{{plan.title}}</view>
								<view class="plan-duration">{{plan.duration}}</view>
							</view>
							<view class="right">
								<view class="plan-price">
									<text class="currency">¥</text>
									<text class="amount">{{plan.price}}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="bottom-bar">
			<view class="total-price">
				<text class="label">合计：</text>
				<text class="currency">¥</text>
				<text class="amount">{{selectedPlanPrice}}</text>
			</view>
			<button class="pay-btn" :disabled="disabled" @tap="handlePay">支付</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				userId: "",
				disabled: false,
				userName: "",
				imgUrl: this.$base.uploadImgUrl,
				titleTop: 0,
				userInfo: {
					avatar: 'https://pic.kefeichangduo.top/qdkbm/newimage/fhui/user-img.png',
					id: ''
				},
				selectedPlan: 0,
				orderId: "",
				openId: "",
				skuId: "",
				plans: [{
						title: '',
						skuId: 32,
						price: 50,
						duration: '1个月'
					},
					{
						title: '',
						skuId: 34,
						price: 100,
						duration: '100天'
					},
					{
						title: '',
						price: 180,
						skuId: 35,
						duration: '1年'
					},
					{
						title: '',
						price: 360,
						skuId: 36,
						duration: '3年'
					}
				]
			}
		},
		onLoad() {
			this.skuId = this.plans[0].skuId
		},
		onShow() {
			if (uni.getStorageSync('userName')) {
				this.userInfo.id = uni.getStorageSync('userName')
			}
			if (uni.getStorageSync('openId')) {
				this.openId = uni.getStorageSync('openId')
			}


		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top
		},
		computed: {
			selectedPlanPrice() {
				let price = 0
				price = this.plans[this.selectedPlan].price
				return price


			}
		},
		methods: {
			selectPlan(index, item) {
				this.selectedPlan = index;
				this.skuId = item.skuId
			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			},
			wechatPay() {
				this.$apis.submitOrder({
					id: this.orderId,
					channelCode: "wx_pub",
					channelExtras: {
						openid: this.openId
					},
					displayMode: "url",
					returnUrl: ""
				}).then((res) => {
					if (res.code == 0) {
						let obj = JSON.parse(res.data.displayContent)
						this.wepay(obj)
					} else {
						this.disabled = false
					}
				}).catch((err) => {
					this.disabled = false
				})
			},
			wepay(obj) {
				let that = this
				uni.requestPayment({
					appId: obj.appId,
					timeStamp: obj.timeStamp,
					nonceStr: obj.nonceStr,
					package: obj.packageValue,
					signType: obj.signType,
					paySign: obj.paySign,
					success(res) {
						// 显示支付成功提示
						uni.showToast({
							title: '支付成功',
							icon: 'success',
							duration: 800
						});
						setTimeout(() => {
							uni.navigateBack({
								delta: 1
							})
							that.disabled = false
						}, 1000)
					},
					fail(res) {
						that.disabled = false
						uni.showToast({
							title: '支付失败',
							icon: 'none',
							duration: 300
						})
					}
				});
			},
			handlePay() {
				// 处理支付逻辑
				this.disabled = true
				console.log('支付金额：', this.selectedPlanPrice);
				// 这里可以调用支付接口
				if (this.selectedPlanPrice == 0) {
					uni.showToast({
						title: "请选择套餐类型",
						icon: 'none'
					})
					this.disabled = false
				} else {
					this.$apis.createOrder({
						items: [{
							skuId: this.skuId,
							count: 1,
							cartId: null
						}],
						couponId: undefined,
						pointStatus: false,
						deliveryType: 2,
						addressId: undefined,
						pickUpStoreId: undefined,
						receiverName: "",
						receiverMobile: "",
						seckillActivityId: undefined,
						combinationActivityId: undefined,
						combinationHeadId: undefined,
						bargainRecordId: undefined,
						pointActivityId: undefined,
						remark: ""
					}).then((res) => {
						if (res.code == 0) {
							this.orderId = res.data.payOrderId
							this.wechatPay()
						} else {
							this.disabled = false
						}
					}).catch((err) => {
						this.disabled = false
					})
				}




			}
		}
	}
</script>
<style>
	page {
		background-color: #FFFBF3;
	}
</style>
<style lang="scss">
	.vip-container {

		.mycontents {
			background: #fff;
			border-radius: 48rpx 48rpx 0 0;
			padding: 0 20rpx;
			padding-top: 10rpx;
			margin-top: -10rpx;
		}

		.user-info {
			display: flex;
			flex-direction: column;
			margin: 0 60rpx;
			padding: 30rpx 40rpx;
			background-repeat: no-repeat;
			background-size: 100% 100%;
			border-radius: 24rpx 24rpx 0 0;
			margin-top: 20rpx;

			.avatar {
				width: 130rpx;
				height: 130rpx;
				border-radius: 50%;
				margin-bottom: 10rpx;
			}

			.info {
				display: flex;
				flex-direction: column;
				align-items: flex-start;

				.id {
					font-size: 32rpx;
					font-weight: 700;
					color: #333;
					margin-bottom: 5rpx;

					image {
						width: 50rpx;
						margin-left: 20rpx;
					}
				}

				.promo {
					font-size: 24rpx;
					color: #666;
				}
			}
		}

		.title {
			font-size: 32rpx;
			font-weight: 700;
			color: #000;
			text-align: left;
			margin: 30rpx;
			margin-bottom: 40rpx;
		}

		.plans {
			display: flex;
			margin: 0 25rpx;
			flex-direction: column;
			gap: 25rpx;
			margin-bottom: 100rpx;

			.plan-item {
				background-color: rgba(255, 137, 24, 0.05);
				border-radius: 24rpx;
				padding: 70rpx 30rpx;
				position: relative;
				transition: all 0.3s ease;
				border: 4rpx solid #ECD8C8;

				&.selected {
					border-color: rgba(255, 137, 24, 1);

					background-image: linear-gradient(180deg, rgba(255, 215, 133, 0.30) 0, rgba(255, 201, 93, 0.20) 100%);
					box-shadow: 0 4rpx 12rpx rgba(255, 85, 0, 0.1);
					transform: translateY(-2rpx);

					.plan-price {
						color: #ff7e17;
						transform: scale(1.05);
					}
				}

				.plan-content {
					display: flex;
					align-items: center;
					// justify-content: space-between;
					transition: all 0.3s ease;

					.left {
						text-align: left;

						.plan-title {
							font-size: 28rpx;
							color: #333;
							margin-bottom: 10rpx;
						}

						.plan-price {
							font-weight: 700;
							color: #FF8918;
							transition: all 0.3s ease;
							display: flex;
							align-items: baseline;

							.currency {
								font-size: 36rpx;
								margin-right: 4rpx;
							}

							.amount {
								font-size: 60rpx;
							}
						}
					}

					.right {
						text-align: right;

						.plan-duration {
							font-size: 36rpx;
							font-weight: 700;
							color: #333;
						}
					}
				}
			}

			.other-plans {
				display: grid;
				grid-template-columns: repeat(3, 1fr);
				gap: 20rpx;

				.plan-item {
					.plan-content {
						flex-direction: column;
						text-align: center;

						.left {
							text-align: center;
							margin-bottom: 10rpx;

							.plan-duration {
								font-size: 32rpx;
								font-weight: 700;
								color: #333;
							}

							.plan-title {
								font-size: 28rpx;
								color: #333;
								margin-bottom: 10rpx;
							}

							.plan-price {
								font-weight: 700;
								color: #FF8918;
								transition: all 0.3s ease;
								display: flex;
								align-items: baseline;
								justify-content: center;

								.currency {
									font-size: 36rpx;
									margin-right: 4rpx;
								}

								.amount {
									font-size: 60rpx;
								}
							}
						}

						.right {
							text-align: center;

							.plan-duration {
								font-size: 32rpx;
								font-weight: 700;
								color: #333;
							}

							.plan-price {
								font-weight: 700;
								color: #FF8918;
								transition: all 0.3s ease;
								display: flex;
								align-items: baseline;

								.currency {
									font-size: 36rpx;
									margin-right: 4rpx;
								}

								.amount {
									font-size: 60rpx;
								}
							}
						}
					}

					&.selected {
						border-color: #ff7e17;
						background-color: #fff2d8;
						box-shadow: 0 4rpx 12rpx rgba(255, 85, 0, 0.1);
						transform: translateY(-2rpx);

						.plan-price {
							transform: scale(1.05);
						}
					}
				}
			}
		}

		.bottom-bar {
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			padding: 20rpx 30rpx;
			padding-bottom: 20rpx;
			padding-bottom: calc(constant(safe-area-inset-bottom) + 20rpx);
			padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
			background-color: #FFFFFF;

			display: flex;
			justify-content: space-between;
			align-items: center;
			box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

			.total-price {
				display: flex;
				align-items: baseline;

				.label {
					font-size: 28rpx;
					color: #666;
				}

				.currency {
					font-size: 36rpx;
					color: #FF8918;
					margin-right: 4rpx;
				}

				.amount {
					font-size: 60rpx;
					font-weight: 700;
					color: #FF8918;
				}
			}

			.pay-btn {
				width: 440rpx;
				height: 80rpx;
				background-image: linear-gradient(180deg, #FF8510 0, #FFBD73 100%);
				color: #FFFFFF;
				font-size: 32rpx;
				margin: 0;
				border-radius: 40rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				&[disabled]:not([type]) {
					background-image: linear-gradient(180deg, #f5f5f5 0, #f5f5f5 100%) !important;
					color: #999 !important;
				}

				&::after {
					border: none;
				}
			}
		}
	}
</style>