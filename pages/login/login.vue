<template>
	<view class="content">
		<view class="head" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="left">
					<view class="itemList">
						<view class="item" @tap="back">
							<button>
								<image style="width:20rpx"
									:src="imgUrl+'/qdkbm/newimage/fhui/back-light.png'">
								</image>
							</button>
						</view>
					</view>
				</view>
				<view class="text">登录</view>
			</view>
		</view>
		<view class="login-container" :style="{'height':'calc(100vh - '+height+'px)'}">
			<view class="tit">飞鸿AI</view>
			<view class="desc">学业规划，开启求知征途</view>
			<view class="logos">
				<image :src="imgUrl+'/qdkbm/newimage/fhui/logos3.png'"></image>
			</view>
			<view class="submit">
				<button :disabled="disabled" class="submit-btn" @tap="handleLogin">
					<image :src="imgUrl+'/qdkbm/newimage/fhui/icon-weixin4.png'"></image>
					微信授权登录
				</button>
			</view>

			<view class="label privacy-box" :class="isCheckAll?'':'show-info'">
				<view class="privacy-radio-hot position-a"></view>
				<checkbox-group @change="CheckboxChange($event,isCheckAll)">
					<label :class="isCheckAll?'checked':''">
						<view class="flex">
							<view class="radio">
								<checkbox class="blue" color="#007fe4" :checked="isCheckAll ? true : false"
									:value="isCheckAll">
								</checkbox>已阅读并同意
							</view>
						</view>
					</label>
				</checkbox-group>
				<view class="info14">
					<text @tap="readProtocol">《用户服务协议及隐私政策》</text>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	import dayjs from 'dayjs'
	export default {
		data() {
			return {
				imgUrl: this.$base.uploadImgUrl,
				userInfo: {},
				disabled:true,
				isCheckAll: false,
				code: "",
				height: 0,
				titleTop: 0
			}
		},
		onLoad() {},
		onShow() {
			// 不自动登录，等待用户手动操作
		},
		mounted() {},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top
			const query = uni.createSelectorQuery().in(this)
			query.selectAll('.head').boundingClientRect(data => {
				console.log(data)
				this.height = data[0].height + this.titleTop - 4
				console.log(this.height)
			}).exec()
		},
		methods: {
			// 自动登录方法 - 仅在需要时调用，不自动勾选协议
			autoLogin() {
				// 检查是否已勾选协议
				if (!this.isCheckAll) {
					return; // 如果未勾选协议，不执行登录
				}

				// 使用微信登录
				uni.login().then(res2 => {
					this.$apis.wxLogin({
						code: res2.code
					}).then((res) => {
						if (res.code == 0) {
							// 登录成功，处理用户数据
							this.userInfo = res.data

							// 处理用户数据
							let arr = []
							if (res.data.userRespVO.secondSubject) {
								arr = (res.data.userRespVO.secondSubject).split(',')
							}
							let myInfo = {
								score: res.data.userRespVO.score,
								province: res.data.userRespVO.province,
								grade: res.data.userRespVO.grade,
								secondSubject: res.data.userRespVO.secondSubject || arr.join(','),
								recommendedMajors:res.data.userRespVO.recommendedMajors,
								subjects: arr,
								ranking: res.data.userRespVO.ranking,
								rankingRange: res.data.userRespVO.rankingRange
							}
							let askInfo = res.data.userAssetsDO

							if (res.data.userRespVO.score) {
								uni.setStorageSync('myInfo', myInfo)
							}
							let isVip = false
							if (res.data.userAssetsDO.contentStartTime && res.data.userAssetsDO.contentEndTime) {
								let date1 = (dayjs(res.data.userAssetsDO.contentEndTime).format('YYYY/MM/DD HH:mm:ss'));
								let date2 = dayjs(res.data.userAssetsDO.contentStartTime).format('YYYY/MM/DD HH:mm:ss');
								let diffTime = (new Date(date1)).getTime() - (new Date(date2)).getTime();
								if (diffTime > 0) {
									isVip = true
								} else {
									isVip = false
								}
							} else {
								isVip = false
							}
							uni.setStorageSync('isVip', isVip)
							uni.setStorageSync('askInfo', askInfo)
							uni.setStorageSync('tys', askInfo.contentLeftCount)
							uni.setStorageSync('tws', askInfo.askLeftCount)
							uni.setStorageSync('token', this.userInfo.accessToken)
							uni.setStorageSync('userId', this.userInfo.userId)
							uni.setStorageSync('openId', res.data.userRespVO.openId)
							uni.setStorageSync('userName', res.data.userRespVO.username)
							uni.setStorageSync('nickname', res.data.userRespVO.nickname)

							// 无感登录，直接返回上一页
							uni.navigateBack({
								delta: 1
							})
						}
					}).catch((err) => {
						// 登录失败，不做处理，等待用户手动登录
					})
				}).catch((err) => {
					// 获取微信登录码失败，不做处理，等待用户手动登录
				})
			},

			checkedProtocol() {
				uni.showToast({
					title: "请勾选《用户服务协议及隐私政策》",
					icon: 'none'
				})
			},
			CheckboxChange(e, item) {
				if (this.isCheckAll) {
					this.isCheckAll = false
					this.disabled = true
				} else {
					this.isCheckAll = true
					this.disabled = false
				}
			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			},
			readProtocol() {
				uni.navigateTo({
					url: "/pages/center/guide"
				})
			},
			handleLogin() {
				if(this.isCheckAll){
					uni.showLoading({
							title: "登录中..."
						})
						uni.login().then(res2 => {
							this.$apis.wxLogin({
								code: res2.code
							}).then((res) => {
								if (res.code == 0) {
									uni.hideLoading()
									this.userInfo = res.data
									uni.showToast({
										title: '登录成功',
										icon: 'success',
										duration: 1000
									});
									setTimeout(() => {
										uni.navigateBack({
											delta: 1
										})
									}, 1000)
									let arr = []
									if (res.data.userRespVO.secondSubject) {
										arr = (res.data.userRespVO.secondSubject).split(',')
									}
									let myInfo = {
										score: res.data.userRespVO.score,
										province: res.data.userRespVO.province,
										grade: res.data.userRespVO.grade,
										secondSubject: res.data.userRespVO.secondSubject || arr.join(','),
										recommendedMajors:res.data.userRespVO.recommendedMajors,
										subjects: arr,
										ranking: res.data.userRespVO.ranking,
										rankingRange: res.data.userRespVO.rankingRange
									}
									console.log('登录myInfo的值:', myInfo)
									let askInfo = res.data.userAssetsDO

									if (res.data.userRespVO.score) {
										uni.setStorageSync('myInfo', myInfo)
									}
									let isVip = false
									if (res.data.userAssetsDO.contentStartTime && res.data.userAssetsDO
										.contentEndTime) {

										let date1 = (dayjs(res.data.userAssetsDO.contentEndTime).format(
											'YYYY/MM/DD HH:mm:ss')); //开始时间

										let date2 = dayjs(res.data.userAssetsDO.contentStartTime).format(
											'YYYY/MM/DD HH:mm:ss'); //结束时间

										let diffTime = (new Date(date1)).getTime() - (new Date(
											date2)).getTime(); //时间差的毫秒数
										if (diffTime > 0) {
											isVip = true
										} else {
											isVip = false
										}

									} else {
										isVip = false
									}
									uni.setStorageSync('isVip', isVip)
									uni.setStorageSync('askInfo', askInfo)
									uni.setStorageSync('tys', askInfo.contentLeftCount)
									uni.setStorageSync('tws', askInfo.askLeftCount)
									uni.setStorageSync('token', this.userInfo.accessToken)
									uni.setStorageSync('userId', this.userInfo.userId)
									uni.setStorageSync('openId', res.data.userRespVO.openId)
									uni.setStorageSync('userName', res.data.userRespVO.username)
									uni.setStorageSync('nickname', res.data.userRespVO.nickname)
								} else {
									uni.hideLoading()
								}
							}).catch((err) => {
								uni.hideLoading()
							})
						})





				} else{
					uni.showToast({
						title:"请阅读并同意用户隐私协议",
						icon:'none'
					})
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.privacy-box {
		position: relative;
		display: flex;
		flex-direction: row;
		margin: 60rpx 0;
	}

	.privacy-box.show-info::before {
		background: url(https://front-xps-cdn.xsyx.xyz/custom/shopping-vip/loginV3/gx3.png)50%/contain no-repeat;
		content: "";
		height: 68rpx;
		left: -8rpx;
		position: absolute;
		top: 0;
		transform: translateY(-100%);
		width: 196rpx
	}

	.privacy-box .privacy-radio-hot {
		background: transparent;
		height: 70rpx;
		left: -4rpx;
		top: -4rpx;
		width: 146rpx;
		z-index: -1;
		position: absolute;
	}

	label {
		display: block;
		width: 100%;

		.flex {
			display: flex;
			// padding: 40rpx 0 10rpx 0;
			position: relative;

			align-items: center;

			.radio {
				margin-left: 0rpx;
				margin-right: 0rpx;
				display: flex;
				align-items: center;
				position: relative;
				font-size: 28rpx;
				color: #333;

				&::after {
					width: 8px;
					height: 4px;
					content: "";
					position: absolute;
					left: 20rpx;
					top: 50%;
					margin-left: -9rpx;
					margin-top: -3px;
					display: inline-block;
					border: 3rpx solid #fff;
					border-width: 0 0 3rpx 3rpx;
					transform: rotate(-45deg);
					-ms-transform: rotate(-45deg);
					-moz-transform: rotate(-45deg);
					-webkit-transform: rotate(-45deg);
					-o-transform: rotate(-45deg);
					vertical-align: baseline;
				}

				&::before {
					display: flex;
					text-align: center;
					border-width: 1px;
					content: "";
					background-color: #fff;
					border-color: #c8c9cc;
					flex-direction: row;
					flex: none;
					align-items: center;
					justify-content: center;
					box-sizing: border-box;
					color: transparent;
					text-align: center;
					transition-property: color, border-color, background-color;
					font-size: 20px;
					width: 40rpx;
					height: 40rpx;
					margin-right: 10rpx;
					border: 1px solid #c8c9cc;
					transition-duration: .2s;
					border-style: solid;
					transform: scale(0.9);
					border-radius: 100%;

				}

				radio {
					visibility: hidden;
					display: none;
					transform: scale(0.7);
				}

				checkbox {
					visibility: hidden;
					display: none;
					transform: scale(0.8);
				}

			}



		}

		&.checked .flex {


			.radio {
				&::before {
					width: 40rpx;
					height: 40rpx;
					background-color: #ff6200;
					border-color: #ff6200;
				}
			}
		}
	}

	.info14 {
		font-size: 28rpx;

		text {
			color: #FF8918;
			font-size: 28rpx;
			// text-decoration: underline;
		}
	}

	.login-container {

		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		overflow: hidden;

		.tit {
			font-size: 48rpx;
			font-weight: 700;
			margin-bottom: 40rpx;
			line-height: 48rpx;
		}

		.desc {
			font-size: 32rpx;
			color: #676767;
			margin-bottom: 70rpx;
		}

		.logos {
			image {
				width: 280rpx;
				height: 420rpx;
			}
		}
	}

	.submit {
		margin: 0 40rpx;
		box-sizing: border-box;
		width: 100%;
		padding-top: 150rpx;
	}

	.submit-btn {
		margin: 0 40rpx;
		background-image: linear-gradient(180deg, #FF8510 0, #FFBD73 100%);
		color: #fff;
		font-size: 32rpx;
		border-radius: 50rpx;
		height: 90rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		line-height: 90rpx;
		&[disabled]:not([type]){
			background-image:linear-gradient(180deg, #f5f5f5 0, #f5f5f5 100%) !important;
			color:#999 !important;
		}

		image {
			width: 48rpx;
			height: 48rpx;
			margin-right: 15rpx;
		}

		&::after {
			border: none;
		}
	}
</style>