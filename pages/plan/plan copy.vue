<template>
	<view>
		<u-popup :round="8" mode="center" closeOnClickOverlay bgColor="transparent" :show="show" @close="close"
			@open="open">
			<view class="picbox"
				style="background-image: url('https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/pop-bg.png');">
				<view class="closeicon" @tap="close">
					<image src="https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/fh-cancelbtn.png"></image>
				</view>
				<view class="successimg" style="position: absolute;left:50%;margin-left:-90rpx;top:-10rpx;z-index:99;">
					<image style="width:220rpx;" mode="widthFix"
						src="https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/popicon1.png"></image>
				</view>
				<view class="myboxs">
					<view class="cont">
						<text v-if="confirmText=='微信支付'">您确定花费{{itemInfo.money}}元\n购买<text
								class="bold">{{itemInfo.name}}</text>{{itemInfo.total}}次吗？</text>
						<text v-if="confirmText=='立即使用'">您已购买<text
								class="bold">{{itemInfo.name}}</text>{{itemInfo.total}}次!</text>
					</view>
					<view class="btn"><button :disabled="disabled" @tap="confirmPay">{{confirmText}}</button></view>
				</view>
			</view>
			<!-- <view class="popbox">
				<image mode="widthFix" style="width: 100%;" src="https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/pop-bg.png"></image>
				<view class="popcont">
					
				
				<view class="cont"><text>{{myMsg}}</text></view>
				<view class="btn"><button @tap="confirmPay">{{confirmText}}</button></view>
				</view>
			</view> -->
		</u-popup>
		<view class="bgs">
			<image src="https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/fh-bg11.png" mode="widthFix"
				style="position: absolute;left:0;top:0;width:100%"></image>
		</view>
		<view class="head" style="background:none" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="left">
					<view class="itemList">
					</view>
				</view>
				<view class="text">规划报告</view>
			</view>
		</view>
		<view class="loading-mask" v-if="loading">
			<view class="loading-spinner"></view>
		</view>
		<view class="list" style="position: relative;">
			<view class="none" v-if="list.length==0">
				<view class="icon">
					<image :src="imgUrl+'/qdkbm/newimage/nodata-express.png'"></image>
				</view>
				<view class="text">暂无数据</view>
			</view>
			<view class="item" v-for="(item,index) in list" :data-name="item.name" :data-end="item.leftTimes"
				:data-total="item.totalTimes" :data-money="item.price" :data-id="item.id" :key="index" @tap="clickDetail">
				<block v-if="item.id==1">
					<image style="width: 100%;" src="https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/fh-box1.png"
						mode="widthFix"></image>
				</block>
				<block v-if="item.id==2">
					<image style="width: 100%;" src="https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/fh-box2.png"
						mode="widthFix"></image>
				</block>
				<view class="boxs">
					<view class="box1">
						<view class="left">{{item.name}}</view>
						<view class="right">可用次数：{{item.leftTimes}}/{{item.totalTimes}}</view>
					</view>
					<view class="box2">
						{{item.content}}
					</view>
				</view>
			</view>
		</view>
		<tabBar :current-page="2"></tabBar>
	</view>
</template>

<script>
	import tabBar from '@/components/tabBar.vue'
	export default {
		components: {
			tabBar
		},
		data() {
			return {
				disabled:false,
				loading: false,
				imgUrl:this.$base.uploadImgUrl,
				itemInfo: {},
				show: false,
				confirmText: "",
				myMsg: "",
				list: [
					// 	{
					// 	id: 1,
					// 	name: "体验版",
					// 	price:9.9,
					// 	leftTimes: 0,
					// 	totalTimes: 10,
					// 	content: "根据最新教育部高考大数据，给您提供1份个性化院校及专业填报指南。"
					// }, {
					// 	id: 2,
					// 	name: "专业版",
					// 	leftTimes: 1,
					// 	price:399,
					// 	totalTimes: 10,
					// 	content: "堪比志愿名师标准，给你生成20+份精准分析报告，内容含院校及专业填报推荐，学校特色，专业等。"
					// },
				],
				orderId: "",
				openId: "",
				titleTop: 0
			}
		},
		onLoad() {

		},
		onShow() {
			if (uni.getStorageSync('openId')) {
				this.openId = uni.getStorageSync('openId')
			}
			if(uni.getStorageInfoSync('token')){
				this.getList()
			} else{
				this.list = [
						{
						id: 1,
						name: "体验版",
						price:9.9,
						leftTimes: 0,
						totalTimes: 0,
						content: "根据最新教育部高考大数据，给您提供1份个性化院校及专业填报指南。"
					}, {
						id: 2,
						name: "专业版",
						leftTimes: 0,
						price:399,
						totalTimes: 0,
						content: "堪比志愿名师标准，给你生成20+份精准分析报告，内容含院校及专业填报推荐，学校特色，专业等。"
					}
				]
			}
			

			// if(!uni.getStorageSync('token')){
			// 	uni.navigateTo({
			// 		url:"/pages/login/login"
			// 	})
			// } else{

			// }
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top
		},
		methods: {
			getList() {
				this.loading = true
				this.$apis.getWishIntroduce({source:'tab'}).then((res) => {
					if (res.code == 0) {
						// res.data.map((item) => {
						// 	item.leftTimes = 10
						// 	item.totalTimes = 10
						// })
						this.list = res.data
						if(!res.data){
							this.list = [
													{
													id: 1,
													name: "体验版",
													price:9.9,
													leftTimes: 0,
													totalTimes: 0,
													content: "根据最新教育部高考大数据，给您提供1份个性化院校及专业填报指南。"
												}, {
													id: 2,
													name: "专业版",
													leftTimes: 0,
													price:399,
													totalTimes: 0,
													content: "堪比志愿名师标准，给你生成20+份精准分析报告，内容含院校及专业填报推荐，学校特色，专业等。"
												}
											]
						}
						setTimeout(() => {
							this.loading = false
						}, 500)

					} else {
						this.list = []
						setTimeout(() => {
							this.loading = false
						}, 500)
					}
				}).catch((err) => {
					this.list = []
					setTimeout(() => {
						this.loading = false
					}, 500)
				})
			},
			confirmbtn2() {
				let that = this
				that.show = false
				let version = that.itemInfo.name === '体验版' ? 1 : 2
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/plan/work?ver=' + version
					})
				}, 200)
			},
			confirmPay() {
				if (this.confirmText == '微信支付') {
					this.disabled = true
					this.$apis.createOrder({
						items: [{
							skuId: this.itemInfo.id == 1 ? 30 : 31,
							count: 1,
							cartId: null
						}],
						couponId: undefined,
						pointStatus: false,
						deliveryType: 2,
						addressId: undefined,
						pickUpStoreId: undefined,
						receiverName: "",
						receiverMobile: "",
						seckillActivityId: undefined,
						combinationActivityId: undefined,
						combinationHeadId: undefined,
						bargainRecordId: undefined,
						pointActivityId: undefined,
						remark: ""
					}).then((res) => {
						if (res.code == 0) {
							this.orderId = res.data.payOrderId
							this.wechatPay()
						} else{
							this.disabled = false
						}
					}).catch((err)=>{
						this.disabled = false
					})
				} else {
					this.getList()
					this.confirmbtn2()
				}
			},
			wechatPay() {
				this.$apis.submitOrder({
					id: this.orderId,
					channelCode: "wx_pub",
					channelExtras: {
						openid: this.openId
					},
					displayMode: "url",
					returnUrl: ""
				}).then((res) => {
					if (res.code == 0) {
						let obj = JSON.parse(res.data.displayContent)
						this.wepay(obj)
					} else{
						this.disabled = false
					}
				}).catch((err)=>{
					this.disabled = false
				})
			},
			wepay(obj) {
				let that = this
				uni.requestPayment({
					appId: obj.appId,
					timeStamp: obj.timeStamp,
					nonceStr: obj.nonceStr,
					package: obj.packageValue,
					signType: obj.signType,
					paySign: obj.paySign,
					success(res) {
						that.disabled = false
						uni.showToast({
							title: "支付成功",
							icon: "none",
							duration: 1000
						})
						setTimeout(() => {
							that.myMsg = '您已购买' + that.itemInfo.name + that.itemInfo.totalTimes?that.itemInfo.totalTimes:10 + '次！';
							that.confirmText = "立即使用"
						}, 1000)
					},
					fail(res) {
						that.disabled = false
						uni.showToast({
							title: '支付失败',
							icon: 'none',
							duration: 650
						})
					}
				});
			},
			open() {
				this.show = true
			},
			close() {
				this.show = false
				this.disabled = false
			},
			clickDetail(e) {
				let count = e.currentTarget.dataset.end
				let itemInfo = {
					name: e.currentTarget.dataset.name,
					id:e.currentTarget.dataset.id,
					count:e.currentTarget.dataset.end,
					total: e.currentTarget.dataset.name === '体验版'?3:6,
					money: e.currentTarget.dataset.money
				}
				let version = itemInfo.name === '体验版' ? 1 : 2
				// if (count <= 0) {
				// 	this.show = true
				// 	this.itemInfo = itemInfo
				// 	this.confirmText = "微信支付"
				// 	this.myMsg = '您确定花费9.9元\n购买' + itemInfo.name + itemInfo.totalTimes + '次吗？'
				// } else {
					let token = uni.getStorageSync('token')
					if(token){
						uni.navigateTo({
							url: '/pages/plan/work?ver=' + version +'&item=' + encodeURIComponent(JSON.stringify(itemInfo)) 
						})
					} else{
						uni.showModal({
							title: '提示',
							content: '您还未登录，请先登录',
							confirmText: '去登录',
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/login/login'
									});
								}
							}
						});
					}
					
				 //}
			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			}
		}
	}
</script>
<style>
	page {
		background: #fef1db
	}
</style>
<style scoped lang="scss">
	.none {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background: #fff;
		border-radius: 16rpx;
		height: calc(100vh - 360rpx);
	
		.icon {
			image {
				width: 320rpx;
				height: 320rpx;
			}
		}
	
		.text {
			margin-top: 30rpx;
			color: #999;
			font-size: 28rpx;
		}
	}
	@-webkit-keyframes countloading {
		0% {
			transform: rotate(180deg)
		}

		100% {
			transform: rotate(0deg)
		}
	}

	@keyframes countloading {
		0% {
			transform: rotate(180deg)
		}

		100% {
			transform: rotate(0deg)
		}
	}


	// .progress-active-loading{animation:progress-active 2.4s cubic-bezier(.23,1,.32,1) infinite;background-color:#fff;height:100%;position:absolute;width:100%;z-index:999}
	@-webkit-keyframes progress-active {
		0% {
			opacity: .1;
			transform: translateX(-100%) scaleX(0)
		}

		20% {
			opacity: .5;
			transform: translateX(-100%) scaleX(0)
		}

		100% {
			opacity: 0;
			transform: translateX(0) scaleX(1)
		}
	}

	@keyframes progress-active {
		0% {
			opacity: .1;
			transform: translateX(-100%) scaleX(0)
		}

		20% {
			opacity: .5;
			transform: translateX(-100%) scaleX(0)
		}

		100% {
			opacity: 0;
			transform: translateX(0) scaleX(1)
		}
	}


	.loading-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;

		.loading-spinner {
			width: 50rpx;
			height: 50rpx;
			// border: 8rpx solid #c8a178;
			// border-top-color: transparent;
			// border-radius: 50%;
			animation: countloading 1s ease infinite;
			// animation:progress-active 2.4s cubic-bezier(.23,1,.32,1) infinite;
			background-image: url('https://pic.kefeichangduo.top/qdkbm/newimage/fhui/waiting.png');
			background-position: 50%;
			background-repeat: no-repeat;
			background-size: contain;
		}
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(360deg);
		}
	}

	.closeicon {
		position: absolute;
		right: -25rpx;
		top: 5rpx;

		image {
			width: 64rpx;
			height: 64rpx;
		}
	}

	.picbox {
		background-repeat: no-repeat;
		background-size: contain;
		width: 600rpx;
	}

	.myboxs {
		margin: 100rpx 30rpx 20rpx 30rpx;
		height: 400rpx;
		flex-direction: column;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;

		.cont {
			margin-bottom: 40rpx;

			text {
				font-weight: 700;
				letter-spacing: 2rpx;
				font-size: 32rpx;
				color: #AA7248;
				line-height: 54rpx;

				.bold {
					color: #e6702b;
					margin: 0 4rpx;
				}
			}
		}

		.btn {
			button {
				color: #fff;
				padding: 5rpx 80rpx;
				border-radius: 70rpx;
				font-size: 32rpx;
				background-image: linear-gradient(90deg, #FF8C38 0, #FD5819 100%);

				&::after {
					border: none;
				}
			}
		}
	}

	.popbox {
		display: flex;
		position: relative;
		flex-direction: column;

		.popcont {
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
		}

		.cont {
			font-size: 28rpx;
			padding: 40rpx 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.btn {
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;

			button {
				width: 170rpx;
				height: 60rpx;
				font-size: 28rpx;
				line-height: 60rpx;
				padding: 0;
				margin: 0;
				background: #fb6b3d;
				color: #fff;

				&::after {
					border: none
				}
			}
		}
	}

	.list {
		padding: 30rpx;
		padding-top: 15rpx;

		.item {
			position: relative;
			margin-bottom: 20rpx;

			.boxs {
				position: absolute;
				left: 0;
				top: 0;
				width: 100%;
				height: 100%;

				.box1 {
					display: flex;
					flex-direction: row;
					justify-content: space-between;
					align-items: center;
					margin: 25rpx 30rpx;

					.left {
						font-size: 36rpx;
						opacity: 0;
						font-weight: 700;
					}

					.right {
						color: #976c55;
						font-weight: 700;
						font-size: 28rpx;
						margin-right: 150rpx;
					}
				}

				.box2 {
					font-size: 28rpx;
					color: #999;
					margin: 0 30rpx;
					background: #FFF0E5;
					border-radius: 24rpx;
					color: #976C55;
					min-height: 100rpx;
					padding: 22rpx 30rpx;
					line-height: 40rpx;
					text-align: justify;
				}
			}

			&:nth-of-type(2) {
				.box1 {
					.right {
						color: #C74E11;
					}
				}

				.box2 {
					background: rgba(252, 170, 60, 0.92);
					color: #fff;
				}
			}
		}
	}







	.myboxstyle {
		width: 560rpx;
	}

	.mytitstyle {
		margin-top: 50rpx;
	}

	.mystyle {
		text-align: center;
		margin-bottom: 50rpx;
	}





	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.logo {
		height: 200rpx;
		width: 200rpx;
		margin-top: 200rpx;
		margin-left: auto;
		margin-right: auto;
		margin-bottom: 50rpx;
	}

	.text-area {
		display: flex;
		justify-content: center;
	}

	.title {
		font-size: 36rpx;
		color: #8f8f94;
	}
</style>