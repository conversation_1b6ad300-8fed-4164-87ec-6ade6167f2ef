<template>
	<view>
		<view class="head" style="background: #fff;" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="left" @tap="back">
					<view class="itemList">
						<view class="item">
							<button>
								<image style="width:20rpx"
									src="https://pic.kefeichangduo.top/qdkbm/newimage/fhui/back.png">
								</image>
							</button>
						</view>
					</view>
				</view>
				<view class="text" style="color: #333;">专业解析</view>
			</view>
			<view class="search-input">
				<view class="icon">
					<image :src="imgUrl+'/qdkbm/newimage/fhui/icon-search4.png'"></image>
				</view>
				<view class="inputwrap">
					<input type="text" class="inputstyle" v-model="searchKeyword" @input="handleInput"
						placeholder="请输入专业名称" placeholder-class="gray" />
					<button @tap="search">搜索</button>
				</view>
			</view>



			<!-- 教育层次标签页 -->
			<view class="tab-bar" v-if="!isSearchMode">
				<view class="tab-item" :class="{active: currentEducationLevel === '本科(普通教育)'}"
					@tap="changeEducationLevel('本科(普通教育)')">本科(普通)
				</view>
				<view class="tab-item" :class="{active: currentEducationLevel === '本科(职业教育)'}"
					@tap="changeEducationLevel('本科(职业教育)')">本科(职业)
				</view>
				<view class="tab-item" :class="{active: currentEducationLevel === '高职（专科）'}"
					@tap="changeEducationLevel('高职（专科）')">专科(高职)
				</view>
			</view>
			<!-- 搜索模式提示 -->
			<view class="search-mode-tip" v-if="isSearchMode">
				<view class="tip-content">
					<text>搜索结果包含所有教育层次</text>
					<text class="clear-search" @tap="clearSearch">清除搜索</text>
				</view>
			</view>
		</view>

		<view class="loading-mask" v-if="loading">
			<view class="loading-spinner"></view>
		</view>

		<view class="counter-warp">
			<wyh-tree-select-detail ref="treeSelect" :height="height" :items="filteredItems" :highlightedIds="yxzy_ids" @clickItem="onItem"
				@clickNav="handleCategoryClick" />
		</view>
	</view>
</template>
<script>
	import wyhTreeSelectDetail from '@/components/wyh-tree-select/wyh-tree-select-detail.vue'
	export default {
		components: {
			wyhTreeSelectDetail
		},
		name: 'major',
		data() {
			return {
				imgUrl: this.$base.uploadImgUrl,
				yxzy_ids: [],
				loading: false,
				items: [],
				originalItems: null, // 保存原始数据
				height: 0,
				titleTop: 0,
				searchKeyword: '', // 搜索关键词
				scrollLeft: 0, // 分类标签滚动位置
				currentCategory: 'all', // 当前选中的分类
				categories: [{id: 'all', name: '全部'}], // 分类列表
				inputTimer: null, // 用于输入防抖
				currentEducationLevel: '本科(普通教育)', // 当前教育层次
				isSearchMode: false, // 是否处于搜索模式
				searchResults: [] // 搜索结果数据
			}
		},
		computed: {
			// 根据搜索关键词和当前分类过滤专业列表
			filteredItems() {
				// 如果处于搜索模式，直接返回搜索结果
				if (this.isSearchMode && this.searchResults.length > 0) {
					return this.searchResults;
				}

				if (!this.items || this.items.length === 0) return [];

				// 如果没有搜索关键词且当前分类是全部，直接返回原始数据
				if (!this.searchKeyword && this.currentCategory === 'all') {
					return this.items;
				}

				// 复制一份数据进行过滤
				let filteredData = JSON.parse(JSON.stringify(this.items));

				// 根据分类过滤
				if (this.currentCategory !== 'all') {
					// 找到对应分类的数据
					const categoryData = this.items.find(item => item.id === this.currentCategory);
					if (categoryData) {
						filteredData = [categoryData];
					}
				}

				// 如果有搜索关键词，进行搜索过滤
				if (this.searchKeyword) {
					const keyword = this.searchKeyword.toLowerCase();

					// 过滤函数，递归搜索子项
					const filterByKeyword = (items) => {
						return items.filter(item => {
							// 检查当前项名称是否匹配
							const nameMatch = item.name && item.name.toLowerCase().includes(keyword);

							// 如果有子项，递归过滤子项
							if (item.children && item.children.length > 0) {
								item.children = filterByKeyword(item.children);
							}

							// 如果名称匹配或者有匹配的子项，则保留
							return nameMatch || (item.children && item.children.length > 0);
						});
					};

					filteredData = filterByKeyword(filteredData);
				}

				return filteredData;
			}
		},
		watch: {
		},
		onShow() {
			// 只有在items为空时才重新加载数据，避免从详情页返回时页面闪烁
			if (!this.items || this.items.length === 0) {
				this.getSubject();
			}
		},
		mounted() {},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top
			const query = uni.createSelectorQuery().in(this)
			query.selectAll('.head').boundingClientRect(data => {
				console.log(data)
				this.height = data[0].height + this.titleTop - 4
				console.log(this.height)
			}).exec()
		},
		methods: {
			// 处理输入事件，实现实时搜索
			handleInput() {
				// 使用防抖技术，避免频繁请求
				if (this.inputTimer) {
					clearTimeout(this.inputTimer);
				}

				// 设置300毫秒的防抖延迟
				this.inputTimer = setTimeout(() => {
					this.search();
				}, 300);
			},

			// 搜索专业
			search() {
				// 如果没有保存原始数据，先保存
				if (!this.originalItems && this.items.length > 0) {
					this.originalItems = JSON.parse(JSON.stringify(this.items));
				}

				// 如果搜索关键字为空且有原始数据，恢复原始数据
				if (!this.searchKeyword.trim() && this.originalItems) {
					this.clearSearch();
					return;
				}

				// 如果有搜索关键字，调用API进行搜索
				if (this.searchKeyword.trim()) {
					this.loading = true;
					// 进入搜索模式
					this.isSearchMode = true;

					this.$apis.getmajordata1({
						majorName: this.searchKeyword.trim()
						// 不传递教育层次参数，搜索所有教育层次的专业
					}).then((res) => {
						if (res.code == 0) {
							// 处理搜索结果
							if (res.data && res.data.tree && res.data.tree.categories) {
								// 将搜索结果转换为树形结构
								const searchResults = this.processSearchResults(res.data.tree.categories);
								this.searchResults = searchResults;

								// 更新分类列表
								this.updateCategories(res.data.tree.categories);
							}
						}
						this.loading = false;
					}).catch((err) => {
						console.error('搜索专业失败:', err);
						this.loading = false;
						this.isSearchMode = false;
					});
				}
			},

			// 清除搜索
			clearSearch() {
				this.searchKeyword = '';
				this.isSearchMode = false;
				this.searchResults = [];

				if (this.originalItems) {
					this.items = JSON.parse(JSON.stringify(this.originalItems));
				} else {
					// 如果没有原始数据，重新获取当前教育层次的数据
					this.getSubject();
				}
			},

			// 处理搜索结果
			processSearchResults(categories) {
				if (!categories || categories.length === 0) return [];

				return categories.map((item) => {
					let categoryItem = {
						id: item.id,
						name: item.name,
						text: item.name,
						badge: 'number',
						children: [],
						loaded: true // 搜索结果默认已加载
					};

					if (item.children && item.children.length > 0) {
						categoryItem.children = item.children.map((newitem) => {
							let subCategoryItem = {
								id: newitem.id,
								name: newitem.name,
								text: newitem.name,
								isshow: true,
								children: []
							};

							if (newitem.children && newitem.children.length > 0) {
								subCategoryItem.children = newitem.children.map((mynewitem) => {
									return {
										id: mynewitem.id,
										name: mynewitem.name,
										text: mynewitem.name,
										code: mynewitem.code
									};
								});
							}

							return subCategoryItem;
						});
					}

					return categoryItem;
				});
			},

			// 更新分类列表
			updateCategories(categories) {
				// 提取所有分类
				let categoryList = [{id: 'all', name: '全部'}];

				// 添加一级分类
				categories.forEach(category => {
					categoryList.push({
						id: category.id,
						name: category.name
					});
				});

				this.categories = categoryList;
			},

			// 切换教育层次
			changeEducationLevel(level) {
				// 如果点击的是当前层次，不做任何操作
				if (this.currentEducationLevel === level) return;

				// 如果处于搜索模式，先清除搜索
				if (this.isSearchMode) {
					this.clearSearch();
				}

				// 更新当前教育层次
				this.currentEducationLevel = level;

				// 清空搜索关键词
				this.searchKeyword = '';

				// 重置左侧导航选中状态
				this.currentCategory = 'all';

				// 重置树形选择组件的选中状态为第一项
				if (this.$refs.treeSelect) {
					this.$refs.treeSelect.resetActiveIndex();
				}

				// 重新获取专业数据
				this.getSubject();
			},

			onItem(e) {
				// 更新高亮显示的专业ID
				this.yxzy_ids = [e.id];

				// 注意：现在在组件内部已经处理了导航逻辑
				// 这里只需要更新高亮状态
			},

			// 处理左侧导航点击事件
			handleCategoryClick(index) {
				// 获取选中的类别
				const selectedCategory = this.items[index];
				console.log('点击类别:', selectedCategory);

				// 如果该类别尚未加载数据或者children为null，则加载数据
				if (selectedCategory && (!selectedCategory.loaded || !selectedCategory.children || selectedCategory.children.length === 0)) {
					console.log('加载类别数据:', selectedCategory.id);
					this.getSubject(selectedCategory.id);
				}
			},

			getSubject(categoryId = '') {
				this.loading = true
				// 如果是切换教育层次加载数据，则使用教育层次参数
				// 如果是搜索结果加载数据，则不使用教育层次参数
				const params = {
					name: "",
					categoryId: categoryId
				};

				// 如果没有搜索关键词，则添加教育层次参数
				if (!this.searchKeyword.trim()) {
					params.educationLevel = this.currentEducationLevel;
				}

				// 记录是否需要重置左侧导航选中状态
				// 只有在切换教育层次或首次加载时才重置
				const shouldResetActiveIndex = !categoryId && this.currentCategory === 'all';

				this.$apis.getmajordata1(params).then((res) => {
					if (res.code == 0) {
						// 如果是首次加载或者没有传入categoryId，则直接使用返回的数据
						if (!categoryId || this.items.length === 0) {
							let items = [];
							if (res.data && res.data.tree && res.data.tree.categories) {
								items = res.data.tree.categories.map((item) => {
									let categoryItem = {
										id: item.id,
										name: item.name,
										text: item.name,
										badge: 'number',
										children: [],
										loaded: item.loaded || false
									};

									if (item.children && item.children.length > 0) {
										categoryItem.children = item.children.map((newitem) => {
											let subCategoryItem = {
												id: newitem.id,
												name: newitem.name,
												text: newitem.name,
												isshow: true,
												children: []
											};

											if (newitem.children && newitem.children.length > 0) {
												subCategoryItem.children = newitem.children.map((mynewitem) => {
													return {
														id: mynewitem.id,
														name: mynewitem.name,
														text: mynewitem.name,
														code: mynewitem.code
													};
												});
											}

											return subCategoryItem;
										});
									}

									return categoryItem;
								});
							}

							this.items = items;
							// 保存原始数据，用于清除搜索时恢复
							this.originalItems = JSON.parse(JSON.stringify(items));

							// 只有在切换教育层次或首次加载时才重置左侧导航选中状态
							if (shouldResetActiveIndex && this.$refs.treeSelect) {
								this.$refs.treeSelect.resetActiveIndex();
							}
						} else {
							// 如果是加载特定类别的数据，则更新该类别的信息
							if (res.data && res.data.tree && res.data.tree.categories) {
								// 找到当前加载的类别
								const currentCategory = res.data.tree.categories.find(item => item.id === categoryId);

								if (currentCategory) {
									// 在现有数据中找到对应的类别索引
									const categoryIndex = this.items.findIndex(item => item.id === categoryId);

									if (categoryIndex !== -1) {
										// 处理二级分类数据
										let children = [];
										if (currentCategory.children && currentCategory.children.length > 0) {
											children = currentCategory.children.map((newitem) => {
												let subCategoryItem = {
													id: newitem.id,
													name: newitem.name,
													text: newitem.name,
													isshow: true,
													children: []
												};

												if (newitem.children && newitem.children.length > 0) {
													subCategoryItem.children = newitem.children.map((mynewitem) => {
														return {
															id: mynewitem.id,
															name: mynewitem.name,
															text: mynewitem.name,
															code: mynewitem.code
														};
													});
												}

												return subCategoryItem;
											});
										}

										// 更新类别数据
										this.items[categoryIndex].children = children;
										this.items[categoryIndex].loaded = true;
										console.log('类别数据已更新:', categoryId);
									}
								}
							}
						}
						this.loading = false;
					} else{
						this.loading = false;
					}
				}).catch((err)=>{
					console.error('获取专业数据失败:', err);
					this.loading = false;
				})
			},
			// 移除了bindChange方法，因为现在在组件内部处理了导航逻辑
			handelShowDialog() {
				this.showDialog = true;
			},
			doConfirmCategorys(items) {
				let arr = [];
				for (let i = 0; i < items.length; i++) {
					arr.push(items[i].label);
				}
				this.selectedItemsStr = arr.join(',');
			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			}
		}
	}
</script>
<style>
	page {
		background: #f5f5f5
	}
	.gray{
		color:#999;
	}
</style>
<style scoped lang="scss">
	@-webkit-keyframes countloading{0%{transform:rotate(180deg)}100%{transform:rotate(0deg)}}
	@keyframes countloading{0%{transform:rotate(180deg)}100%{transform:rotate(0deg)}}
	.loading-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;

		.loading-spinner {
			width: 50rpx;
			height: 50rpx;
			animation:countloading 1s ease infinite;
			background-image:url('https://pic.kefeichangduo.top/qdkbm/newimage/fhui/waiting.png');background-position:50%;background-repeat:no-repeat;background-size:contain;
		}
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(360deg);
		}
	}
	.search-input {
		margin: 20rpx 30rpx;
		align-items: center;
		height: 72rpx;
		background: #f1f1f1;
		border-radius: 48rpx;
		display: flex;
		flex-direction: row;

		.icon {
			width: 100rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			image {
				width: 40rpx;
				height: 40rpx;
			}
		}

		.inputwrap {
			flex: 1;
			position: relative;

			.inputstyle {
				width: 100%;
				height: 72rpx;
				line-height: 72rpx;
				font-size: 32rpx;
				color: #333;
			}

			button {
				z-index: 99;
				position: absolute;
				right: 0;
				top: 0;
				height: 72rpx;
				line-height: 72rpx;
				color: #fff;
				font-size: 34rpx;
				border-radius: 48rpx;
				background: #FF8918;
				padding: 0 50rpx;
				margin: 0;
				min-width: inherit;

				&::after {
					border: none
				}
			}
		}
	}

	.counter-warp {}

	.label {
		margin: 10px;
	}

	button {
		&::after {
			border: none
		}
	}

	/* 教育层次标签样式 */
	.tab-bar {
		display: flex;
		justify-content: space-around;
		background-color: #fff;
		padding: 20rpx 0;
		margin: 0 30rpx 20rpx 30rpx;
		border-radius: 12rpx;

		.tab-item {
			padding: 15rpx 20rpx;
			font-size: 32rpx;
			color: #666;
			position: relative;

			&.active {
				color: #FF8918;
				font-weight: bold;

				&:after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 40rpx;
					height: 6rpx;
					background-color: #FF8918;
					border-radius: 3rpx;
				}
			}
		}
	}

	/* 搜索模式提示样式 */
	.search-mode-tip {
		background-color: #FFF0E0;
		padding: 20rpx 0;
		margin: 0 30rpx 20rpx 30rpx;
		border-radius: 12rpx;

		.tip-content {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 30rpx;
			font-size: 28rpx;
			color: #FF8918;

			.clear-search {
				color: #FF8918;
				font-weight: bold;
				padding: 10rpx 20rpx;
				border: 1px solid #FF8918;
				border-radius: 30rpx;
			}
		}
	}

	/* 分类标签样式 */
	.category-tabs {
		display: flex;
		overflow-x: auto;
		margin: 0 20rpx 20rpx 20rpx;
		padding: 0 10rpx;
		white-space: nowrap;

		.category-tab {
			padding: 15rpx 30rpx;
			margin-right: 20rpx;
			font-size: 32rpx;
			color: #666;
			background: #fff;
			border: 2rpx solid #cecece;
			border-radius: 50rpx;

			&.active {
				border-color: #FF8918;
				background: #FF8918;
				color: #fff;
			}
		}
	}


</style>