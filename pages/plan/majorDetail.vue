<template>
	<view class="trend-detail-container">
		<view class="head" style="background:#FFFBF3;" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="left" @tap="back">
					<view class="itemList">
						<view class="item">
							<button style="width: auto;color:#333;">
								<image v-if="source" :src="imgUrl+'/qdkbm/newimage/fhui/logos4.png'"
									style="width: 64rpx;margin-right:10rpx;" mode="widthFix"></image>
								<text v-if="source" style="font-size: 32rpx;">飞鸿AI志愿规划</text>
								<image v-if="!source" style="width:20rpx" :src="imgUrl+'/qdkbm/newimage/fhui/back.png'">
								</image>
							</button>
						</view>
					</view>
				</view>
				<view class="text" style="color:#333;font-weight:700" :style="{'color':source?'transparent':''}">
					{{title?title:'专业介绍'}}</view>
			</view>
		</view>
		<view class="loading-mask" v-if="loading">
			<view class="loading-spinner"></view>
		</view>
		<!-- 专业基本信息 -->
		<view class="major-header">
			<view class="major-header-bg">
				<view class="major-title-container">
					<view class="major-icon">
						<text class="icon-text">🎓</text>
					</view>
					<view class="major-title-content">
						<view class="major-title">{{majorInfo ? (majorInfo.majorName || majorName) : majorName}}</view>
						<view class="major-meta" v-if="majorInfo && (majorInfo.educationLevel || majorInfo.disciplinaryCategory || majorInfo.disciplinarySubCategory)">
							<view class="meta-item" v-if="majorInfo.educationLevel">{{majorInfo.educationLevel}}</view>
							<view class="meta-divider" v-if="majorInfo.educationLevel && (majorInfo.disciplinaryCategory || majorInfo.disciplinarySubCategory)">·</view>
							<view class="meta-item" v-if="majorInfo.disciplinaryCategory">{{majorInfo.disciplinaryCategory}}</view>
							<view class="meta-divider" v-if="majorInfo.disciplinaryCategory && majorInfo.disciplinarySubCategory">·</view>
							<view class="meta-item" v-if="majorInfo.disciplinarySubCategory">{{majorInfo.disciplinarySubCategory}}</view>
						</view>
					</view>
				</view>
				<view class="major-decoration">
					<view class="decoration-circle circle-1"></view>
					<view class="decoration-circle circle-2"></view>
					<view class="decoration-circle circle-3"></view>
				</view>
			</view>
		</view>

		<!-- Tab导航 -->
		<view class="tab-nav">
			<view
				class="tab-item"
				:class="{'active': currentTab === 0}"
				@tap="switchTab(0)"
			>
				专业解析
			</view>
			<view
				class="tab-item"
				:class="{'active': currentTab === 1}"
				@tap="switchTab(1)"
			>
				专业简介
			</view>
			<view
				class="tab-item"
				:class="{'active': currentTab === 2}"
				@tap="switchTab(2)"
			>
				开设院校
			</view>
		</view>

		<!-- Tab内容 -->
		<view class="tab-content">
			<!-- 专业解析 -->
			<view v-if="currentTab === 0" class="tab-panel">
				<view class="analysis-content">
					<view class="none" v-if="!myhtml || myhtml.trim() === ''">
						<view class="empty-state">
							<text class="empty-icon">📊</text>
							<text>暂无专业解析数据</text>
						</view>
					</view>
					<view class="analysis-card" v-if="myhtml && myhtml.trim() !== ''">
						<view class="industry-content">
							<mpHtml  :content="myhtml" :tagStyle="mpHtmlStyle"></mpHtml>
						</view>
					</view>
				</view>
			</view>

			<!-- 专业简介 -->
			<view v-if="currentTab === 1" class="tab-panel">
				<view class="major-intro-content" v-if="majorInfo">
					<!-- 基本信息 -->
					<view class="info-section" v-if="hasBasicInfo()">
						<view class="section-header">
							<view class="section-icon">
								<text class="icon-emoji">ℹ️</text>
							</view>
							<view class="section-title">基本信息</view>
						</view>
						<view class="info-card">
							<view class="info-grid">
								<view class="info-item" v-if="majorInfo.majorCode && majorInfo.majorCode.trim() !== ''">
									<view class="info-icon">📋</view>
									<view class="info-content">
										<view class="info-label">专业代码</view>
										<view class="info-value">{{majorInfo.majorCode}}</view>
									</view>
								</view>
								<view class="info-item">
									<view class="info-icon">⏰</view>
									<view class="info-content">
										<view class="info-label">修业年限</view>
										<view class="info-value">{{majorInfo.studyYears || '四年'}}</view>
									</view>
								</view>
								<view class="info-item">
									<view class="info-icon">🎓</view>
									<view class="info-content">
										<view class="info-label">授予学位</view>
										<view class="info-value">{{majorInfo.degree || '学士'}}</view>
									</view>
								</view>
								<view class="info-item" v-if="majorInfo.graduateScale && majorInfo.graduateScale.trim() !== ''">
									<view class="info-icon">👥</view>
									<view class="info-content">
										<view class="info-label">毕业规模</view>
										<view class="info-value">{{majorInfo.graduateScale}}</view>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 男女比例 -->
					<view class="info-section" v-if="majorInfo.maleFemaleRatio && majorInfo.maleFemaleRatio.trim() !== ''">
						<view class="section-header">
							<view class="section-icon">
								<text class="icon-emoji">👥</text>
							</view>
							<view class="section-title">男女比例</view>
						</view>
						<view class="gender-card">
							<view class="gender-ratio-container">
								<view class="gender-stats">
									<view class="gender-stat male-stat">
										<view class="stat-icon">👨</view>
										<view class="stat-content">
											<view class="stat-label">男生</view>
											<view class="stat-value">{{getMalePercentage() || 0}}%</view>
										</view>
									</view>
									<view class="gender-stat female-stat">
										<view class="stat-icon">👩</view>
										<view class="stat-content">
											<view class="stat-label">女生</view>
											<view class="stat-value">{{getFemalePercentage() || 0}}%</view>
										</view>
									</view>
								</view>
								<view class="ratio-bar-container">
									<view class="ratio-bar">
										<view class="male-bar" :style="'width: ' + (getMalePercentage() || 0) + '%'">
											<view class="bar-label">{{getMalePercentage() || 0}}%</view>
										</view>
										<view class="female-bar" :style="'width: ' + (getFemalePercentage() || 0) + '%'">
											<view class="bar-label">{{getFemalePercentage() || 0}}%</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 专业介绍 -->
					<view class="info-section" v-if="majorInfo.majorIntroduction && majorInfo.majorIntroduction.trim() !== ''">
						<view class="section-header">
							<view class="section-icon">
								<text class="icon-emoji">📖</text>
							</view>
							<view class="section-title">专业介绍</view>
						</view>
						<view class="content-card">
							<view class="intro-text">{{majorInfo.majorIntroduction}}</view>
						</view>
					</view>

					<!-- 开设课程 -->
					<view class="info-section" v-if="majorInfo.courses && Array.isArray(majorInfo.courses) && majorInfo.courses.length > 0">
						<view class="section-header">
							<view class="section-icon">
								<text class="icon-emoji">📚</text>
							</view>
							<view class="section-title">开设课程</view>
						</view>
						<view class="content-card">
							<view class="courses-container">
								<view class="course-tag" v-for="(course, index) in majorInfo.courses" :key="index" v-if="course && (course.courseName || course)">
									《{{course.courseName || course}}》
								</view>
							</view>
						</view>
					</view>

					<!-- 部分高校专业方向培养 -->
					<view class="info-section" v-if="majorInfo.recommendSchools && Array.isArray(majorInfo.recommendSchools) && majorInfo.recommendSchools.length > 0">
						<view class="section-header">
							<view class="section-icon">
								<text class="icon-emoji">🏫</text>
							</view>
							<view class="section-title">专业方向培养</view>
						</view>
						<view class="content-card">
							<view class="schools-container">
								<view class="school-tag" v-for="(school, index) in majorInfo.recommendSchools" :key="index" v-if="school && school.trim() !== ''">
									{{school}}
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="none" v-else>
					暂无专业简介数据
				</view>
			</view>

			<!-- 开设院校 -->
			<view v-if="currentTab === 2" class="tab-panel">
				<view class="schools-content" v-if="majorInfo && majorInfo.recommendSchools && Array.isArray(majorInfo.recommendSchools) && majorInfo.recommendSchools.length > 0">
					<view class="schools-header">
						<view class="schools-count">
							<text class="count-number">{{getValidSchoolsCount()}}</text>
							<text class="count-text">所院校开设此专业</text>
						</view>
					</view>
					<view class="schools-grid">
						<view class="school-card" v-for="(school, index) in majorInfo.recommendSchools" :key="index" v-if="school && school.trim() !== ''">
							<view class="school-rank">{{getValidSchoolIndex(index) + 1}}</view>
							<view class="school-info">
								<view class="school-name">{{school}}</view>
								<view class="school-level">本科院校</view>
							</view>
							<view class="school-arrow">
								<text class="arrow-icon">›</text>
							</view>
						</view>
					</view>
				</view>
				<view class="none" v-else>
					<view class="empty-state">
						<text class="empty-icon">🏫</text>
						<text>暂无开设院校数据</text>
					</view>
				</view>
			</view>
		</view>

		<view class="bottom-buttons">
			<button class="btn province-btn" @tap="openQA">
				<text>智能问答</text>
			</button>

			<!-- <button class="btn download-btn" @tap="downloadContent">
				<image class="btn-icon" :src="imgUrl+'/qdkbm/newimage/fhui/icon-download.png'">
				</image>
			</button> -->
			<button class="btn download-btn" open-type="share">
				<image class="btn-icon" style="width: 44rpx;height:44rpx;margin-left:10rpx"
					:src="imgUrl+'/qdkbm/newimage/fhui/shareicon.png'">
				</image>
			</button>
		</view>


		<loading-popup :show="showpopbox" :imgUrl="imgUrl" @close="closepopbox" @back="backWithPopupClose"></loading-popup>
	</view>
</template>

<script>
	import mpHtml from '@/uni_modules/mp-html/components/mp-html/mp-html.vue'
	import simpleMarkdown from '@/utils/simple-markdown.js'
	import LoadingPopup from '@/components/loading-popup/loading-popup.vue'

	const markdownit = simpleMarkdown;
	export default {
		components: {
			mpHtml,
			LoadingPopup
		},
		data() {
			return {
				loading: false,
				title: "",
				showpopbox: false,
				imgUrl: this.$base.uploadImgUrl,
				titleTop: 0,
				titleTop2: 0,
				industryName: '',
				content: '',
				majorName: '',
				majorId: '', // 专业ID，用于调用新接口
				myhtml: "",
				showBackPopup: false,
				showProvincePopup: false,
				selectedProvince: {},
				dsCus: "",
				version: "",
				source: "",
				currentTab: 0, // 当前选中的tab，0-专业解析，1-专业简介，2-开设院校
				majorInfo: null, // 专业详细信息
				mpHtmlStyle: {
					h3: 'font-size: 36rpx; font-weight: 700; color: #FF8510; margin: 30rpx 0 20rpx; padding: 16rpx 20rpx; border-left: 8rpx solid #FF8510; background-color: rgba(255, 133, 16, 0.05); border-radius: 0 8rpx 8rpx 0;',
					p: 'font-size: 30rpx; line-height: 1.8; color: #333; margin-bottom: 20rpx; text-align: justify;',
					ol: 'padding-left: 40rpx; margin: 20rpx 0;',
					ul: 'padding-left: 40rpx; margin: 20rpx 0;',
					li: 'position: relative; margin-bottom: 16rpx; padding-left: 10rpx; font-size: 30rpx; line-height: 1.6; color: #333;',
					strong: 'color: #FF5B03; font-weight: 700;',
					a: 'color: #209BFF; text-decoration: underline;',
					hr: 'border: none; height: 2rpx; background: linear-gradient(to right, transparent, rgba(255, 133, 16, 0.5), transparent); margin: 30rpx 0;',
					img: 'max-width: 100%; height: auto; margin: 10rpx auto; display: block;'
				}
			}
		},
		onLoad(options) {
			if (options.majorName) {
				this.majorName = options.majorName
			}
			if (options.majorId) {
				this.majorId = options.majorId
				this.getMajorInfo()
			}
			if (options.source) {
				this.source = options.source
			}

			// 如果有专业名称，获取专业解析内容
			if (this.majorName) {
				this.getReport()
			}
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top;
			uni.createSelectorQuery().select('.head').boundingClientRect((res2) => {
				this.titleTop2 = res2.height + this.titleTop
			}).exec()
		},
		onShareAppMessage(res) {
			return {
				title: '好友邀您查看专业介绍',
				path: `/pages/plan/majorDetail?majorName=${this.majorName}&source=share`,
				imageUrl: ''
			}
		},
		methods: {
			// 切换tab
			switchTab(index) {
				this.currentTab = index;
			},

			// 获取男性比例
			getMalePercentage() {
				if (!this.majorInfo || !this.majorInfo.maleFemaleRatio || this.majorInfo.maleFemaleRatio.trim() === '') return 0;
				const ratio = this.majorInfo.maleFemaleRatio.trim();
				// 假设格式为 "38:62" 或 "男38:女62"
				const match = ratio.match(/(\d+):(\d+)/);
				if (match) {
					const male = parseInt(match[1]);
					const female = parseInt(match[2]);
					if (isNaN(male) || isNaN(female) || male < 0 || female < 0) return 0;
					const total = male + female;
					if (total === 0) return 0;
					return Math.round((male / total) * 100);
				}
				return 0;
			},

			// 获取女性比例
			getFemalePercentage() {
				if (!this.majorInfo || !this.majorInfo.maleFemaleRatio || this.majorInfo.maleFemaleRatio.trim() === '') return 0;
				const ratio = this.majorInfo.maleFemaleRatio.trim();
				// 假设格式为 "38:62" 或 "男38:女62"
				const match = ratio.match(/(\d+):(\d+)/);
				if (match) {
					const male = parseInt(match[1]);
					const female = parseInt(match[2]);
					if (isNaN(male) || isNaN(female) || male < 0 || female < 0) return 0;
					const total = male + female;
					if (total === 0) return 0;
					return Math.round((female / total) * 100);
				}
				return 0;
			},

			// 获取有效学校数量
			getValidSchoolsCount() {
				if (!this.majorInfo || !this.majorInfo.recommendSchools || !Array.isArray(this.majorInfo.recommendSchools)) {
					return 0;
				}
				return this.majorInfo.recommendSchools.filter(school => school && school.trim() !== '').length;
			},

			// 获取有效学校的索引
			getValidSchoolIndex(currentIndex) {
				if (!this.majorInfo || !this.majorInfo.recommendSchools || !Array.isArray(this.majorInfo.recommendSchools)) {
					return currentIndex;
				}
				let validIndex = 0;
				for (let i = 0; i <= currentIndex; i++) {
					const school = this.majorInfo.recommendSchools[i];
					if (school && school.trim() !== '') {
						if (i === currentIndex) {
							return validIndex;
						}
						validIndex++;
					}
				}
				return validIndex;
			},

			// 检查是否有基本信息
			hasBasicInfo() {
				if (!this.majorInfo) return false;
				// 检查是否有任何基本信息字段
				return (this.majorInfo.majorCode && this.majorInfo.majorCode.trim() !== '') ||
					   this.majorInfo.studyYears ||
					   this.majorInfo.degree ||
					   (this.majorInfo.graduateScale && this.majorInfo.graduateScale.trim() !== '');
			},

			// 获取专业详细信息（新接口）
			getMajorInfo() {
				if (!this.majorId) return;

				this.showpopbox = true;
				this.$apis.getCeeMajorDetail({
					id: this.majorId
				}).then((res) => {
					this.showpopbox = false;
					if (res.code == 0 && res.data) {
						this.majorInfo = res.data;
						this.title = res.data.majorName || this.majorName;

						// 如果没有专业名称，使用API返回的专业名称获取专业解析内容
						if (!this.majorName && res.data.majorName) {
							this.majorName = res.data.majorName;
							this.getReport();
						}
					} else {
						uni.showToast({
							title: '获取专业信息失败',
							icon: 'none'
						});
					}
				}).catch((err) => {
					this.showpopbox = false;
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none'
					});
				});
			},

			getReport() {
				// uni.showLoading({
				// 	title:"玩命加载中..."
				// })
				this.showpopbox = true
				this.$apis.getmajorsDetail({
					majorName: this.majorName
				}).then((res) => {
					if (res.code == 0) {
						this.showpopbox = false
						// uni.hideLoading()
						if (res.data) {
							this.title = this.majorName + ''
							this.myhtml = res.data.content
						} else {
							this.myhtml = ''
						}
					}
				}).catch((err) => {
					this.showpopbox = false
					// uni.hideLoading()
				})
			},
			back() {
				if (this.source) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack({
						delta: 1
					})
				}
			},
			confirmBack() {
				this.closeBackPopup();
				uni.navigateBack({
					delta: 1
				});
			},
			openQA() {
				uni.navigateTo({
					url: '/pages_chat/chat/chat'
				});
			},
			downloadContent() {
				uni.showLoading({
					title: '复制中...'
				});
				setTimeout(() => {
					uni.hideLoading();
					uni.setClipboardData({
						data: this.myhtml,
						success: function(res) {
							uni.showToast({
								title: "复制成功",
								icon: "success",
							});
						},
					});

					// uni.showToast({
					// 	title: '下载成功',
					// 	icon: 'success'
					// });
				}, 1500);
			},
			// 关闭弹窗
			closepopbox() {
				this.showpopbox = false;
			},
			// 关闭弹窗并返回上一页
			backWithPopupClose() {
				this.showpopbox = false;
				uni.navigateBack({
					delta: 1
				});
			}
		}
	}
</script>
<style>
	page {
		background: #f8f9fa;
	}
</style>
<style lang="scss">
	@-webkit-keyframes countloading {
		0% {
			transform: rotate(180deg)
		}

		100% {
			transform: rotate(0deg)
		}
	}

	@keyframes countloading {
		0% {
			transform: rotate(180deg)
		}

		100% {
			transform: rotate(0deg)
		}
	}


	// .progress-active-loading{animation:progress-active 2.4s cubic-bezier(.23,1,.32,1) infinite;background-color:#fff;height:100%;position:absolute;width:100%;z-index:999}
	@-webkit-keyframes progress-active {
		0% {
			opacity: .1;
			transform: translateX(-100%) scaleX(0)
		}

		20% {
			opacity: .5;
			transform: translateX(-100%) scaleX(0)
		}

		100% {
			opacity: 0;
			transform: translateX(0) scaleX(1)
		}
	}

	@keyframes progress-active {
		0% {
			opacity: .1;
			transform: translateX(-100%) scaleX(0)
		}

		20% {
			opacity: .5;
			transform: translateX(-100%) scaleX(0)
		}

		100% {
			opacity: 0;
			transform: translateX(0) scaleX(1)
		}
	}


	.loading-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;

		.loading-spinner {
			width: 50rpx;
			height: 50rpx;
			// border: 8rpx solid #c8a178;
			// border-top-color: transparent;
			// border-radius: 50%;
			animation: countloading 1s ease infinite;
			// animation:progress-active 2.4s cubic-bezier(.23,1,.32,1) infinite;
			background-image: url('https://pic.kefeichangduo.top/qdkbm/newimage/fhui/waiting.png');
			background-position: 50%;
			background-repeat: no-repeat;
			background-size: contain;
		}
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(360deg);
		}
	}

	.closeicon {
		position: absolute;
		right: -25rpx;
		top: 5rpx;

		image {
			width: 64rpx;
			height: 64rpx;
		}
	}

	.picbox {
		background-repeat: no-repeat;
		background-size: 100% 100%;
		width: 600rpx;
	}

	.myboxs {
		margin: 50rpx 30rpx 20rpx 30rpx;
		height: 320rpx;
		flex-direction: column;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;

		.tit2 {
			margin-bottom: 20rpx;
			color: #AA7248;
			margin-top: 30rpx;
			font-size: 42rpx;
			font-weight: 700;
		}

		.cont {
			margin-bottom: 20rpx;

			text {
				font-weight: 700;
				letter-spacing: 0rpx;
				font-size: 26rpx;
				color: #AA7248;
				line-height: 42rpx;

				.bold {
					color: #e6702b;
					margin: 0 4rpx;
				}
			}
		}

		.jiazaizhong {
			margin-bottom: 20rpx;
		}

		.btn {
			button {
				color: #fff;
				padding: 5rpx 80rpx;
				border-radius: 70rpx;
				font-size: 32rpx;
				background-image: linear-gradient(90deg, #FF8C38 0, #FD5819 100%);

				&::after {
					border: none;
				}
			}
		}
	}

	// 专业解析内容样式
	.analysis-content {
		background: #f8f9fa;
		padding: 20rpx;

		.analysis-card {
			background: #fff;
			border-radius: 16rpx;
			padding: 30rpx;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
			border: 1rpx solid #f0f0f0;

			.industry-content {
				line-height: 1.7;
			}
		}
	}

	.contents {
		padding: 0 20rpx;
	}

	.mylist {
		margin: 0 -10rpx;
		display: flex;
		flex-direction: row;
		margin-bottom: 25rpx;

		.item {
			flex: 1;
			margin: 0 10rpx;
			background-repeat: no-repeat;
			width: 100%;
			background-size: 100% 100%;
			border-radius: 16rpx;

			.box11 {
				padding: 30rpx 30rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.left {
					font-size: 40rpx;
					font-weight: 700;
					color: #000;
				}

				.right {
					image {
						width: 15rpx;
						height: 24rpx;
					}
				}
			}
		}
	}

	.none {
		text-align: center;
		display: flex;
		padding: 180rpx 0;
		justify-content: center;
		align-items: center;
		font-size: 32rpx;
		color: #666;
	}

	.cityPart {
		padding: 40rpx 20rpx;

		.title {
			font-weight: 700;
			font-size: 32rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #000;
			margin-bottom: 35rpx;
		}

		.cityList {
			max-height: 600rpx;
			padding-bottom: 30rpx;
			display: flex;
			flex-direction: row;
			flex-wrap: wrap;
			overflow-y: auto;

			.item {
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0 10rpx;
				margin-bottom: 20rpx;
				width: calc(25% - 20rpx);
				background: #efefef;
				height: 70rpx;
				line-height: 70rpx;
				font-size: 28rpx;
				border-radius: 8rpx;

				&.active {
					color: #fff;
					background: #ff7e17;
				}

				&:active {
					transform: scale(0.98);
				}
			}
		}
	}

	.mybox {
		flex-direction: column;
		padding: 40rpx 60rpx 60rpx 60rpx;
		width: 480rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.tit {
			font-weight: 700;
			font-size: 32rpx;
			color: #000;
			margin-bottom: 40rpx;
		}

		.desc {
			font-size: 28rpx;
			color: #FF5500;
			text-align: justify;
			padding: 0 20rpx;
			margin-bottom: 40rpx;
		}

		.btnlist {
			display: flex;
			flex-direction: row;
			width: 100%;

			.btn {
				flex: 1;
				margin: 0 10rpx;

				button {
					font-size: 28rpx;
					padding: 0;
					height: 78rpx;
					line-height: 78rpx;
					color: #fff;
					font-weight: 700;
					background-image: linear-gradient(180deg, #FF8510 0, #FFBD73 100%);
					border-radius: 50rpx;

					&.btns {
						border: 2rpx solid #FF8510;
						height: 72rpx;
						line-height: 72rpx;
						color: #FF8510;
						background-image: linear-gradient(180deg, #fff 0, #fff 100%);
					}

					&::after {
						border: none;
					}
				}
			}
		}
	}

	// 专业头部信息样式
	.major-header {
		position: relative;
		overflow: hidden;

		.major-header-bg {
			background: linear-gradient(135deg, #FF8510 0%, #FFBD73 100%);
			padding: 40rpx 30rpx;
			position: relative;
			border-radius: 0 0 40rpx 40rpx;

			.major-title-container {
				display: flex;
				align-items: center;
				position: relative;
				z-index: 2;

				.major-icon {
					width: 80rpx;
					height: 80rpx;
					background: rgba(255, 255, 255, 0.2);
					border-radius: 20rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 20rpx;

					.icon-text {
						font-size: 40rpx;
						color: #fff;
					}
				}

				.major-title-content {
					flex: 1;

					.major-title {
						font-size: 44rpx;
						font-weight: 700;
						color: #fff;
						margin-bottom: 10rpx;
						line-height: 1.3;
					}

					.major-meta {
						display: flex;
						align-items: center;

						.meta-item {
							font-size: 26rpx;
							color: rgba(255, 255, 255, 0.9);
						}

						.meta-divider {
							margin: 0 8rpx;
							color: rgba(255, 255, 255, 0.7);
						}
					}
				}
			}

			.major-decoration {
				position: absolute;
				top: 0;
				right: 0;
				width: 200rpx;
				height: 200rpx;
				z-index: 1;

				.decoration-circle {
					position: absolute;
					border-radius: 50%;
					background: rgba(255, 255, 255, 0.1);

					&.circle-1 {
						width: 120rpx;
						height: 120rpx;
						top: -60rpx;
						right: -60rpx;
					}

					&.circle-2 {
						width: 80rpx;
						height: 80rpx;
						top: 20rpx;
						right: 20rpx;
					}

					&.circle-3 {
						width: 40rpx;
						height: 40rpx;
						top: 80rpx;
						right: 80rpx;
					}
				}
			}
		}
	}

	// Tab导航样式
	.tab-nav {
		display: flex;
		background: #fff;
		border-bottom: 2rpx solid #f0f0f0;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

		.tab-item {
			flex: 1;
			text-align: center;
			padding: 30rpx 0;
			font-size: 32rpx;
			color: #666;
			position: relative;
			transition: all 0.3s ease;

			&.active {
				color: #FF8510;
				font-weight: 700;
				background: linear-gradient(180deg, rgba(255, 133, 16, 0.05) 0%, transparent 100%);

				&::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 60rpx;
					height: 6rpx;
					background: linear-gradient(90deg, #FF8510 0%, #FFBD73 100%);
					border-radius: 3rpx;
					animation: tabSlide 0.3s ease;
				}
			}

			&:active {
				background: rgba(255, 133, 16, 0.1);
			}
		}
	}

	@keyframes tabSlide {
		0% {
			width: 0;
		}
		100% {
			width: 60rpx;
		}
	}

	// Tab内容样式
	.tab-content {
		background: #f8f9fa;
		min-height: 60vh;

		.tab-panel {
			padding: 0;
		}
	}

	// 专业简介内容样式
	.major-intro-content {
		background: #f8f9fa;
		padding: 20rpx;

		.info-section {
			margin-bottom: 30rpx;

			.section-header {
				display: flex;
				align-items: center;
				margin-bottom: 20rpx;

				.section-icon {
					width: 40rpx;
					height: 40rpx;
					margin-right: 15rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					.icon-emoji {
						font-size: 28rpx;
					}
				}

				.section-title {
					font-size: 32rpx;
					font-weight: 700;
					color: #333;
				}
			}

			.info-card, .gender-card, .content-card {
				background: #fff;
				border-radius: 16rpx;
				padding: 30rpx;
				box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
				border: 1rpx solid #f0f0f0;
			}

			.info-grid {
				display: grid;
				grid-template-columns: 1fr 1fr;
				gap: 20rpx;

				.info-item {
					display: flex;
					align-items: center;
					padding: 20rpx;
					background: #f8f9fa;
					border-radius: 12rpx;
					transition: all 0.3s ease;

					&:hover {
						background: #e9ecef;
					}

					.info-icon {
						font-size: 32rpx;
						margin-right: 15rpx;
					}

					.info-content {
						flex: 1;

						.info-label {
							font-size: 24rpx;
							color: #666;
							margin-bottom: 5rpx;
						}

						.info-value {
							font-size: 28rpx;
							font-weight: 700;
							color: #333;
						}
					}
				}
			}

			.intro-text {
				font-size: 28rpx;
				line-height: 1.8;
				color: #333;
				text-align: justify;
			}

			.courses-container {
				display: flex;
				flex-wrap: wrap;
				gap: 15rpx;

				.course-tag {
					background: linear-gradient(135deg, #FF8510 0%, #FFBD73 100%);
					color: #fff;
					padding: 12rpx 20rpx;
					border-radius: 20rpx;
					font-size: 26rpx;
					font-weight: 500;
					box-shadow: 0 2rpx 8rpx rgba(255, 133, 16, 0.3);
				}
			}

			.schools-container {
				display: flex;
				flex-wrap: wrap;
				gap: 15rpx;

				.school-tag {
					background: #f0f7ff;
					color: #1890ff;
					border: 1rpx solid #d6e4ff;
					padding: 12rpx 20rpx;
					border-radius: 20rpx;
					font-size: 26rpx;
					font-weight: 500;
				}
			}
		}
	}

	// 男女比例样式
	.gender-ratio-container {
		.gender-stats {
			display: flex;
			justify-content: space-around;
			margin-bottom: 30rpx;

			.gender-stat {
				display: flex;
				flex-direction: column;
				align-items: center;
				padding: 20rpx;
				border-radius: 16rpx;
				min-width: 120rpx;

				&.male-stat {
					background: linear-gradient(135deg, #4A90E2 0%, #6BB6FF 100%);
					color: #fff;
				}

				&.female-stat {
					background: linear-gradient(135deg, #E94B3C 0%, #FF6B6B 100%);
					color: #fff;
				}

				.stat-icon {
					font-size: 40rpx;
					margin-bottom: 10rpx;
				}

				.stat-content {
					text-align: center;

					.stat-label {
						font-size: 24rpx;
						opacity: 0.9;
						margin-bottom: 5rpx;
					}

					.stat-value {
						font-size: 32rpx;
						font-weight: 700;
					}
				}
			}
		}

		.ratio-bar-container {
			.ratio-bar {
				display: flex;
				height: 20rpx;
				border-radius: 10rpx;
				overflow: hidden;
				background: #f0f0f0;
				position: relative;

				.male-bar, .female-bar {
					position: relative;
					transition: width 0.8s ease;
					display: flex;
					align-items: center;
					justify-content: center;

					.bar-label {
						font-size: 20rpx;
						color: #fff;
						font-weight: 600;
						text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
					}
				}

				.male-bar {
					background: linear-gradient(90deg, #4A90E2 0%, #6BB6FF 100%);
				}

				.female-bar {
					background: linear-gradient(90deg, #E94B3C 0%, #FF6B6B 100%);
				}
			}
		}
	}

	// 开设院校样式
	.schools-content {
		background: #f8f9fa;
		padding: 20rpx;

		.schools-header {
			margin-bottom: 30rpx;
			text-align: center;

			.schools-count {
				background: linear-gradient(135deg, #FF8510 0%, #FFBD73 100%);
				color: #fff;
				padding: 20rpx 30rpx;
				border-radius: 25rpx;
				display: inline-block;
				box-shadow: 0 4rpx 12rpx rgba(255, 133, 16, 0.3);

				.count-number {
					font-size: 36rpx;
					font-weight: 700;
					margin-right: 8rpx;
				}

				.count-text {
					font-size: 28rpx;
				}
			}
		}

		.schools-grid {
			display: flex;
			flex-direction: column;
			gap: 20rpx;

			.school-card {
				background: #fff;
				border-radius: 16rpx;
				padding: 25rpx;
				display: flex;
				align-items: center;
				box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
				border: 1rpx solid #f0f0f0;
				transition: all 0.3s ease;

				&:active {
					transform: translateY(2rpx);
					box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
				}

				.school-rank {
					width: 60rpx;
					height: 60rpx;
					background: linear-gradient(135deg, #FF8510 0%, #FFBD73 100%);
					color: #fff;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 24rpx;
					font-weight: 700;
					margin-right: 20rpx;
					flex-shrink: 0;
				}

				.school-info {
					flex: 1;

					.school-name {
						font-size: 30rpx;
						font-weight: 600;
						color: #333;
						margin-bottom: 8rpx;
						line-height: 1.3;
					}

					.school-level {
						font-size: 24rpx;
						color: #666;
						background: #f0f7ff;
						color: #1890ff;
						padding: 4rpx 12rpx;
						border-radius: 12rpx;
						display: inline-block;
					}
				}

				.school-arrow {
					width: 24rpx;
					height: 24rpx;
					opacity: 0.6;
					display: flex;
					align-items: center;
					justify-content: center;

					.arrow-icon {
						font-size: 32rpx;
						color: #999;
						font-weight: bold;
					}
				}
			}
		}
	}

	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 80rpx 20rpx;
		color: #999;

		.empty-icon {
			font-size: 80rpx;
			margin-bottom: 20rpx;
			opacity: 0.6;
		}

		text:not(.empty-icon) {
			font-size: 28rpx;
		}
	}

	.trend-detail-container {

		padding-bottom: 120rpx;

		.content {
			padding: 30rpx;

			.industry-title {
				font-size: 36rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 30rpx;
				text-align: center;
			}

			.industry-content {
				line-height: 1.7;
			}
		}

		.bottom-buttons {
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			display: flex;
			justify-content: space-between;
			padding: 20rpx 20rpx;
			background-color: #FFFFFF;
			box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

			.btn {

				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 40rpx;
				font-size: 24rpx;

				white-space: nowrap;

				.btn-icon {
					width: 70rpx;
					height: 70rpx;
				}

				&.qa-btn {
					margin: 0;
					padding: 0;
				}

				&.province-btn {
					background-image: linear-gradient(180deg, #FF8510 0, #FFBD73 100%);
					flex: 1;
					margin: 0 0rpx;
					color: #fff;
					font-size: 32rpx;
					border: none;
				}

				&.download-btn {
					background-color: transparent;
					color: #333;
					width: auto;
					border: none;
					padding: 10rpx 20rpx;
					margin: 0;
				}

				&::after {
					border: none;
				}
			}
		}
	}
</style>