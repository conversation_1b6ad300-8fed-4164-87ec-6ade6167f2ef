// 提交当前题目数据到服务器
export function submitCurrentQuestionData() {
  // 确保当前题目存在
  if (!this.questions || this.currentQuestion < 0 || this.currentQuestion >= this.questions.length) {
    // console.log('当前题目不存在或超出范围');
    this._isNavigating = false;
    return;
  }
  
  const currentQ = this.questions[this.currentQuestion];
  if (!currentQ) {
    // console.log('无法获取当前题目对象');
    this._isNavigating = false;
    return;
  }
  
  // console.log('准备提交题目数据，ID:', currentQ.id);
  
  // 准备提交的答案数组
  let answerChoices = [];
  let writeContent = '';
  
  // 根据题目类型收集答案
  if (currentQ.type === 1) { // 单选题
    const currentAnswer = this.answers[currentQ.id];
    if (currentAnswer !== undefined) {
      answerChoices.push(currentAnswer);
      // console.log('单选题答案:', currentAnswer);
    }
  } else if (currentQ.type === 2) { // 多选题
    if (currentQ.id === 7) { // 第7题，专业选择
      answerChoices = Array.isArray(this.yxzy_ids) ? this.yxzy_ids : [];
      // console.log('专业选择答案:', answerChoices);
    } else {
      answerChoices = Array.isArray(this.answers[currentQ.id]) ? this.answers[currentQ.id] : [];
      // console.log('多选题答案:', answerChoices);
    }
  } else { // 填空题或其他类型
    if (currentQ.id == 6) { // 分数题
      answerChoices = [];
      // 为分数题准备写入内容
      const subjects = Array.isArray(this.fsList) ? this.fsList : [];
      const scoreStrings = subjects.map(item => {
        if (item && item.name && item.score !== undefined) {
          return item.name + ':' + item.score;
        }
        return '';
      }).filter(str => str !== '');
      
      writeContent = scoreStrings.join(',');
      // console.log('分数题答案:', writeContent);
    } else {
      // 其他填空题
      if (this.answers[currentQ.id] !== undefined) {
        writeContent = this.answers[currentQ.id];
        // console.log('填空题答案:', writeContent);
      }
    }
  }
  
  // 调用API提交当前题目数据
  this.$apis.commitQuestion({
    type: this.ver,
    questionId: currentQ.id,
    writeContent: writeContent,
    answerChoices: answerChoices,
    answerNo: this.answerNo
  }).then(res => {
    if (res.code == 0) {
      // console.log('题目数据提交成功:', res.data);
      
      // 更新答案编号和记录ID
      this.answerNo = res.data.answerNo;
      this.workId = res.data.recordId;
      
      // 更新问题状态
      this.checkAnswerStatus();
      
      // 重置当前问题的答题状态
      if (this.currentQuestion != 13) {
        this.isCurrentQuestionAnswered = false;
      }
      
      // 处理导航逻辑
      // 找到就业方向题目的索引
      const jobDirectionIndex = this.questions.findIndex(q => q.id === 12);
      // console.log(`就业方向题索引=${jobDirectionIndex}, 当前题目索引=${this.currentQuestion}`);
      
      // 如果当前题目是就业方向题的前一题，则跳过就业方向题
      if (this.currentQuestion === jobDirectionIndex - 1) {
        // console.log('检测到下一题是就业方向题，将跳过');
        
        // 跳过就业方向题，直接到下下题
        if (jobDirectionIndex + 1 < this.questions.length) {
          this.currentQuestion = jobDirectionIndex + 1;
          // console.log(`跳过就业方向题，直接到索引=${this.currentQuestion}`);
        } else {
          // 如果没有下下题，则只前进一题
          this.currentQuestion = this.currentQuestion + 1;
          // console.log(`前进到索引=${this.currentQuestion}`);
        }
      } else if (this.currentQuestion === jobDirectionIndex) {
        // 如果当前题目就是就业方向题，也跳过
        // console.log('当前题目是就业方向题，将跳过');
        if (jobDirectionIndex + 1 < this.questions.length) {
          this.currentQuestion = jobDirectionIndex + 1;
          // console.log(`跳过就业方向题，直接到索引=${this.currentQuestion}`);
        }
      } else {
        // 正常前进到下一题
        this.currentQuestion = this.currentQuestion + 1;
        // console.log(`正常前进到索引=${this.currentQuestion}`);
      }
      
      // 滚动到页面顶部
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 100
      });
      
      // 重置导航标志
      setTimeout(() => {
        this._isNavigating = false;
      }, 300);
    } else {
      console.error('题目数据提交失败:', res);
      uni.showToast({
        title: '提交失败，请重试',
        icon: 'none',
        duration: 2000
      });
      this._isNavigating = false;
    }
  }).catch(err => {
    console.error('题目数据提交异常:', err);
    uni.showToast({
      title: '网络异常，请重试',
      icon: 'none',
      duration: 2000
    });
    this._isNavigating = false;
  });
}
