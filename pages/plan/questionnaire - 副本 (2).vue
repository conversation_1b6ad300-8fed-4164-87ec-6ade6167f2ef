<template>
	<view class="bg">
		<view class="bgs">
			<image src="https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/fh-bg11.png" mode="widthFix"
				style="position: absolute;left:0;top:0;width:100%"></image>
		</view>
		<u-popup :round="8" mode="center" closeOnClickOverlay bgColor="transparent" :show="show" @close="close"
			@open="open">
			<view class="picbox"
				style="background-image: url('https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/pop-bg.png');">
				<view class="closeicon" @tap="close">
					<image src="https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/fh-cancelbtn.png"></image>
				</view>

				<view class="myboxs">
					<view class="tit2">报告生成中</view>
					<view class="cont">
						<text>可能需要几分钟，请耐心等待\n也可去使用其他功能,点击行为记录可进行查看</text>

					</view>
					<view class="jiazaizhong">
						<image src="https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/pop-progress.png"
							style="width:500rpx;height:30rpx"></image>
					</view>
					<view class="btn"><button @tap="goHome">返回首页</button></view>
				</view>
			</view>

		</u-popup>
		<view class="head" style="background:none" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="left">
					<view class="itemList">
						<view class="item" @tap="back">
							<button>
								<image style="width:20rpx"
									src="https://pacetupian.yuyuexiaobao.com/PACE/mall/back-light.png">
								</image>
							</button>
						</view>
					</view>
				</view>
				<view class="text">规划报告</view>
			</view>
		</view>
		<view class="container">
			<view class="question-container">
				<view class="question-header">
					<text class="question-number">{{currentQuestion + 1}}</text>
					<text class="question-title">{{questions[currentQuestion].content}}</text>
				</view>
				<view class="question-content">
					<view v-if="questions.length>0 && questions[currentQuestion].id ===1" class="cityList">
						<view class="myitem" @tap="clickProvince(item.id,questions[currentQuestion].id)"
							:class="answers[questions[currentQuestion].id]==item.id?'active':''"
							v-for="(item,index) in questions[currentQuestion].questionChoiceDoS" :key="index">
							{{item.choiceContent}}
						</view>

					</view>

					<view v-if="questions.length>0 && questions[currentQuestion].id ===2" class="sexList">
						<view class="myitem" @tap="clickSex(item.id,questions[currentQuestion].id)"
							:class="answers[questions[currentQuestion].id]==item.id?'active':''"
							v-for="(item,index) in questions[currentQuestion].questionChoiceDoS" :key="index">
							<view class="img">
								<view class="iconchecked" v-if="answers[questions[currentQuestion].id]==item.id"
									style="position: absolute;right:0;top:10rpx">
									<image style="width:50rpx;height:50rpx;"
										src="https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/fh-duihao.png"></image>
								</view>
								<image
									:src="item.choiceContent=='我是男生'?answers[questions[currentQuestion].id]==item.id?'https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/fh-boy-cur.png':'https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/fh-boy.png':answers[questions[currentQuestion].id]===item?'https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/fh-girl-cur.png':'https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/fh-girl.png'">
								</image>
							</view>
							<view class="text">{{item.choiceContent}}</view>
						</view>

					</view>

					<!-- 单选题 -->
					<block
						v-if="questions.length>0 && questions[currentQuestion].type === 1   && questions[currentQuestion].id !== 1  && questions[currentQuestion].id !== 2">
						<view class="radio-list">
							<view class="item" :data-id="item.id" :data-question-id="questions[currentQuestion].id"
								:class="answers[questions[currentQuestion].id]==item.id?'cur':''"
								v-for="(item,index) in questions[currentQuestion].questionChoiceDoS" :key="index"
								@tap="handleSelect">

								<view class="icon">
									<image
										:src="answers[questions[currentQuestion].id]==item.id?'https://pic.kefeichangduo.top/qdkbm/newimage/fh-notchecked.png':'https://pic.kefeichangduo.top/qdkbm/newimage/fh-checked.png'">
									</image>
								</view>
								<view class="text">{{item.choiceContent}}</view>

							</view>

						</view>
					</block>


					<!-- 多选题 -->

					<block
						v-if="questions.length>0 && questions[currentQuestion].type === 2 && questions[currentQuestion].id !== 7">
						<view class="radio-list">
							<view class="item" :class="item.checked?'cur':''"
								v-for="(item,index) in questions[currentQuestion].questionChoiceDoS" :key="index"
								@tap="handleMultiSelect(item,questions[currentQuestion].id,questions[currentQuestion].questionChoiceDoS)">

								<view class="icon">
									<image
										:src="item.checked?'https://pic.kefeichangduo.top/qdkbm/newimage/fh-notchecked.png':'https://pic.kefeichangduo.top/qdkbm/newimage/fh-checked.png'">
									</image>
								</view>
								<view class="text">{{item.choiceContent}}</view>

							</view>

						</view>
					</block>



					<!-- 多选题 -->
					<view
						v-if="questions.length>0 && questions[currentQuestion].type === 2 && questions[currentQuestion].id === 7">
						<view class="test"> <wyh-tree-select :items="items" :activeIds="yxzy_ids" :max="5"
								@clickItem="onItem" @onChange="bindChange($event, 'yxzy_ids')" />
						</view>

						<!-- <view class="dx-part" style="display: none;">
							<view class="dx-left">
								<view class="item" :class="item.checked?'active':''" @tap="changeLeftNav(item)"
									v-for="(item,index) in leftNavList" :key="index">{{item.name}}
									<view class="zy-list">
										<view class="myitem" :class="myitem.checked?'active':''"
											@tap="changemyItem(myitem)" v-for="(myitem,myindex) in item.children"
											:key="myindex">
											{{myitem.name}}
										</view>
									</view>
								</view>
							</view>

						</view> -->
						<!-- <checkbox-group :data-question-id="questions[currentQuestion].id" @change="handleMultiSelect">
							<label class="option-item"
								v-for="(item,index) in questions[currentQuestion].questionChoiceDoS" :key="index">
								<checkbox :value="item.id" />
								<text>{{item.choiceContent}}</text>
							</label>
						</checkbox-group> -->
					</view>


					<!-- 输入框 -->
					<view
						v-if="questions.length>0 && questions[currentQuestion].type === 3 && questions[currentQuestion].id === 5"
						style="font-size:28rpx;color:#333;display: flex;flex-direction: row;align-items: center;">
						<input class="input-field"
							style="font-size:32rpx;color:#FB6B3D;width: 160rpx;text-align:center;margin-right:20rpx;background:#FFF0EB;border-color:#FB6B3D;"
							:data-question-id="questions[currentQuestion].id" type="digit"
							:value="answers[questions[currentQuestion].id]" @input="handleInput" placeholder="请输入" /> 分
					</view>


					<view
						v-if="questions.length>0 && questions[currentQuestion].type === 3 && questions[currentQuestion].id === 13"
						style="font-size:28rpx;color:#333;display: flex;flex-direction: row;align-items: center;">
						<input class="input-field"
							style="width:100%;font-size:32rpx;color:#333;background:#f6f6f6;border-color:#f6f6f6;"
							:data-question-id="questions[currentQuestion].id"
							:value="answers[questions[currentQuestion].id]" @input="handleInput" placeholder="例如:税务局" />
					</view>
					<!-- 科目分数 -->
					<view
						v-if="questions.length>0 && questions[currentQuestion].type === 3 && questions[currentQuestion].id === 6"
						class="subjects-container">
						<view class="subject-item" v-for="(item,index) in questions[currentQuestion].subjects"
							:key="index">
							<text class="subject-name">{{item.name}}：</text>
							<input class="subject-score"
								style="height:70rpx;font-size:28rpx;line-height:70rpx;padding:0;padding-left:20rpx;background:#f6f6f6;border-color:#f6f6f6;margin-right:20rpx;"
								:data-question-id="questions[currentQuestion].id" :data-subject-index="index"
								:value="answers[questions[currentQuestion].id][index].score" @input="handleSubjectScore"
								type="digit" placeholder="请输入" maxlength="3" />
							<view class="unit">分</view>
						</view>
						<view class="total" style="padding:0 20rpx;font-size:28rpx;align-items: center;width:100%">
							总分：{{total}}</view>
					</view>
				</view>
			</view>
			<view class="button-container">
				<button v-if="currentQuestion > 0" class="nav-button prev-button" @tap="prevQuestion">上一题</button>
				<button v-if="currentQuestion < questions.length - 1" class="nav-button next-button"
					:class="isCurrentQuestionAnswered ? '' : 'disabled'" @tap="nextQuestion"
					:disabled="questions[currentQuestion].id===4?fkList.length!==2:questions[currentQuestion].id===6?fsList.length!==6:questions[currentQuestion].id===7?yxzy_ids.length==0 || yxzy_ids.length>5:!isCurrentQuestionAnswered&&!answers[questions[currentQuestion].id]">下一题</button>
				<button v-if="currentQuestion === questions.length - 1" class="submit-button"
					@tap="submitQuestionnaire">提交</button>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		components: {},
		data() {
			return {
				subjects: [],
				items: [],
				fsList: [],
				answerNo: -1,
				fkList: [],
				show: false,
				titleTop: 0,
				total: 0,
				currentQuestion: 0,
				questions: [{
						"id": 1, //问题Id
						"content": "学生所处的高考省份", //问题内容
						"type": 1, //问题类型 1单选题 2多选题 3填空题
						"isNecessary": 1, //是否必答题 0非必答 1必答 
						"status": 1, //问题状态 0无效 1有效 在这里都是有效
						"sort": 1, //排序字段 1234 小在前
						"questionChoiceDoS": [ //问题的选项 
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 1, //选项的Id
								"questionId": 1, //问题Id
								"choiceContent": "北京", //选项的内容
								"sort": 1, //排序字段 1234 从小到大排序
								"type": 1, //选项类型 1文字选项 2图文选项
								"imageUrl": null //如果是图文选项 这里是图片地址
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 2,
								"questionId": 1,
								"choiceContent": "天津",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 3,
								"questionId": 1,
								"choiceContent": "河北",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 4,
								"questionId": 1,
								"choiceContent": "山西",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 5,
								"questionId": 1,
								"choiceContent": "内蒙古",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 6,
								"questionId": 1,
								"choiceContent": "辽宁",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 7,
								"questionId": 1,
								"choiceContent": "吉林",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 8,
								"questionId": 1,
								"choiceContent": "黑龙江",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 9,
								"questionId": 1,
								"choiceContent": "上海",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 10,
								"questionId": 1,
								"choiceContent": "江苏",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 11,
								"questionId": 1,
								"choiceContent": "浙江",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 12,
								"questionId": 1,
								"choiceContent": "安徽",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 13,
								"questionId": 1,
								"choiceContent": "福建",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 14,
								"questionId": 1,
								"choiceContent": "江西",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 15,
								"questionId": 1,
								"choiceContent": "山东",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 16,
								"questionId": 1,
								"choiceContent": "河南",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 17,
								"questionId": 1,
								"choiceContent": "湖北",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 18,
								"questionId": 1,
								"choiceContent": "湖南",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 19,
								"questionId": 1,
								"choiceContent": "广东",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 20,
								"questionId": 1,
								"choiceContent": "广西",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 21,
								"questionId": 1,
								"choiceContent": "海南",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 22,
								"questionId": 1,
								"choiceContent": "重庆",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 23,
								"questionId": 1,
								"choiceContent": "四川",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 24,
								"questionId": 1,
								"choiceContent": "贵州",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 25,
								"questionId": 1,
								"choiceContent": "云南",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 26,
								"questionId": 1,
								"choiceContent": "西藏",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 27,
								"questionId": 1,
								"choiceContent": "陕西",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 28,
								"questionId": 1,
								"choiceContent": "甘肃",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 29,
								"questionId": 1,
								"choiceContent": "青海",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 30,
								"questionId": 1,
								"choiceContent": "宁夏",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 31,
								"questionId": 1,
								"choiceContent": "新疆",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							}
						]
					},
					{
						"id": 2,
						"content": "学生性别",
						"type": 1,
						"isNecessary": 1,
						"status": 1,
						"sort": 2,
						"questionChoiceDoS": [{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 32,
								"questionId": 2,
								"choiceContent": "我是男生",
								"sort": 1,
								"type": 2,
								"imageUrl": "xxxxxxxxxxx"
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 33,
								"questionId": 2,
								"choiceContent": "我是女生",
								"sort": 1,
								"type": 2,
								"imageUrl": "xxxxxxxxxxx"
							}
						]
					},
					{
						"id": 3,
						"content": "学生主科",
						"type": 1,
						"isNecessary": 1,
						"status": 1,
						"sort": 3,
						"questionChoiceDoS": [{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 34,
								"questionId": 3,
								"choiceContent": "物理",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 35,
								"questionId": 3,
								"choiceContent": "历史",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							}
						]
					},
					{
						"id": 4,
						"content": "学生副科（4选2）",
						"type": 2,
						"isNecessary": 1,
						"status": 1,
						"sort": 4,
						"questionChoiceDoS": [{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 36,
								"questionId": 4,
								"choiceContent": "化学",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 37,
								"questionId": 4,
								"choiceContent": "生物",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 38,
								"questionId": 4,
								"choiceContent": "政治",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 39,
								"questionId": 4,
								"choiceContent": "地理",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							}
						]
					},
					{
						"id": 5,
						"content": "高考总分",
						"type": 3,
						"isNecessary": 1,
						"status": 1,
						"sort": 5,
						"questionChoiceDoS": [{
							"createTime": 1742905924000,
							"updateTime": 1742905924000,
							"creator": "admin",
							"updater": "admin",
							"deleted": false,
							"id": 40,
							"questionId": 5,
							"choiceContent": "分",
							"sort": 1,
							"type": 1,
							"imageUrl": null
						}]
					},
					{
						"id": 6,
						"content": "各科分数",
						"type": 3,
						"isNecessary": 1,
						"status": 1,
						"sort": 6,
						"questionChoiceDoS": [{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 41,
								"questionId": 6,
								"choiceContent": "语文分",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 42,
								"questionId": 6,
								"choiceContent": "数学分",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 43,
								"questionId": 6,
								"choiceContent": "外语分",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 44,
								"questionId": 6,
								"choiceContent": "物理分",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 45,
								"questionId": 6,
								"choiceContent": "化学分",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 46,
								"questionId": 6,
								"choiceContent": "政治分",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							}
						]
					},
					{
						"id": 7,
						"content": "意向专业类别",
						"type": 2,
						"isNecessary": 1,
						"status": 1,
						"sort": 7,
						"questionChoiceDoS": [{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 47,
								"questionId": 7,
								"choiceContent": "工学",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 48,
								"questionId": 7,
								"choiceContent": "医学",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 49,
								"questionId": 7,
								"choiceContent": "文学",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 50,
								"questionId": 7,
								"choiceContent": "管理学",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 51,
								"questionId": 7,
								"choiceContent": "理学",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 52,
								"questionId": 7,
								"choiceContent": "经济学",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 53,
								"questionId": 7,
								"choiceContent": "法学",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 54,
								"questionId": 7,
								"choiceContent": "艺术学",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 55,
								"questionId": 7,
								"choiceContent": "教育学",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 56,
								"questionId": 7,
								"choiceContent": "农学",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 57,
								"questionId": 7,
								"choiceContent": "历史学",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 58,
								"questionId": 7,
								"choiceContent": "哲学",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 59,
								"questionId": 7,
								"choiceContent": "林学类",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 60,
								"questionId": 7,
								"choiceContent": "水产类",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 61,
								"questionId": 7,
								"choiceContent": "草学类",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 62,
								"questionId": 7,
								"choiceContent": "植物生产类",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 63,
								"questionId": 7,
								"choiceContent": "动物医学类",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 64,
								"questionId": 7,
								"choiceContent": "动物生产类",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 65,
								"questionId": 7,
								"choiceContent": "自然保护与环境生态类",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							}
						]
					},
					{
						"id": 8,
						"content": "性格",
						"type": 1,
						"isNecessary": 1,
						"status": 1,
						"sort": 8,
						"questionChoiceDoS": [{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 66,
								"questionId": 8,
								"choiceContent": "内向",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 67,
								"questionId": 8,
								"choiceContent": "外向",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							}
						]
					},
					{
						"id": 9,
						"content": "学习能力",
						"type": 1,
						"isNecessary": 1,
						"status": 1,
						"sort": 9,
						"questionChoiceDoS": [{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 68,
								"questionId": 9,
								"choiceContent": "强",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 69,
								"questionId": 9,
								"choiceContent": "弱",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							}
						]
					},
					{
						"id": 10,
						"content": "社交能力",
						"type": 1,
						"isNecessary": 1,
						"status": 1,
						"sort": 10,
						"questionChoiceDoS": [{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 70,
								"questionId": 10,
								"choiceContent": "强",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 71,
								"questionId": 10,
								"choiceContent": "弱",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							}
						]
					},
					{
						"id": 11,
						"content": "家庭年收入（单位元）",
						"type": 1,
						"isNecessary": 1,
						"status": 1,
						"sort": 11,
						"questionChoiceDoS": [{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 72,
								"questionId": 11,
								"choiceContent": "小于2万",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 73,
								"questionId": 11,
								"choiceContent": "2万到5万",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 74,
								"questionId": 11,
								"choiceContent": "5万到10万",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 75,
								"questionId": 11,
								"choiceContent": "10万到20万",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 76,
								"questionId": 11,
								"choiceContent": "20万到50万",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 77,
								"questionId": 11,
								"choiceContent": "50万以上",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							}
						]
					},
					{
						"id": 12,
						"content": "就业方向",
						"type": 1,
						"isNecessary": 1,
						"status": 1,
						"sort": 12,
						"questionChoiceDoS": [{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 78,
								"questionId": 12,
								"choiceContent": "体制内",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 79,
								"questionId": 12,
								"choiceContent": "体制外",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							}
						]
					},
					{
						"id": 13,
						"content": "人脉资源",
						"type": 3,
						"isNecessary": 1,
						"status": 1,
						"sort": 13,
						"questionChoiceDoS": []
					},
					{
						"id": 14,
						"content": "毕业去向",
						"type": 1,
						"isNecessary": 1,
						"status": 1,
						"sort": 14,
						"questionChoiceDoS": [{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 80,
								"questionId": 14,
								"choiceContent": "考公",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 81,
								"questionId": 14,
								"choiceContent": "专升本",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 82,
								"questionId": 14,
								"choiceContent": "考研",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 83,
								"questionId": 14,
								"choiceContent": "就业",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							}
						]
					},
					{
						"id": 15,
						"content": "城市省份",
						"type": 1,
						"isNecessary": 1,
						"status": 1,
						"sort": 15,
						"questionChoiceDoS": [{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 84,
								"questionId": 15,
								"choiceContent": "省内",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							},
							{
								"createTime": 1742905924000,
								"updateTime": 1742905924000,
								"creator": "admin",
								"updater": "admin",
								"deleted": false,
								"id": 85,
								"questionId": 15,
								"choiceContent": "省外",
								"sort": 1,
								"type": 1,
								"imageUrl": null
							}
						]
					}
				],
				answers: {},
				yxzyList: [],
				yxzy_ids: [],
				showSubmit: false,
				isCurrentQuestionAnswered: false
			}
		},
		onLoad(options) {
			if (options.ver) {
				this.ver = options.ver
				if (options.answerNo) {
					this.answerNo = options.answerNo
				}
				this.getSubject()
				this.getAnser()
				// this.getquestionList()
			}
		},
		onShow() {},
		onPageScroll(e) {},
		mounted() {},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top
		},
		methods: {
			getAnser() {
				let obj = {
					1: [15],
					2: [33],
					3: [34],
					4: [36, 37],
					5: "600",
					6: [{
						score: 100
					}, {
						score: 100
					}, {
						score: 100
					}, {
						score: 100
					}, {
						score: 100
					}, {
						score: 100
					}],
					7: ['01102', '0345', '01121'],
					8: [66]
				}
				console.log(obj)
				let myarr = Object.values(obj)
				let keys = Object.keys(obj)
	
				console.log(keys)
				console.log(myarr)
				console.log(obj)
				this.questions.map((myitems, myindexs) => {
					if (myitems.questionChoiceDoS.length > 0) {
						myitems.questionChoiceDoS.map((newitems, newindexs) => {
							newitems.checked = false
							if (myitems.type == 1 && newitems.id == keys[myindexs]) {
								newitems.checked = true
							}
							return newitems
						})
					}
				})
				console.log(this.questions)
				this.currentQuestion = myarr.length
				this.answers = obj
			},
			getSubject() {
				let items = [{
						"id": "00345",
						"name": "医药卫生大类",
						"parentId": null,
						"hot": false,
						"children": [{
								"id": "01102",
								"name": "临床医学类",
								"parentId": "00345",
								"hot": false,
								"children": [{
										"id": "1102",
										"name": "临床医学",
										"parentId": "01102",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1103",
										"name": "口腔医学",
										"parentId": "01102",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0345",
								"name": "中医药类",
								"parentId": "00345",
								"hot": false,
								"children": [{
										"id": "1104",
										"name": "中医学",
										"parentId": "0345",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1116",
										"name": "中药学",
										"parentId": "0345",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1106",
										"name": "针灸推拿",
										"parentId": "0345",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1133",
										"name": "中医康复技术",
										"parentId": "0345",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1225",
										"name": "中医养生保健",
										"parentId": "0345",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1112",
										"name": "哈医学",
										"parentId": "0345",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1119",
										"name": "藏药学",
										"parentId": "0345",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1111",
										"name": "傣医学",
										"parentId": "0345",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1117",
										"name": "蒙药学",
										"parentId": "0345",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1105",
										"name": "中医骨伤",
										"parentId": "0345",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1108",
										"name": "藏医学",
										"parentId": "0345",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1107",
										"name": "蒙医学",
										"parentId": "0345",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1118",
										"name": "维药学",
										"parentId": "0345",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1110",
										"name": "维医学",
										"parentId": "0345",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "345",
										"name": "朝医学",
										"parentId": "0345",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1592",
										"name": "中药制药",
										"parentId": "0345",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1593",
										"name": "药膳与食疗",
										"parentId": "0345",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1591",
										"name": "中药材生产与加工",
										"parentId": "0345",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0347",
								"name": "健康管理与促进类",
								"parentId": "00345",
								"hot": false,
								"children": [{
										"id": "1236",
										"name": "心理咨询",
										"parentId": "0347",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1213",
										"name": "医学营养",
										"parentId": "0347",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1202",
										"name": "健康管理",
										"parentId": "0347",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1279",
										"name": "假肢与矫形器技术",
										"parentId": "0347",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1247",
										"name": "医疗设备应用技术",
										"parentId": "0347",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1280",
										"name": "老年保健与管理",
										"parentId": "0347",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1258",
										"name": "精密医疗器械技术",
										"parentId": "0347",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "347",
										"name": "医疗器械经营与管理",
										"parentId": "0347",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1595",
										"name": "婴幼儿托育服务与管理",
										"parentId": "0347",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1596",
										"name": "生殖健康管理",
										"parentId": "0347",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "01121",
								"name": "医学技术类",
								"parentId": "00345",
								"hot": false,
								"children": [{
										"id": "1124",
										"name": "医学美容技术",
										"parentId": "01121",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1126",
										"name": "卫生检验与检疫技术",
										"parentId": "01121",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1128",
										"name": "放射治疗技术",
										"parentId": "01121",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1123",
										"name": "医学影像技术",
										"parentId": "01121",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1122",
										"name": "医学生物技术",
										"parentId": "01121",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1121",
										"name": "医学检验技术",
										"parentId": "01121",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1125",
										"name": "口腔医学技术",
										"parentId": "01121",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1129",
										"name": "呼吸治疗技术",
										"parentId": "01121",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "01113",
								"name": "护理类",
								"parentId": "00345",
								"hot": false,
								"children": [{
										"id": "1113",
										"name": "护理",
										"parentId": "01113",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1114",
										"name": "助产",
										"parentId": "01113",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "01130",
								"name": "康复治疗类",
								"parentId": "00345",
								"hot": false,
								"children": [{
										"id": "1132",
										"name": "言语听觉康复技术",
										"parentId": "01130",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1278",
										"name": "康复辅助器具技术",
										"parentId": "01130",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1130",
										"name": "康复治疗技术",
										"parentId": "01130",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "01136",
								"name": "公共卫生与卫生管理类",
								"parentId": "00345",
								"hot": false,
								"children": [{
										"id": "1136",
										"name": "预防医学",
										"parentId": "01136",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1147",
										"name": "公共卫生管理",
										"parentId": "01136",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1158",
										"name": "卫生监督",
										"parentId": "01136",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1169",
										"name": "卫生信息管理",
										"parentId": "01136",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1594",
										"name": "健康大数据管理与服务",
										"parentId": "01136",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "01180",
								"name": "人口与计划生育类",
								"parentId": "00345",
								"hot": false,
								"children": [{
										"id": "1191",
										"name": "生殖健康服务与管理",
										"parentId": "01180",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1180",
										"name": "人口与家庭发展服务",
										"parentId": "01180",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "01115",
								"name": "药学类",
								"parentId": "00345",
								"hot": false,
								"children": [{
									"id": "1115",
									"name": "药学",
									"parentId": "01115",
									"hot": false,
									"children": null,
									"universityList": null
								}],
								"universityList": null
							},
							{
								"id": "01127",
								"name": "眼视光类",
								"parentId": "00345",
								"hot": false,
								"children": [{
										"id": "1127",
										"name": "眼视光技术",
										"parentId": "01127",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1597",
										"name": "眼视光仪器技术",
										"parentId": "01127",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1598",
										"name": "视觉训练与康复",
										"parentId": "01127",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							}
						],
						"universityList": null
					},
					{
						"id": "00983",
						"name": "交通运输大类",
						"parentId": null,
						"hot": false,
						"children": [{
								"id": "01026",
								"name": "航空运输类",
								"parentId": "00983",
								"hot": false,
								"children": [{
										"id": "1031",
										"name": "空中乘务",
										"parentId": "01026",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1036",
										"name": "飞机机电设备维修",
										"parentId": "01026",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1033",
										"name": "民航空中安全保卫",
										"parentId": "01026",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1035",
										"name": "机场运行",
										"parentId": "01026",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1027",
										"name": "民航通信技术",
										"parentId": "01026",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1043",
										"name": "航空物流",
										"parentId": "01026",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1029",
										"name": "直升机驾驶技术",
										"parentId": "01026",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1037",
										"name": "飞机电子设备维修",
										"parentId": "01026",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1045",
										"name": "通用航空航务技术",
										"parentId": "01026",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1044",
										"name": "通用航空器维修",
										"parentId": "01026",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1028",
										"name": "定翼机驾驶技术",
										"parentId": "01026",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1039",
										"name": "航空地面设备维修",
										"parentId": "01026",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1040",
										"name": "机场场务技术与管理",
										"parentId": "01026",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1038",
										"name": "飞机部件修理",
										"parentId": "01026",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1046",
										"name": "飞机结构修理",
										"parentId": "01026",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1041",
										"name": "航空油料",
										"parentId": "01026",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1032",
										"name": "民航安全技术管理",
										"parentId": "01026",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1026",
										"name": "民航运输",
										"parentId": "01026",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1568",
										"name": "民航运输服务",
										"parentId": "01026",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1569",
										"name": "机场运行服务与管理",
										"parentId": "01026",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0983",
								"name": "铁道运输类",
								"parentId": "00983",
								"hot": false,
								"children": [{
										"id": "983",
										"name": "铁道机车",
										"parentId": "0983",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "996",
										"name": "高速铁路客运乘务",
										"parentId": "0983",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "990",
										"name": "铁道信号自动控制",
										"parentId": "0983",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "991",
										"name": "铁道通信与信息化技术",
										"parentId": "0983",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "998",
										"name": "动车组检修技术",
										"parentId": "0983",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "987",
										"name": "铁道供电技术",
										"parentId": "0983",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "995",
										"name": "高速铁道工程技术",
										"parentId": "0983",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "994",
										"name": "铁路桥梁与隧道工程技术",
										"parentId": "0983",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "989",
										"name": "铁道机械化维修技术",
										"parentId": "0983",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "988",
										"name": "铁道工程技术",
										"parentId": "0983",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "984",
										"name": "铁道车辆",
										"parentId": "0983",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "992",
										"name": "铁道交通运营管理",
										"parentId": "0983",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1291",
										"name": "高铁综合维修技术",
										"parentId": "0983",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1556",
										"name": "高速铁路客运服务",
										"parentId": "0983",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1554",
										"name": "高速铁路综合维修技术",
										"parentId": "0983",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1552",
										"name": "铁道机车运用与维护",
										"parentId": "0983",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1549",
										"name": "高速铁路施工与维护",
										"parentId": "0983",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1553",
										"name": "铁道车辆技术",
										"parentId": "0983",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1550",
										"name": "铁道桥梁隧道工程技术",
										"parentId": "0983",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1551",
										"name": "铁道养路机械应用技术",
										"parentId": "0983",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0999",
								"name": "道路运输类",
								"parentId": "00983",
								"hot": false,
								"children": [{
										"id": "1000",
										"name": "道路桥梁工程技术",
										"parentId": "0999",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "999",
										"name": "智能交通技术运用",
										"parentId": "0999",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1007",
										"name": "汽车运用与维修技术",
										"parentId": "0999",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1011",
										"name": "新能源汽车运用与维修",
										"parentId": "0999",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1001",
										"name": "道路运输与路政管理",
										"parentId": "0999",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1004",
										"name": "工程机械运用技术",
										"parentId": "0999",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1002",
										"name": "道路养护与管理",
										"parentId": "0999",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1009",
										"name": "汽车车身维修技术",
										"parentId": "0999",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1006",
										"name": "交通枢纽运营管理",
										"parentId": "0999",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1010",
										"name": "汽车运用安全管理",
										"parentId": "0999",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1005",
										"name": "交通运营管理",
										"parentId": "0999",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1003",
										"name": "公路机械化施工技术",
										"parentId": "0999",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1557",
										"name": "道路与桥梁工程技术",
										"parentId": "0999",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1565",
										"name": "新能源汽车检测与维修技术",
										"parentId": "0999",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1562",
										"name": "智能交通技术",
										"parentId": "0999",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1564",
										"name": "汽车技术服务与营销",
										"parentId": "0999",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1559",
										"name": "智能工程机械运用技术",
										"parentId": "0999",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1561",
										"name": "道路工程造价",
										"parentId": "0999",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1563",
										"name": "道路运输管理",
										"parentId": "0999",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1560",
										"name": "道路工程检测技术",
										"parentId": "0999",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1558",
										"name": "道路机械化施工技术",
										"parentId": "0999",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "01012",
								"name": "水上运输类",
								"parentId": "00983",
								"hot": false,
								"children": [{
										"id": "1013",
										"name": "国际邮轮乘务管理",
										"parentId": "01012",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1014",
										"name": "船舶电子电气技术",
										"parentId": "01012",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1024",
										"name": "水路运输与海事管理",
										"parentId": "01012",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1018",
										"name": "港口与航道工程技术",
										"parentId": "01012",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1012",
										"name": "航海技术",
										"parentId": "01012",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1016",
										"name": "港口机械与自动控制",
										"parentId": "01012",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1023",
										"name": "海上救捞技术",
										"parentId": "01012",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1017",
										"name": "港口电气技术",
										"parentId": "01012",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1020",
										"name": "港口与航运管理",
										"parentId": "01012",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1025",
										"name": "集装箱运输管理",
										"parentId": "01012",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1022",
										"name": "轮机工程技术",
										"parentId": "01012",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1015",
										"name": "船舶检验",
										"parentId": "01012",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1702",
										"name": "港口机械与智能控制",
										"parentId": "01012",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1567",
										"name": "水路运输安全管理",
										"parentId": "01012",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1861",
										"name": "水上救捞技术",
										"parentId": "01012",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "01049",
								"name": "城市轨道交通类",
								"parentId": "00983",
								"hot": false,
								"children": [{
										"id": "1054",
										"name": "城市轨道交通工程技术",
										"parentId": "01049",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1049",
										"name": "城市轨道交通车辆技术",
										"parentId": "01049",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1050",
										"name": "城市轨道交通机电技术",
										"parentId": "01049",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1051",
										"name": "城市轨道交通通信信号技术",
										"parentId": "01049",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1052",
										"name": "城市轨道交通供配电技术",
										"parentId": "01049",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1055",
										"name": "城市轨道交通运营管理",
										"parentId": "01049",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1570",
										"name": "城市轨道车辆应用技术",
										"parentId": "01049",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "01056",
								"name": "邮政类",
								"parentId": "00983",
								"hot": false,
								"children": [{
										"id": "1057",
										"name": "快递运营管理",
										"parentId": "01056",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1056",
										"name": "邮政通信管理",
										"parentId": "01056",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1571",
										"name": "邮政快递运营管理",
										"parentId": "01056",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1572",
										"name": "邮政快递智能技术",
										"parentId": "01056",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "01047",
								"name": "管道运输类",
								"parentId": "00983",
								"hot": false,
								"children": [{
										"id": "1047",
										"name": "管道工程技术",
										"parentId": "01047",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1048",
										"name": "管道运输管理",
										"parentId": "01047",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							}
						],
						"universityList": null
					},
					{
						"id": "00837",
						"name": "装备制造大类",
						"parentId": null,
						"hot": false,
						"children": [{
								"id": "0837",
								"name": "机械设计制造类",
								"parentId": "00837",
								"hot": false,
								"children": [{
										"id": "856",
										"name": "工业设计",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "855",
										"name": "机械装备制造技术",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "857",
										"name": "工业工程技术",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "847",
										"name": "焊接技术与自动化",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "842",
										"name": "特种加工技术",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "849",
										"name": "理化测试与质检技术",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "848",
										"name": "机械产品检测检验技术",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "845",
										"name": "铸造技术",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "846",
										"name": "锻压技术",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "837",
										"name": "机械设计与制造",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "839",
										"name": "数控技术",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "838",
										"name": "机械制造与自动化",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "844",
										"name": "金属材料与热处理技术",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "850",
										"name": "模具设计与制造",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "853",
										"name": "电线电缆制造技术",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "854",
										"name": "内燃机制造与维修",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "840",
										"name": "精密机械技术",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "843",
										"name": "材料成型与控制技术",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1690",
										"name": "机械制造及自动化",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1495",
										"name": "数字化设计与制造技术",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1504",
										"name": "工业产品质量检测技术",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1496",
										"name": "现代铸造技术",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1691",
										"name": "材料成型及控制技术",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1499",
										"name": "智能焊接技术",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1502",
										"name": "智能光电制造技术",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1501",
										"name": "增材制造技术",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1497",
										"name": "现代锻压技术",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1500",
										"name": "工业材料表面处理技术",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1503",
										"name": "内燃机制造与应用技术",
										"parentId": "0837",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0890",
								"name": "航空装备类",
								"parentId": "00837",
								"hot": false,
								"children": [{
										"id": "900",
										"name": "无人机应用技术",
										"parentId": "0890",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "890",
										"name": "飞行器制造技术",
										"parentId": "0890",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "891",
										"name": "飞行器维修技术",
										"parentId": "0890",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "894",
										"name": "航空发动机维修技术",
										"parentId": "0890",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "892",
										"name": "航空发动机制造技术",
										"parentId": "0890",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "899",
										"name": "航空材料精密成型技术",
										"parentId": "0890",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "893",
										"name": "航空发动机装试技术",
										"parentId": "0890",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "896",
										"name": "飞机机载设备维修技术",
										"parentId": "0890",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "895",
										"name": "飞机机载设备制造技术",
										"parentId": "0890",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "901",
										"name": "导弹维修",
										"parentId": "0890",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "898",
										"name": "航空电子电气技术",
										"parentId": "0890",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1694",
										"name": "飞机机载设备装配调试技术",
										"parentId": "0890",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1525",
										"name": "航空装备表面处理技术",
										"parentId": "0890",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1526",
										"name": "导弹维修技术",
										"parentId": "0890",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1523",
										"name": "飞行器数字化装配技术",
										"parentId": "0890",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1521",
										"name": "飞行器数字化制造技术",
										"parentId": "0890",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1524",
										"name": "航空发动机装配调试技术",
										"parentId": "0890",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0880",
								"name": "船舶与海洋工程装备类",
								"parentId": "00837",
								"hot": false,
								"children": [{
										"id": "880",
										"name": "船舶工程技术",
										"parentId": "0880",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "887",
										"name": "海洋工程技术",
										"parentId": "0880",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "882",
										"name": "船舶电气工程技术",
										"parentId": "0880",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "881",
										"name": "船舶机械工程技术",
										"parentId": "0880",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "889",
										"name": "船舶动力工程技术",
										"parentId": "0880",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "885",
										"name": "游艇设计与制造",
										"parentId": "0880",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "888",
										"name": "船舶通信与导航",
										"parentId": "0880",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "883",
										"name": "船舶舾装工程技术",
										"parentId": "0880",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "884",
										"name": "船舶涂装工程技术",
										"parentId": "0880",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1519",
										"name": "邮轮内装技术",
										"parentId": "0880",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1517",
										"name": "船舶智能焊接技术",
										"parentId": "0880",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1518",
										"name": "船舶通信装备技术",
										"parentId": "0880",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1520",
										"name": "海洋工程装备技术",
										"parentId": "0880",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0866",
								"name": "自动化类",
								"parentId": "00837",
								"hot": false,
								"children": [{
										"id": "866",
										"name": "机电一体化技术",
										"parentId": "0866",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "876",
										"name": "工业机器人技术",
										"parentId": "0866",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "869",
										"name": "智能控制技术",
										"parentId": "0866",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "868",
										"name": "工业过程自动化技术",
										"parentId": "0866",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "871",
										"name": "工业自动化仪表",
										"parentId": "0866",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "867",
										"name": "电气自动化技术",
										"parentId": "0866",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "870",
										"name": "工业网络技术",
										"parentId": "0866",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "872",
										"name": "液压与气动技术",
										"parentId": "0866",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1506",
										"name": "智能机器人技术",
										"parentId": "0866",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1505",
										"name": "智能机电技术",
										"parentId": "0866",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1512",
										"name": "计量测试与应用技术",
										"parentId": "0866",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1508",
										"name": "工业互联网应用",
										"parentId": "0866",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1507",
										"name": "工业自动化仪表技术",
										"parentId": "0866",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0902",
								"name": "汽车制造类",
								"parentId": "00837",
								"hot": false,
								"children": [{
										"id": "909",
										"name": "新能源汽车技术",
										"parentId": "0902",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "903",
										"name": "汽车检测与维修技术",
										"parentId": "0902",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "907",
										"name": "汽车改装技术",
										"parentId": "0902",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "905",
										"name": "汽车造型技术",
										"parentId": "0902",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "906",
										"name": "汽车试验技术",
										"parentId": "0902",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "902",
										"name": "汽车制造与装配技术",
										"parentId": "0902",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "904",
										"name": "汽车电子技术",
										"parentId": "0902",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1695",
										"name": "汽车制造与试验技术",
										"parentId": "0902",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1696",
										"name": "汽车造型与改装技术",
										"parentId": "0902",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1527",
										"name": "智能网联汽车技术",
										"parentId": "0902",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0851",
								"name": "机电设备类",
								"parentId": "00837",
								"hot": false,
								"children": [{
										"id": "865",
										"name": "新能源装备技术",
										"parentId": "0851",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "873",
										"name": "电梯工程技术",
										"parentId": "0851",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "851",
										"name": "电机与电器技术",
										"parentId": "0851",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "864",
										"name": "光电制造与应用技术",
										"parentId": "0851",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "859",
										"name": "机电设备安装技术",
										"parentId": "0851",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "862",
										"name": "制冷与空调技术",
										"parentId": "0851",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "861",
										"name": "数控设备应用与维护",
										"parentId": "0851",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "860",
										"name": "机电设备维修与管理",
										"parentId": "0851",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "858",
										"name": "自动化生产设备应用",
										"parentId": "0851",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1692",
										"name": "智能制造装备技术",
										"parentId": "0851",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1693",
										"name": "机电设备技术",
										"parentId": "0851",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0877",
								"name": "轨道装备类",
								"parentId": "00837",
								"hot": false,
								"children": [{
										"id": "877",
										"name": "铁道机车车辆制造与维护",
										"parentId": "0877",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "878",
										"name": "铁道通信信号设备制造与维护",
										"parentId": "0877",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "879",
										"name": "铁道施工和养路机械制造与维护",
										"parentId": "0877",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1513",
										"name": "高速铁路动车组制造与维护",
										"parentId": "0877",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1514",
										"name": "城市轨道交通车辆制造与维护",
										"parentId": "0877",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1515",
										"name": "轨道交通通信信号设备制造与维护",
										"parentId": "0877",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1516",
										"name": "轨道交通工程机械制造与维护",
										"parentId": "0877",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							}
						],
						"universityList": null
					},
					{
						"id": "00288",
						"name": "文化艺术大类",
						"parentId": null,
						"hot": false,
						"children": [{
								"id": "0962",
								"name": "艺术设计类",
								"parentId": "00288",
								"hot": false,
								"children": [{
										"id": "1726",
										"name": "动漫设计",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1633",
										"name": "数字媒体艺术设计",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1610",
										"name": "视觉传播设计与制作",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1688",
										"name": "室内艺术设计",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1727",
										"name": "游戏设计",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1677",
										"name": "服装与服饰设计",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1644",
										"name": "产品艺术设计",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1731",
										"name": "摄影与摄像艺术",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1725",
										"name": "工艺美术品设计",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1730",
										"name": "美容美体艺术",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1724",
										"name": "首饰设计与工艺",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1699",
										"name": "展示艺术设计",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "962",
										"name": "服装陈列与展示设计",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1717",
										"name": "公共艺术设计",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1655",
										"name": "家具艺术设计",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1720",
										"name": "包装艺术设计",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1719",
										"name": "雕刻艺术设计",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1722",
										"name": "刺绣设计与工艺",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1721",
										"name": "陶瓷设计与工艺",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1723",
										"name": "玉器设计与工艺",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1666",
										"name": "皮具艺术设计",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1599",
										"name": "艺术设计",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1622",
										"name": "广告设计与制作",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1728",
										"name": "人物形象设计",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1710",
										"name": "环境艺术设计",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1732",
										"name": "美术",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1635",
										"name": "广告艺术设计",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1634",
										"name": "游戏艺术设计",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1631",
										"name": "视觉传达设计",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1636",
										"name": "雕塑设计",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1632",
										"name": "书画艺术",
										"parentId": "0962",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0288",
								"name": "表演艺术类",
								"parentId": "00288",
								"hot": false,
								"children": [{
										"id": "1734",
										"name": "戏剧影视表演",
										"parentId": "0288",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1744",
										"name": "现代流行音乐",
										"parentId": "0288",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1746",
										"name": "音乐制作",
										"parentId": "0288",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1738",
										"name": "音乐剧表演",
										"parentId": "0288",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1749",
										"name": "舞蹈编导",
										"parentId": "0288",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1752",
										"name": "舞台艺术设计与制作",
										"parentId": "0288",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1743",
										"name": "模特与礼仪",
										"parentId": "0288",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1747",
										"name": "钢琴伴奏",
										"parentId": "0288",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1735",
										"name": "歌舞表演",
										"parentId": "0288",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1741",
										"name": "国际标准舞",
										"parentId": "0288",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1750",
										"name": "戏曲导演",
										"parentId": "0288",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1737",
										"name": "曲艺表演",
										"parentId": "0288",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1739",
										"name": "舞蹈表演",
										"parentId": "0288",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1753",
										"name": "音乐表演",
										"parentId": "0288",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1742",
										"name": "服装表演",
										"parentId": "0288",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1733",
										"name": "表演艺术",
										"parentId": "0288",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1748",
										"name": "钢琴调律",
										"parentId": "0288",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1745",
										"name": "作曲技术",
										"parentId": "0288",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1736",
										"name": "戏曲表演",
										"parentId": "0288",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "288",
										"name": "音乐传播",
										"parentId": "0288",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1712",
										"name": "时尚表演与传播",
										"parentId": "0288",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1637",
										"name": "戏曲音乐",
										"parentId": "0288",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1638",
										"name": "现代魔术设计与表演",
										"parentId": "0288",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "01640",
								"name": "文化服务类",
								"parentId": "00288",
								"hot": false,
								"children": [{
										"id": "1765",
										"name": "文物修复与保护",
										"parentId": "01640",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1761",
										"name": "文化创意与策划",
										"parentId": "01640",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1764",
										"name": "公共文化服务与管理",
										"parentId": "01640",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1766",
										"name": "考古探掘技术",
										"parentId": "01640",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1767",
										"name": "文物博物馆服务与管理",
										"parentId": "01640",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1763",
										"name": "文化市场经营管理",
										"parentId": "01640",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1768",
										"name": "图书档案管理",
										"parentId": "01640",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1641",
										"name": "文物考古技术",
										"parentId": "01640",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1643",
										"name": "石窟寺保护技术",
										"parentId": "01640",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1640",
										"name": "文化产业经营与管理",
										"parentId": "01640",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1642",
										"name": "文物展示利用技术",
										"parentId": "01640",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "01639",
								"name": "民族文化艺术类",
								"parentId": "00288",
								"hot": false,
								"children": [{
										"id": "1759",
										"name": "少数民族古籍修复",
										"parentId": "01639",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1755",
										"name": "民族美术",
										"parentId": "01639",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1758",
										"name": "民族传统技艺",
										"parentId": "01639",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1756",
										"name": "民族服装与服饰",
										"parentId": "01639",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1754",
										"name": "民族表演艺术",
										"parentId": "01639",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1757",
										"name": "民族民居装饰",
										"parentId": "01639",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1760",
										"name": "中国少数民族语言文化",
										"parentId": "01639",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1639",
										"name": "民族服装与饰品",
										"parentId": "01639",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							}
						],
						"universityList": null
					},
					{
						"id": "00333",
						"name": "资源环境与安全大类",
						"parentId": null,
						"hot": false,
						"children": [{
								"id": "0334",
								"name": "环境保护类",
								"parentId": "00333",
								"hot": false,
								"children": [{
										"id": "709",
										"name": "环境监测与控制技术",
										"parentId": "0334",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "712",
										"name": "环境工程技术",
										"parentId": "0334",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "717",
										"name": "污染修复与生态工程技术",
										"parentId": "0334",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "713",
										"name": "环境信息技术",
										"parentId": "0334",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "720",
										"name": "资源综合利用与管理技术",
										"parentId": "0334",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "710",
										"name": "农村环境保护",
										"parentId": "0334",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "719",
										"name": "清洁生产与减排技术",
										"parentId": "0334",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "716",
										"name": "环境评价与咨询服务",
										"parentId": "0334",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "711",
										"name": "室内环境检测与控制技术",
										"parentId": "0334",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "714",
										"name": "核与辐射检测防护技术",
										"parentId": "0334",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "715",
										"name": "环境规划与管理",
										"parentId": "0334",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "334",
										"name": "水净化与安全技术",
										"parentId": "0334",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1683",
										"name": "环境监测技术",
										"parentId": "0334",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1446",
										"name": "资源综合利用技术",
										"parentId": "0334",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1441",
										"name": "生态保护技术",
										"parentId": "0334",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1442",
										"name": "生态环境大数据技术",
										"parentId": "0334",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1445",
										"name": "绿色低碳技术",
										"parentId": "0334",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1447",
										"name": "智能环保装备技术",
										"parentId": "0334",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1684",
										"name": "环境管理与评价",
										"parentId": "0334",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1444",
										"name": "生态环境修复技术",
										"parentId": "0334",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0671",
								"name": "测绘地理信息类",
								"parentId": "00333",
								"hot": false,
								"children": [{
										"id": "675",
										"name": "测绘地理信息技术",
										"parentId": "0671",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "676",
										"name": "地籍测绘与土地管理",
										"parentId": "0671",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "679",
										"name": "导航与位置服务",
										"parentId": "0671",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "682",
										"name": "国土测绘与规划",
										"parentId": "0671",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "678",
										"name": "测绘与地质工程技术",
										"parentId": "0671",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "680",
										"name": "地图制图与数字传播技术",
										"parentId": "0671",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "681",
										"name": "地理国情监测技术",
										"parentId": "0671",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "673",
										"name": "测绘工程技术",
										"parentId": "0671",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "671",
										"name": "工程测量技术",
										"parentId": "0671",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "672",
										"name": "摄影测量与遥感技术",
										"parentId": "0671",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "677",
										"name": "矿山测量",
										"parentId": "0671",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1435",
										"name": "无人机测绘技术",
										"parentId": "0671",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1434",
										"name": "国土空间规划与测绘",
										"parentId": "0671",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1436",
										"name": "空间数字建模与应用技术",
										"parentId": "0671",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0721",
								"name": "安全类",
								"parentId": "00333",
								"hot": false,
								"children": [{
										"id": "724",
										"name": "安全技术与管理",
										"parentId": "0721",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "721",
										"name": "安全健康与环保",
										"parentId": "0721",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "725",
										"name": "工程安全评价与监理",
										"parentId": "0721",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "726",
										"name": "安全生产监测监控",
										"parentId": "0721",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "727",
										"name": "职业卫生技术与管理",
										"parentId": "0721",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "722",
										"name": "化工安全技术",
										"parentId": "0721",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "723",
										"name": "救援技术",
										"parentId": "0721",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1685",
										"name": "职业健康安全技术",
										"parentId": "0721",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1448",
										"name": "安全智能监测技术",
										"parentId": "0721",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1450",
										"name": "消防救援技术",
										"parentId": "0721",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1449",
										"name": "应急救援技术",
										"parentId": "0721",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1451",
										"name": "森林草原防火技术",
										"parentId": "0721",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0690",
								"name": "煤炭类",
								"parentId": "00333",
								"hot": false,
								"children": [{
										"id": "692",
										"name": "矿山机电技术",
										"parentId": "0690",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "693",
										"name": "矿井通风与安全",
										"parentId": "0690",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "698",
										"name": "煤化分析与检验",
										"parentId": "0690",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "694",
										"name": "综合机械化采煤",
										"parentId": "0690",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "699",
										"name": "煤层气采输技术",
										"parentId": "0690",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "690",
										"name": "煤矿开采技术",
										"parentId": "0690",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "691",
										"name": "矿井建设",
										"parentId": "0690",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "695",
										"name": "选煤技术",
										"parentId": "0690",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "700",
										"name": "矿井运输与提升",
										"parentId": "0690",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "697",
										"name": "煤炭深加工与利用",
										"parentId": "0690",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1681",
										"name": "煤炭清洁利用技术",
										"parentId": "0690",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1679",
										"name": "煤矿智能开采技术",
										"parentId": "0690",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1439",
										"name": "通风技术与安全管理",
										"parentId": "0690",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1680",
										"name": "矿山机电与智能装备",
										"parentId": "0690",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1438",
										"name": "矿井建设工程技术",
										"parentId": "0690",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0333",
								"name": "资源勘查类",
								"parentId": "00333",
								"hot": false,
								"children": [{
										"id": "656",
										"name": "国土资源调查与管理",
										"parentId": "0333",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "660",
										"name": "宝玉石鉴定与加工",
										"parentId": "0333",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "657",
										"name": "地质调查与矿产普查",
										"parentId": "0333",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "658",
										"name": "矿产地质与勘查",
										"parentId": "0333",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "659",
										"name": "岩矿分析与鉴定",
										"parentId": "0333",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "661",
										"name": "煤田地质与勘查技术",
										"parentId": "0333",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "333",
										"name": "权籍信息化管理",
										"parentId": "0333",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1427",
										"name": "生态地质调查",
										"parentId": "0333",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1429",
										"name": "煤田地质勘查",
										"parentId": "0333",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1428",
										"name": "矿产地质勘查",
										"parentId": "0333",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0662",
								"name": "地质类",
								"parentId": "00333",
								"hot": false,
								"children": [{
										"id": "669",
										"name": "环境地质工程",
										"parentId": "0662",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "667",
										"name": "地球物理勘探技术",
										"parentId": "0662",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "668",
										"name": "地质灾害调查与防治",
										"parentId": "0662",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "666",
										"name": "矿山地质",
										"parentId": "0662",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "664",
										"name": "水文与工程地质",
										"parentId": "0662",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "665",
										"name": "钻探技术",
										"parentId": "0662",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "670",
										"name": "岩土工程技术",
										"parentId": "0662",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "662",
										"name": "工程地质勘查",
										"parentId": "0662",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1432",
										"name": "城市地质勘查",
										"parentId": "0662",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1430",
										"name": "钻探工程技术",
										"parentId": "0662",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0683",
								"name": "石油与天然气类",
								"parentId": "00333",
								"hot": false,
								"children": [{
										"id": "687",
										"name": "油气地质勘探技术",
										"parentId": "0683",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "689",
										"name": "石油工程技术",
										"parentId": "0683",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "686",
										"name": "油气储运技术",
										"parentId": "0683",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "683",
										"name": "钻井技术",
										"parentId": "0683",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "688",
										"name": "油田化学应用技术",
										"parentId": "0683",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "684",
										"name": "油气开采技术",
										"parentId": "0683",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1437",
										"name": "油气智能开采技术",
										"parentId": "0683",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0701",
								"name": "金属与非金属矿类",
								"parentId": "00333",
								"hot": false,
								"children": [{
										"id": "701",
										"name": "金属与非金属矿开采技术",
										"parentId": "0701",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "703",
										"name": "矿业装备维护技术",
										"parentId": "0701",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "702",
										"name": "矿物加工技术",
										"parentId": "0701",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1682",
										"name": "矿山智能开采技术",
										"parentId": "0701",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0704",
								"name": "气象类",
								"parentId": "00333",
								"hot": false,
								"children": [{
										"id": "706",
										"name": "应用气象技术",
										"parentId": "0704",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "704",
										"name": "大气科学技术",
										"parentId": "0704",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "705",
										"name": "大气探测技术",
										"parentId": "0704",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "708",
										"name": "防雷技术",
										"parentId": "0704",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1440",
										"name": "雷电防护技术",
										"parentId": "0704",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							}
						],
						"universityList": null
					},
					{
						"id": "00337",
						"name": "能源动力与材料大类",
						"parentId": null,
						"hot": false,
						"children": [{
								"id": "0337",
								"name": "电力技术类",
								"parentId": "00337",
								"hot": false,
								"children": [{
										"id": "741",
										"name": "分布式发电与微电网技术",
										"parentId": "0337",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "733",
										"name": "电力系统继电保护与自动化技术",
										"parentId": "0337",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "736",
										"name": "电力客户服务与管理",
										"parentId": "0337",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "737",
										"name": "水电站与电力网",
										"parentId": "0337",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "734",
										"name": "水电站机电设备与自动化",
										"parentId": "0337",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "739",
										"name": "农业电气化技术",
										"parentId": "0337",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "738",
										"name": "电源变换技术与应用",
										"parentId": "0337",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "731",
										"name": "电力系统自动化技术",
										"parentId": "0337",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "728",
										"name": "发电厂及电力系统",
										"parentId": "0337",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "730",
										"name": "供用电技术",
										"parentId": "0337",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "732",
										"name": "高压输配电线路施工运行与维护",
										"parentId": "0337",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "735",
										"name": "电网监控技术",
										"parentId": "0337",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "337",
										"name": "机场电工技术",
										"parentId": "0337",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1452",
										"name": "水电站与电力网技术",
										"parentId": "0337",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1455",
										"name": "电力系统继电保护技术",
										"parentId": "0337",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1456",
										"name": "输配电工程技术",
										"parentId": "0337",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1453",
										"name": "分布式发电与智能微电网技术",
										"parentId": "0337",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0748",
								"name": "新能源发电工程类",
								"parentId": "00337",
								"hot": false,
								"children": [{
										"id": "748",
										"name": "风力发电工程技术",
										"parentId": "0748",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1076",
										"name": "光伏工程技术",
										"parentId": "0748",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "753",
										"name": "光伏发电技术与应用",
										"parentId": "0748",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "749",
										"name": "风电系统运行与维护",
										"parentId": "0748",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "750",
										"name": "生物质能应用技术",
										"parentId": "0748",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "755",
										"name": "节电技术与管理",
										"parentId": "0748",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "754",
										"name": "工业节能技术",
										"parentId": "0748",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "757",
										"name": "农村能源与环境技术",
										"parentId": "0748",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1461",
										"name": "新能源材料应用技术",
										"parentId": "0748",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1290",
										"name": "氢能技术应用",
										"parentId": "0748",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0769",
								"name": "非金属材料类",
								"parentId": "00337",
								"hot": false,
								"children": [{
										"id": "770",
										"name": "高分子材料工程技术",
										"parentId": "0769",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "771",
										"name": "复合材料工程技术",
										"parentId": "0769",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "773",
										"name": "光伏材料制备技术",
										"parentId": "0769",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "776",
										"name": "硅材料制备技术",
										"parentId": "0769",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "772",
										"name": "非金属矿物材料技术",
										"parentId": "0769",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "777",
										"name": "橡胶工程技术",
										"parentId": "0769",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "774",
										"name": "炭素加工技术",
										"parentId": "0769",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "769",
										"name": "材料工程技术",
										"parentId": "0769",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1470",
										"name": "炭材料工程技术",
										"parentId": "0769",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1471",
										"name": "橡胶智能制造技术",
										"parentId": "0769",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1469",
										"name": "航空复合材料成型与加工技术",
										"parentId": "0769",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1689",
										"name": "高分子材料智能制造技术",
										"parentId": "0769",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1468",
										"name": "复合材料智能制造技术",
										"parentId": "0769",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0778",
								"name": "建筑材料类",
								"parentId": "00337",
								"hot": false,
								"children": [{
										"id": "780",
										"name": "建筑装饰材料技术",
										"parentId": "0778",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "782",
										"name": "新型建筑材料技术",
										"parentId": "0778",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "779",
										"name": "建筑材料检测技术",
										"parentId": "0778",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "783",
										"name": "建筑材料生产与管理",
										"parentId": "0778",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "781",
										"name": "建筑材料设备应用",
										"parentId": "0778",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "778",
										"name": "建筑材料工程技术",
										"parentId": "0778",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1472",
										"name": "装配式建筑构件智能制造技术",
										"parentId": "0778",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0758",
								"name": "黑色金属材料类",
								"parentId": "00337",
								"hot": false,
								"children": [{
										"id": "758",
										"name": "黑色冶金技术",
										"parentId": "0758",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "761",
										"name": "金属材料质量检测",
										"parentId": "0758",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "759",
										"name": "轧钢工程技术",
										"parentId": "0758",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "760",
										"name": "钢铁冶金设备应用技术",
										"parentId": "0758",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "762",
										"name": "铁矿资源综合利用",
										"parentId": "0758",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1463",
										"name": "智能轧钢技术",
										"parentId": "0758",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1464",
										"name": "钢铁冶金设备维护",
										"parentId": "0758",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1686",
										"name": "金属材料检测技术",
										"parentId": "0758",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1462",
										"name": "钢铁智能冶金技术",
										"parentId": "0758",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0742",
								"name": "热能与发电工程类",
								"parentId": "00337",
								"hot": false,
								"children": [{
										"id": "744",
										"name": "核电站动力设备运行与维护",
										"parentId": "0742",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "756",
										"name": "太阳能光热技术与应用",
										"parentId": "0742",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "746",
										"name": "电厂化学与环保技术",
										"parentId": "0742",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "747",
										"name": "电厂热工自动化技术",
										"parentId": "0742",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "742",
										"name": "电厂热能动力装置",
										"parentId": "0742",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "743",
										"name": "城市热能应用技术",
										"parentId": "0742",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "745",
										"name": "火电厂集控运行",
										"parentId": "0742",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1458",
										"name": "地热开发技术",
										"parentId": "0742",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1457",
										"name": "热能动力工程技术",
										"parentId": "0742",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1459",
										"name": "发电运行技术",
										"parentId": "0742",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1460",
										"name": "热工自动化技术",
										"parentId": "0742",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0338",
								"name": "有色金属材料类",
								"parentId": "00337",
								"hot": false,
								"children": [{
										"id": "765",
										"name": "有色冶金技术",
										"parentId": "0338",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "768",
										"name": "金属精密成型技术",
										"parentId": "0338",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "766",
										"name": "有色冶金设备应用技术",
										"parentId": "0338",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "767",
										"name": "金属压力加工",
										"parentId": "0338",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1466",
										"name": "金属智能加工技术",
										"parentId": "0338",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "338",
										"name": "储能材料技术",
										"parentId": "0338",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1467",
										"name": "稀土材料技术",
										"parentId": "0338",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1687",
										"name": "有色金属智能冶金技术",
										"parentId": "0338",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							}
						],
						"universityList": null
					},
					{
						"id": "00348",
						"name": "财经商贸大类",
						"parentId": null,
						"hot": false,
						"children": [{
								"id": "0993",
								"name": "物流类",
								"parentId": "00348",
								"hot": false,
								"children": [{
										"id": "993",
										"name": "铁路物流管理",
										"parentId": "0993",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1403",
										"name": "物流信息技术",
										"parentId": "0993",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1443",
										"name": "冷链物流技术与管理",
										"parentId": "0993",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1401",
										"name": "物流工程技术",
										"parentId": "0993",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1021",
										"name": "港口物流管理",
										"parentId": "0993",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1420",
										"name": "物流金融管理",
										"parentId": "0993",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1454",
										"name": "采购与供应管理",
										"parentId": "0993",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1431",
										"name": "工程物流管理",
										"parentId": "0993",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1409",
										"name": "物流管理",
										"parentId": "0993",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1614",
										"name": "现代物流管理",
										"parentId": "0993",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1615",
										"name": "航空物流管理",
										"parentId": "0993",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1616",
										"name": "智能物流技术",
										"parentId": "0993",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1617",
										"name": "供应链运营",
										"parentId": "0993",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "01286",
								"name": "金融类",
								"parentId": "00348",
								"hot": false,
								"children": [{
										"id": "1286",
										"name": "金融管理",
										"parentId": "01286",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1303",
										"name": "互联网金融",
										"parentId": "01286",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1298",
										"name": "保险",
										"parentId": "01286",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1300",
										"name": "信用管理",
										"parentId": "01286",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1302",
										"name": "农村金融",
										"parentId": "01286",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1296",
										"name": "信托与租赁",
										"parentId": "01286",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1287",
										"name": "国际金融",
										"parentId": "01286",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1299",
										"name": "投资与理财",
										"parentId": "01286",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1288",
										"name": "证券与期货",
										"parentId": "01286",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1711",
										"name": "金融服务与管理",
										"parentId": "01286",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1600",
										"name": "金融科技应用",
										"parentId": "01286",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1603",
										"name": "证券实务",
										"parentId": "01286",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1602",
										"name": "财富管理",
										"parentId": "01286",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1601",
										"name": "保险实务",
										"parentId": "01286",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "01304",
								"name": "财务会计类",
								"parentId": "00348",
								"hot": false,
								"children": [{
										"id": "1305",
										"name": "会计",
										"parentId": "01304",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1306",
										"name": "审计",
										"parentId": "01304",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1307",
										"name": "会计信息管理",
										"parentId": "01304",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1304",
										"name": "财务管理",
										"parentId": "01304",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1605",
										"name": "大数据与会计",
										"parentId": "01304",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1604",
										"name": "大数据与财务管理",
										"parentId": "01304",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1606",
										"name": "大数据与审计",
										"parentId": "01304",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "01311",
								"name": "经济贸易类",
								"parentId": "00348",
								"hot": false,
								"children": [{
										"id": "1349",
										"name": "国际文化贸易",
										"parentId": "01311",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1344",
										"name": "服务外包",
										"parentId": "01311",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1322",
										"name": "国际经济与贸易",
										"parentId": "01311",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1311",
										"name": "国际贸易实务",
										"parentId": "01311",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1348",
										"name": "商务经纪与代理",
										"parentId": "01311",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1334",
										"name": "国际商务",
										"parentId": "01311",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1347",
										"name": "报关与国际货运",
										"parentId": "01311",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1346",
										"name": "经济信息管理",
										"parentId": "01311",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1609",
										"name": "关务与外贸服务",
										"parentId": "01311",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0348",
								"name": "工商管理类",
								"parentId": "00348",
								"hot": false,
								"children": [{
										"id": "1363",
										"name": "市场管理与服务",
										"parentId": "0348",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1374",
										"name": "品牌代理经营",
										"parentId": "0348",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1385",
										"name": "市场营销",
										"parentId": "0348",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1351",
										"name": "工商企业管理",
										"parentId": "0348",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1354",
										"name": "商检技术",
										"parentId": "0348",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1352",
										"name": "商务管理",
										"parentId": "0348",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1355",
										"name": "连锁经营管理",
										"parentId": "0348",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "348",
										"name": "中小企业创业与经营",
										"parentId": "0348",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1611",
										"name": "连锁经营与管理",
										"parentId": "0348",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0349",
								"name": "电子商务类",
								"parentId": "00348",
								"hot": false,
								"children": [{
										"id": "1398",
										"name": "电子商务",
										"parentId": "0349",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1400",
										"name": "网络营销",
										"parentId": "0349",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1399",
										"name": "移动商务",
										"parentId": "0349",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "349",
										"name": "商务数据分析与应用",
										"parentId": "0349",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "425",
										"name": "跨境电子商务",
										"parentId": "0349",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1612",
										"name": "网络营销与直播电商",
										"parentId": "0349",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1613",
										"name": "农村电子商务",
										"parentId": "0349",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "01395",
								"name": "市场营销类",
								"parentId": "00348",
								"hot": false,
								"children": [{
										"id": "1396",
										"name": "广告策划与营销",
										"parentId": "01395",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1395",
										"name": "汽车营销与服务",
										"parentId": "01395",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1397",
										"name": "茶艺与茶叶营销",
										"parentId": "01395",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "01308",
								"name": "统计类",
								"parentId": "00348",
								"hot": false,
								"children": [{
										"id": "1309",
										"name": "统计与会计核算",
										"parentId": "01308",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1308",
										"name": "信息统计与分析",
										"parentId": "01308",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1607",
										"name": "统计与大数据分析",
										"parentId": "01308",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1608",
										"name": "市场调查与统计分析",
										"parentId": "01308",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "01281",
								"name": "财政税务类",
								"parentId": "00348",
								"hot": false,
								"children": [{
										"id": "1285",
										"name": "政府采购管理",
										"parentId": "01281",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1281",
										"name": "财政",
										"parentId": "01281",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1283",
										"name": "税务",
										"parentId": "01281",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1284",
										"name": "资产评估与管理",
										"parentId": "01281",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1709",
										"name": "财税大数据应用",
										"parentId": "01281",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							}
						],
						"universityList": null
					},
					{
						"id": "00290",
						"name": "农林牧渔大类",
						"parentId": null,
						"hot": false,
						"children": [{
								"id": "0331",
								"name": "农业类",
								"parentId": "00290",
								"hot": false,
								"children": [{
										"id": "603",
										"name": "现代农业技术",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "604",
										"name": "休闲农业",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "605",
										"name": "生态农业技术",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "611",
										"name": "烟草栽培与加工",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "608",
										"name": "植物保护与检疫技术",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "602",
										"name": "设施农业与装备",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "614",
										"name": "绿色食品生产与检验",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "613",
										"name": "农产品加工与质量检测",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "609",
										"name": "茶树栽培与茶叶加工",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "620",
										"name": "农村经营管理",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "616",
										"name": "农产品流通与管理",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "617",
										"name": "农业装备应用技术",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "612",
										"name": "棉花加工与经营管理",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "615",
										"name": "农资营销与服务",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "606",
										"name": "园艺技术",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "601",
										"name": "种子生产与经营",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "600",
										"name": "作物生产技术",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "619",
										"name": "农业经济管理",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "610",
										"name": "中草药栽培技术",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1407",
										"name": "烟草栽培与加工技术",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "331",
										"name": "食用菌生产与加工",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1404",
										"name": "作物生产与经营管理",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1674",
										"name": "现代农业经济管理",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1412",
										"name": "绿色食品生产技术",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1406",
										"name": "中草药栽培与加工技术",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1405",
										"name": "茶叶生产与加工技术",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1413",
										"name": "休闲农业经营与管理",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1410",
										"name": "食用菌生产与加工技术",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1411",
										"name": "现代农业装备应用技术",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1408",
										"name": "饲草生产技术",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1414",
										"name": "农村新型经济组织管理",
										"parentId": "0331",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0332",
								"name": "林业类",
								"parentId": "00290",
								"hot": false,
								"children": [{
										"id": "626",
										"name": "野生动物资源保护与利用",
										"parentId": "0332",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "625",
										"name": "野生植物资源保护与利用",
										"parentId": "0332",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "633",
										"name": "林业信息技术与管理",
										"parentId": "0332",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "628",
										"name": "森林防火指挥与通讯",
										"parentId": "0332",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "646",
										"name": "草业技术",
										"parentId": "0332",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "624",
										"name": "经济林培育与利用",
										"parentId": "0332",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "632",
										"name": "林业调查与信息处理",
										"parentId": "0332",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "621",
										"name": "林业技术",
										"parentId": "0332",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "622",
										"name": "园林技术",
										"parentId": "0332",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "627",
										"name": "森林生态旅游",
										"parentId": "0332",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "631",
										"name": "木材加工技术",
										"parentId": "0332",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "623",
										"name": "森林资源保护",
										"parentId": "0332",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "630",
										"name": "自然保护区建设与管理",
										"parentId": "0332",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1676",
										"name": "林业信息技术应用",
										"parentId": "0332",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1415",
										"name": "花卉生产与花艺",
										"parentId": "0332",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "332",
										"name": "木工设备应用技术",
										"parentId": "0332",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1422",
										"name": "木业产品设计与制造",
										"parentId": "0332",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1421",
										"name": "木业智能装备应用技术",
										"parentId": "0332",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1419",
										"name": "森林生态旅游与康养",
										"parentId": "0332",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1675",
										"name": "野生动植物资源保护与利用",
										"parentId": "0332",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1417",
										"name": "林草生态保护与修复",
										"parentId": "0332",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1418",
										"name": "自然保护地建设与管理",
										"parentId": "0332",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1416",
										"name": "森林和草原资源保护",
										"parentId": "0332",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0290",
								"name": "畜牧业类",
								"parentId": "00290",
								"hot": false,
								"children": [{
										"id": "634",
										"name": "畜牧兽医",
										"parentId": "0290",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "639",
										"name": "宠物养护与驯导",
										"parentId": "0290",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "636",
										"name": "动物药学",
										"parentId": "0290",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "638",
										"name": "动物医学检验技术",
										"parentId": "0290",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "644",
										"name": "畜牧工程技术",
										"parentId": "0290",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "648",
										"name": "畜牧业经济管理",
										"parentId": "0290",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "647",
										"name": "养蜂与蜂产品加工",
										"parentId": "0290",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "635",
										"name": "动物医学",
										"parentId": "0290",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "641",
										"name": "实验动物技术",
										"parentId": "0290",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "637",
										"name": "动物防疫与检疫",
										"parentId": "0290",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "643",
										"name": "特种动物养殖",
										"parentId": "0290",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "642",
										"name": "饲料与动物营养",
										"parentId": "0290",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "645",
										"name": "蚕桑技术",
										"parentId": "0290",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "290",
										"name": "宠物临床诊疗技术",
										"parentId": "0290",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1424",
										"name": "宠物医疗技术",
										"parentId": "0290",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1678",
										"name": "特种动物养殖技术",
										"parentId": "0290",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1423",
										"name": "中兽医",
										"parentId": "0290",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1425",
										"name": "畜禽智能化养殖",
										"parentId": "0290",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1426",
										"name": "动物营养与饲料",
										"parentId": "0290",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0649",
								"name": "渔业类",
								"parentId": "00290",
								"hot": false,
								"children": [{
										"id": "654",
										"name": "水生动物医学",
										"parentId": "0649",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "650",
										"name": "海洋渔业技术",
										"parentId": "0649",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "653",
										"name": "水族科学与技术",
										"parentId": "0649",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "655",
										"name": "渔业经济管理",
										"parentId": "0649",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "649",
										"name": "水产养殖技术",
										"parentId": "0649",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							}
						],
						"universityList": null
					},
					{
						"id": "00784",
						"name": "土木建筑大类",
						"parentId": null,
						"hot": false,
						"children": [{
								"id": "0784",
								"name": "建筑设计类",
								"parentId": "00784",
								"hot": false,
								"children": [{
										"id": "784",
										"name": "建筑设计",
										"parentId": "0784",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "788",
										"name": "建筑室内设计",
										"parentId": "0784",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "789",
										"name": "风景园林设计",
										"parentId": "0784",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "787",
										"name": "古建筑工程技术",
										"parentId": "0784",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "791",
										"name": "建筑动画与模型制作",
										"parentId": "0784",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "790",
										"name": "园林工程技术",
										"parentId": "0784",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "785",
										"name": "建筑装饰工程技术",
										"parentId": "0784",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1473",
										"name": "建筑动画技术",
										"parentId": "0784",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0806",
								"name": "建设工程管理类",
								"parentId": "00784",
								"hot": false,
								"children": [{
										"id": "807",
										"name": "工程造价",
										"parentId": "0806",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "806",
										"name": "建设工程管理",
										"parentId": "0806",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "811",
										"name": "建设工程监理",
										"parentId": "0806",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "810",
										"name": "建设项目信息化管理",
										"parentId": "0806",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "809",
										"name": "建筑经济管理",
										"parentId": "0806",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1479",
										"name": "建筑经济信息化管理",
										"parentId": "0806",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0795",
								"name": "土建施工类",
								"parentId": "00784",
								"hot": false,
								"children": [{
										"id": "795",
										"name": "建筑工程技术",
										"parentId": "0795",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "798",
										"name": "土木工程检测技术",
										"parentId": "0795",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "796",
										"name": "地下与隧道工程技术",
										"parentId": "0795",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "799",
										"name": "建筑钢结构工程技术",
										"parentId": "0795",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1477",
										"name": "智能建造技术",
										"parentId": "0795",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1475",
										"name": "装配式建筑工程技术",
										"parentId": "0795",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0792",
								"name": "城乡规划与管理类",
								"parentId": "00784",
								"hot": false,
								"children": [{
										"id": "792",
										"name": "城乡规划",
										"parentId": "0792",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "794",
										"name": "城市信息化管理",
										"parentId": "0792",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "793",
										"name": "村镇建设与管理",
										"parentId": "0792",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1474",
										"name": "智慧城市管理技术",
										"parentId": "0792",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0800",
								"name": "建筑设备类",
								"parentId": "00784",
								"hot": false,
								"children": [{
										"id": "803",
										"name": "建筑智能化工程技术",
										"parentId": "0800",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "805",
										"name": "消防工程技术",
										"parentId": "0800",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "800",
										"name": "建筑设备工程技术",
										"parentId": "0800",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "804",
										"name": "工业设备安装工程技术",
										"parentId": "0800",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "801",
										"name": "供热通风与空调工程技术",
										"parentId": "0800",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "802",
										"name": "建筑电气工程技术",
										"parentId": "0800",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1478",
										"name": "建筑消防技术",
										"parentId": "0800",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0816",
								"name": "房地产类",
								"parentId": "00784",
								"hot": false,
								"children": [{
										"id": "816",
										"name": "房地产经营与管理",
										"parentId": "0816",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "817",
										"name": "房地产检测与估价",
										"parentId": "0816",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "818",
										"name": "物业管理",
										"parentId": "0816",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1483",
										"name": "现代物业管理",
										"parentId": "0816",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1482",
										"name": "房地产智能检测与估价",
										"parentId": "0816",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0812",
								"name": "市政工程类",
								"parentId": "00784",
								"hot": false,
								"children": [{
										"id": "815",
										"name": "环境卫生工程技术",
										"parentId": "0812",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "812",
										"name": "市政工程技术",
										"parentId": "0812",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "814",
										"name": "给排水工程技术",
										"parentId": "0812",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "813",
										"name": "城市燃气工程技术",
										"parentId": "0812",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1480",
										"name": "市政管网智能检测与维护",
										"parentId": "0812",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1481",
										"name": "城市环境工程技术",
										"parentId": "0812",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							}
						],
						"universityList": null
					},
					{
						"id": "004",
						"name": "教育与体育大类",
						"parentId": null,
						"hot": false,
						"children": [{
								"id": "04",
								"name": "教育类",
								"parentId": "004",
								"hot": false,
								"children": [{
										"id": "1797",
										"name": "小学教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1796",
										"name": "学前教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1794",
										"name": "早期教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1798",
										"name": "语文教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "13",
										"name": "心理健康教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "6",
										"name": "体育教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "10",
										"name": "特殊教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1800",
										"name": "英语教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "4",
										"name": "音乐教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1799",
										"name": "数学教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "5",
										"name": "美术教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "7",
										"name": "思想政治教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1805",
										"name": "地理教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "12",
										"name": "现代教育技术",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "9",
										"name": "艺术教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "8",
										"name": "舞蹈教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "11",
										"name": "科学教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1804",
										"name": "历史教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1802",
										"name": "化学教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1801",
										"name": "物理教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1803",
										"name": "生物教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1715",
										"name": "小学科学教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1652",
										"name": "小学英语教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1651",
										"name": "小学数学教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1650",
										"name": "小学语文教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1653",
										"name": "小学道德与法治教育",
										"parentId": "04",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "034",
								"name": "体育类",
								"parentId": "004",
								"hot": false,
								"children": [{
										"id": "43",
										"name": "体育保健与康复",
										"parentId": "034",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "42",
										"name": "体育运营与管理",
										"parentId": "034",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "35",
										"name": "运动防护",
										"parentId": "034",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "38",
										"name": "休闲体育",
										"parentId": "034",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "44",
										"name": "健身指导与管理",
										"parentId": "034",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "39",
										"name": "高尔夫球运动与管理",
										"parentId": "034",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "41",
										"name": "体育艺术表演",
										"parentId": "034",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "294",
										"name": "电子竞技运动与管理",
										"parentId": "034",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "34",
										"name": "运动训练",
										"parentId": "034",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "37",
										"name": "社会体育",
										"parentId": "034",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "40",
										"name": "民族传统体育",
										"parentId": "034",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1295",
										"name": "冰雪设施运维与管理",
										"parentId": "034",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1656",
										"name": "运动健康指导",
										"parentId": "034",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1658",
										"name": "体能训练",
										"parentId": "034",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1659",
										"name": "冰雪运动与管理",
										"parentId": "034",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1657",
										"name": "运动数据分析",
										"parentId": "034",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "015",
								"name": "语言类",
								"parentId": "004",
								"hot": false,
								"children": [{
										"id": "31",
										"name": "应用外语",
										"parentId": "015",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "29",
										"name": "应用泰语",
										"parentId": "015",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "17",
										"name": "应用英语",
										"parentId": "015",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "16",
										"name": "商务英语",
										"parentId": "015",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "26",
										"name": "应用德语",
										"parentId": "015",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "24",
										"name": "应用法语",
										"parentId": "015",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "27",
										"name": "应用西班牙语",
										"parentId": "015",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "19",
										"name": "商务日语",
										"parentId": "015",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "15",
										"name": "汉语",
										"parentId": "015",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "20",
										"name": "应用日语",
										"parentId": "015",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "30",
										"name": "应用阿拉伯语",
										"parentId": "015",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "22",
										"name": "应用韩语",
										"parentId": "015",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "21",
										"name": "旅游日语",
										"parentId": "015",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "18",
										"name": "旅游英语",
										"parentId": "015",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "23",
										"name": "应用俄语",
										"parentId": "015",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "28",
										"name": "应用越南语",
										"parentId": "015",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1654",
										"name": "中文",
										"parentId": "015",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							}
						],
						"universityList": null
					},
					{
						"id": "00110",
						"name": "电子与信息大类",
						"parentId": null,
						"hot": false,
						"children": [{
								"id": "0343",
								"name": "计算机类",
								"parentId": "00110",
								"hot": false,
								"children": [{
										"id": "1079",
										"name": "计算机应用技术",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1080",
										"name": "计算机网络技术",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1083",
										"name": "软件技术",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1085",
										"name": "动漫制作技术",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1089",
										"name": "数字媒体应用技术",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1092",
										"name": "云计算技术与应用",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1090",
										"name": "信息安全与管理",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1093",
										"name": "电子商务技术",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1091",
										"name": "移动应用开发",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1082",
										"name": "计算机系统与维护",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1084",
										"name": "软件与信息服务",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1087",
										"name": "嵌入式技术与应用",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1088",
										"name": "数字展示技术",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1081",
										"name": "计算机信息管理",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "343",
										"name": "大数据技术与应用",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1707",
										"name": "大数据技术",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1289",
										"name": "人工智能技术服务",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1706",
										"name": "数字媒体技术",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "344",
										"name": "虚拟现实应用技术",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1578",
										"name": "人工智能技术应用",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1576",
										"name": "虚拟现实技术应用",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1575",
										"name": "信息安全技术应用",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1574",
										"name": "云计算技术应用",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1582",
										"name": "工业软件开发技术",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1581",
										"name": "区块链技术应用",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1580",
										"name": "工业互联网技术",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1579",
										"name": "嵌入式技术应用",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1583",
										"name": "密码技术应用",
										"parentId": "0343",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0110",
								"name": "通信类",
								"parentId": "00110",
								"hot": false,
								"children": [{
										"id": "1101",
										"name": "物联网工程技术",
										"parentId": "0110",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1099",
										"name": "通信工程设计与监理",
										"parentId": "0110",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1100",
										"name": "光通信技术",
										"parentId": "0110",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "110",
										"name": "电信服务与管理",
										"parentId": "0110",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1094",
										"name": "通信技术",
										"parentId": "0110",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1095",
										"name": "移动通信技术",
										"parentId": "0110",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1096",
										"name": "通信系统运行管理",
										"parentId": "0110",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1708",
										"name": "现代通信技术",
										"parentId": "0110",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1584",
										"name": "现代移动通信技术",
										"parentId": "0110",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1587",
										"name": "智能互联网络技术",
										"parentId": "0110",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1585",
										"name": "通信软件技术",
										"parentId": "0110",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1589",
										"name": "网络规划与优化技术",
										"parentId": "0110",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1586",
										"name": "卫星通信与导航技术",
										"parentId": "0110",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "01060",
								"name": "集成电路类",
								"parentId": "00110",
								"hot": false,
								"children": [{
										"id": "1060",
										"name": "微电子技术",
										"parentId": "01060",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1590",
										"name": "集成电路技术",
										"parentId": "01060",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							}
						],
						"universityList": null
					},
					{
						"id": "00289",
						"name": "轻工纺织大类",
						"parentId": null,
						"hot": false,
						"children": [{
								"id": "0289",
								"name": "轻化工类",
								"parentId": "00289",
								"hot": false,
								"children": [{
										"id": "934",
										"name": "化妆品技术",
										"parentId": "0289",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "938",
										"name": "乐器制造与维护",
										"parentId": "0289",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "939",
										"name": "陶瓷制造工艺",
										"parentId": "0289",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "936",
										"name": "皮具制作与工艺",
										"parentId": "0289",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "935",
										"name": "皮革加工技术",
										"parentId": "0289",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "933",
										"name": "家具设计与制造",
										"parentId": "0289",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "931",
										"name": "香料香精工艺",
										"parentId": "0289",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "928",
										"name": "高分子材料加工技术",
										"parentId": "0289",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "929",
										"name": "制浆造纸技术",
										"parentId": "0289",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "932",
										"name": "表面精饰工艺",
										"parentId": "0289",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "937",
										"name": "鞋类设计与工艺",
										"parentId": "0289",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "289",
										"name": "珠宝首饰技术与管理",
										"parentId": "0289",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1534",
										"name": "香料香精技术与工艺",
										"parentId": "0289",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1531",
										"name": "现代造纸技术",
										"parentId": "0289",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1532",
										"name": "陶瓷制造技术与工艺",
										"parentId": "0289",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0950",
								"name": "纺织服装类",
								"parentId": "00289",
								"hot": false,
								"children": [{
										"id": "960",
										"name": "服装设计与工艺",
										"parentId": "0950",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "958",
										"name": "纺织材料与应用",
										"parentId": "0950",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "957",
										"name": "家用纺织品设计",
										"parentId": "0950",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "954",
										"name": "纺织机电技术",
										"parentId": "0950",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "961",
										"name": "皮革服装制作与工艺",
										"parentId": "0950",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "953",
										"name": "染整技术",
										"parentId": "0950",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "950",
										"name": "现代纺织技术",
										"parentId": "0950",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "959",
										"name": "针织技术与针织服装",
										"parentId": "0950",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "955",
										"name": "纺织品检验与贸易",
										"parentId": "0950",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "951",
										"name": "丝绸技术",
										"parentId": "0950",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "956",
										"name": "纺织品设计",
										"parentId": "0950",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1537",
										"name": "现代非织造技术",
										"parentId": "0950",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1536",
										"name": "现代家用纺织品设计",
										"parentId": "0950",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1535",
										"name": "数字化染整技术",
										"parentId": "0950",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0945",
								"name": "印刷类",
								"parentId": "00289",
								"hot": false,
								"children": [{
										"id": "945",
										"name": "数字图文信息技术",
										"parentId": "0945",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "948",
										"name": "印刷媒体技术",
										"parentId": "0945",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "949",
										"name": "数字印刷技术",
										"parentId": "0945",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "947",
										"name": "印刷媒体设计与制作",
										"parentId": "0945",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "946",
										"name": "印刷设备应用技术",
										"parentId": "0945",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1698",
										"name": "印刷数字图文技术",
										"parentId": "0945",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0940",
								"name": "包装类",
								"parentId": "00289",
								"hot": false,
								"children": [{
										"id": "942",
										"name": "包装策划与设计",
										"parentId": "0940",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "940",
										"name": "包装工程技术",
										"parentId": "0940",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "944",
										"name": "食品包装技术",
										"parentId": "0940",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "943",
										"name": "包装设备应用技术",
										"parentId": "0940",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							}
						],
						"universityList": null
					},
					{
						"id": "0045",
						"name": "公安与司法大类",
						"parentId": null,
						"hot": false,
						"children": [{
								"id": "045",
								"name": "公安管理类",
								"parentId": "0045",
								"hot": false,
								"children": [{
										"id": "52",
										"name": "特警",
										"parentId": "045",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "53",
										"name": "警察管理",
										"parentId": "045",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "46",
										"name": "交通管理",
										"parentId": "045",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "48",
										"name": "信息网络安全监察",
										"parentId": "045",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "45",
										"name": "治安管理",
										"parentId": "045",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "54",
										"name": "公共安全管理",
										"parentId": "045",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "56",
										"name": "部队后勤管理",
										"parentId": "045",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "57",
										"name": "部队政治工作",
										"parentId": "045",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "50",
										"name": "边防检查",
										"parentId": "045",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "55",
										"name": "森林消防",
										"parentId": "045",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "51",
										"name": "边境管理",
										"parentId": "045",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "49",
										"name": "防火管理",
										"parentId": "045",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1660",
										"name": "道路交通管理",
										"parentId": "045",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1661",
										"name": "警务指挥与战术",
										"parentId": "045",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "081",
								"name": "司法技术类",
								"parentId": "0045",
								"hot": false,
								"children": [{
										"id": "87",
										"name": "戒毒矫治技术",
										"parentId": "081",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "88",
										"name": "职务犯罪预防与控制",
										"parentId": "081",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "81",
										"name": "刑事侦查技术",
										"parentId": "081",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "86",
										"name": "罪犯心理测量与矫正技术",
										"parentId": "081",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "84",
										"name": "司法鉴定技术",
										"parentId": "081",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "83",
										"name": "司法信息技术",
										"parentId": "081",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "85",
										"name": "司法信息安全",
										"parentId": "081",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "071",
								"name": "法律实务类",
								"parentId": "0045",
								"hot": false,
								"children": [{
										"id": "73",
										"name": "法律事务",
										"parentId": "071",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "74",
										"name": "检察事务",
										"parentId": "071",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "71",
										"name": "司法助理",
										"parentId": "071",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "72",
										"name": "法律文秘",
										"parentId": "071",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "075",
								"name": "法律执行类",
								"parentId": "0045",
								"hot": false,
								"children": [{
										"id": "79",
										"name": "社区矫正",
										"parentId": "075",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "77",
										"name": "行政执行",
										"parentId": "075",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "78",
										"name": "司法警务",
										"parentId": "075",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "75",
										"name": "刑事执行",
										"parentId": "075",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "76",
										"name": "民事执行",
										"parentId": "075",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "059",
								"name": "公安指挥类",
								"parentId": "0045",
								"hot": false,
								"children": [{
										"id": "61",
										"name": "通信指挥",
										"parentId": "059",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "63",
										"name": "参谋业务",
										"parentId": "059",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "59",
										"name": "警察指挥与战术",
										"parentId": "059",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "60",
										"name": "边防指挥",
										"parentId": "059",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "62",
										"name": "消防指挥",
										"parentId": "059",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "64",
										"name": "抢险救援",
										"parentId": "059",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "350",
										"name": "船艇指挥",
										"parentId": "059",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "067",
								"name": "侦查类",
								"parentId": "0045",
								"hot": false,
								"children": [{
										"id": "67",
										"name": "国内安全保卫",
										"parentId": "067",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "68",
										"name": "经济犯罪侦查",
										"parentId": "067",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "70",
										"name": "禁毒",
										"parentId": "067",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "292",
										"name": "刑事侦查",
										"parentId": "067",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1663",
										"name": "政治安全保卫",
										"parentId": "067",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "065",
								"name": "公安技术类",
								"parentId": "0045",
								"hot": false,
								"children": [{
										"id": "65",
										"name": "刑事科学技术",
										"parentId": "065",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "66",
										"name": "警犬技术",
										"parentId": "065",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1662",
										"name": "网络安全与执法",
										"parentId": "065",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "082",
								"name": "安全防范类",
								"parentId": "0045",
								"hot": false,
								"children": [{
										"id": "82",
										"name": "安全防范技术",
										"parentId": "082",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1664",
										"name": "安全保卫管理",
										"parentId": "082",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1665",
										"name": "智能安防运营管理",
										"parentId": "082",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							}
						],
						"universityList": null
					},
					{
						"id": "001645",
						"name": "新闻传播大类",
						"parentId": null,
						"hot": false,
						"children": [{
								"id": "01647",
								"name": "广播影视类",
								"parentId": "001645",
								"hot": false,
								"children": [{
										"id": "1779",
										"name": "播音与主持",
										"parentId": "01647",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1783",
										"name": "影视编导",
										"parentId": "01647",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1792",
										"name": "传播与策划",
										"parentId": "01647",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1780",
										"name": "广播影视节目制作",
										"parentId": "01647",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1793",
										"name": "媒体营销",
										"parentId": "01647",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1782",
										"name": "影视制片管理",
										"parentId": "01647",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1790",
										"name": "录音技术与艺术",
										"parentId": "01647",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1785",
										"name": "影视美术",
										"parentId": "01647",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1788",
										"name": "影视照明技术与艺术",
										"parentId": "01647",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1786",
										"name": "影视多媒体技术",
										"parentId": "01647",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1791",
										"name": "摄影摄像技术",
										"parentId": "01647",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1787",
										"name": "影视动画",
										"parentId": "01647",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1778",
										"name": "新闻采编与制作",
										"parentId": "01647",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1781",
										"name": "广播电视技术",
										"parentId": "01647",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1789",
										"name": "音像技术",
										"parentId": "01647",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1648",
										"name": "融媒体技术与运营",
										"parentId": "01647",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1714",
										"name": "全媒体广告策划与营销",
										"parentId": "01647",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1647",
										"name": "数字广播电视技术",
										"parentId": "01647",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1649",
										"name": "网络直播与运营",
										"parentId": "01647",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "01645",
								"name": "新闻出版类",
								"parentId": "001645",
								"hot": false,
								"children": [{
										"id": "1770",
										"name": "网络新闻与传播",
										"parentId": "01645",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1769",
										"name": "图文信息处理",
										"parentId": "01645",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1776",
										"name": "数字出版",
										"parentId": "01645",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1777",
										"name": "数字媒体设备管理",
										"parentId": "01645",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1772",
										"name": "出版商务",
										"parentId": "01645",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1774",
										"name": "出版与电脑编辑技术",
										"parentId": "01645",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1771",
										"name": "版面编辑与校对",
										"parentId": "01645",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1775",
										"name": "出版信息管理",
										"parentId": "01645",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1713",
										"name": "出版策划与编辑",
										"parentId": "01645",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1645",
										"name": "数字图文信息处理技术",
										"parentId": "01645",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1646",
										"name": "数字媒体设备应用与管理",
										"parentId": "01645",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							}
						],
						"universityList": null
					},
					{
						"id": "00287",
						"name": "食品药品与粮食大类",
						"parentId": null,
						"hot": false,
						"children": [{
								"id": "0287",
								"name": "药品与医疗器械类",
								"parentId": "00287",
								"hot": false,
								"children": [{
										"id": "972",
										"name": "药品生产技术",
										"parentId": "0287",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1277",
										"name": "康复工程技术",
										"parentId": "0287",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "975",
										"name": "药品质量与安全",
										"parentId": "0287",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1269",
										"name": "医疗器械维护与管理",
										"parentId": "0287",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "980",
										"name": "化妆品经营与管理",
										"parentId": "0287",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "971",
										"name": "中药生产与加工",
										"parentId": "0287",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "976",
										"name": "制药设备应用技术",
										"parentId": "0287",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "973",
										"name": "兽药制药技术",
										"parentId": "0287",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "977",
										"name": "药品经营与管理",
										"parentId": "0287",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "342",
										"name": "药物制剂技术",
										"parentId": "0287",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "340",
										"name": "生物制药技术",
										"parentId": "0287",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "287",
										"name": "食品药品监督管理",
										"parentId": "0287",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "341",
										"name": "中药制药技术",
										"parentId": "0287",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "339",
										"name": "化学制药技术",
										"parentId": "0287",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1540",
										"name": "智能医疗装备技术",
										"parentId": "0287",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1541",
										"name": "医用电子仪器技术",
										"parentId": "0287",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1546",
										"name": "化妆品质量与安全",
										"parentId": "0287",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1542",
										"name": "医用材料与应用",
										"parentId": "0287",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1545",
										"name": "保健食品质量与管理",
										"parentId": "0287",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1543",
										"name": "医疗器械经营与服务",
										"parentId": "0287",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0964",
								"name": "食品类",
								"parentId": "00287",
								"hot": false,
								"children": [{
										"id": "966",
										"name": "食品质量与安全",
										"parentId": "0964",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "970",
										"name": "食品营养与检测",
										"parentId": "0964",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "969",
										"name": "食品营养与卫生",
										"parentId": "0964",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "968",
										"name": "食品检测技术",
										"parentId": "0964",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "964",
										"name": "食品加工技术",
										"parentId": "0964",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "965",
										"name": "酿酒技术",
										"parentId": "0964",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "967",
										"name": "食品贮运与营销",
										"parentId": "0964",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1700",
										"name": "食品检验检测技术",
										"parentId": "0964",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1538",
										"name": "食品智能加工技术",
										"parentId": "0964",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1539",
										"name": "食品营养与健康",
										"parentId": "0964",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0978",
								"name": "食品药品管理类",
								"parentId": "00287",
								"hot": false,
								"children": [{
										"id": "978",
										"name": "药品服务与管理",
										"parentId": "0978",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "979",
										"name": "保健品开发与管理",
										"parentId": "0978",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0981",
								"name": "粮食类",
								"parentId": "00287",
								"hot": false,
								"children": [{
										"id": "981",
										"name": "粮食工程技术",
										"parentId": "0981",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1547",
										"name": "粮食工程技术与管理",
										"parentId": "0981",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1548",
										"name": "粮食储运与质量安全",
										"parentId": "0981",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0982",
								"name": "粮食储检类",
								"parentId": "00287",
								"hot": false,
								"children": [{
									"id": "982",
									"name": "粮油储藏与检测技术",
									"parentId": "0982",
									"hot": false,
									"children": null,
									"universityList": null
								}],
								"universityList": null
							}
						],
						"universityList": null
					},
					{
						"id": "00910",
						"name": "生物与化工大类",
						"parentId": null,
						"hot": false,
						"children": [{
								"id": "0915",
								"name": "化工技术类",
								"parentId": "00910",
								"hot": false,
								"children": [{
										"id": "917",
										"name": "石油化工技术",
										"parentId": "0915",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "920",
										"name": "精细化工技术",
										"parentId": "0915",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "918",
										"name": "高分子合成技术",
										"parentId": "0915",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "922",
										"name": "工业分析技术",
										"parentId": "0915",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "921",
										"name": "海洋化工技术",
										"parentId": "0915",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "924",
										"name": "化工自动化技术",
										"parentId": "0915",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "916",
										"name": "石油炼制技术",
										"parentId": "0915",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "926",
										"name": "烟花爆竹技术与管理",
										"parentId": "0915",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "923",
										"name": "化工装备技术",
										"parentId": "0915",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "927",
										"name": "煤化工技术",
										"parentId": "0915",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "925",
										"name": "涂装防护技术",
										"parentId": "0915",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "915",
										"name": "应用化工技术",
										"parentId": "0915",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1697",
										"name": "分析检验技术",
										"parentId": "0915",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1530",
										"name": "化工智能制造技术",
										"parentId": "0915",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0910",
								"name": "生物技术类",
								"parentId": "00910",
								"hot": false,
								"children": [{
										"id": "912",
										"name": "药品生物技术",
										"parentId": "0910",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "911",
										"name": "化工生物技术",
										"parentId": "0910",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "913",
										"name": "农业生物技术",
										"parentId": "0910",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "914",
										"name": "生物产品检验检疫",
										"parentId": "0910",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "910",
										"name": "食品生物技术",
										"parentId": "0910",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1529",
										"name": "生物信息技术",
										"parentId": "0910",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1528",
										"name": "绿色生物制造技术",
										"parentId": "0910",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							}
						],
						"universityList": null
					},
					{
						"id": "001058",
						"name": "电子信息大类",
						"parentId": null,
						"hot": false,
						"children": [{
							"id": "01058",
							"name": "电子信息类",
							"parentId": "001058",
							"hot": false,
							"children": [{
									"id": "1058",
									"name": "电子信息工程技术",
									"parentId": "01058",
									"hot": false,
									"children": null,
									"universityList": null
								},
								{
									"id": "1078",
									"name": "物联网应用技术",
									"parentId": "01058",
									"hot": false,
									"children": null,
									"universityList": null
								},
								{
									"id": "1062",
									"name": "智能终端技术与应用",
									"parentId": "01058",
									"hot": false,
									"children": null,
									"universityList": null
								},
								{
									"id": "1065",
									"name": "汽车智能技术",
									"parentId": "01058",
									"hot": false,
									"children": null,
									"universityList": null
								},
								{
									"id": "1068",
									"name": "电子电路设计与工艺",
									"parentId": "01058",
									"hot": false,
									"children": null,
									"universityList": null
								},
								{
									"id": "1074",
									"name": "光电技术应用",
									"parentId": "01058",
									"hot": false,
									"children": null,
									"universityList": null
								},
								{
									"id": "1072",
									"name": "声像工程技术",
									"parentId": "01058",
									"hot": false,
									"children": null,
									"universityList": null
								},
								{
									"id": "1063",
									"name": "智能监控技术应用",
									"parentId": "01058",
									"hot": false,
									"children": null,
									"universityList": null
								},
								{
									"id": "1069",
									"name": "电子制造技术与设备",
									"parentId": "01058",
									"hot": false,
									"children": null,
									"universityList": null
								},
								{
									"id": "1077",
									"name": "光电显示技术",
									"parentId": "01058",
									"hot": false,
									"children": null,
									"universityList": null
								},
								{
									"id": "1067",
									"name": "电子产品营销与服务",
									"parentId": "01058",
									"hot": false,
									"children": null,
									"universityList": null
								},
								{
									"id": "1061",
									"name": "智能产品开发",
									"parentId": "01058",
									"hot": false,
									"children": null,
									"universityList": null
								},
								{
									"id": "1059",
									"name": "应用电子技术",
									"parentId": "01058",
									"hot": false,
									"children": null,
									"universityList": null
								},
								{
									"id": "1070",
									"name": "电子测量技术与仪器",
									"parentId": "01058",
									"hot": false,
									"children": null,
									"universityList": null
								},
								{
									"id": "1073",
									"name": "移动互联应用技术",
									"parentId": "01058",
									"hot": false,
									"children": null,
									"universityList": null
								},
								{
									"id": "1066",
									"name": "电子产品质量检测",
									"parentId": "01058",
									"hot": false,
									"children": null,
									"universityList": null
								},
								{
									"id": "1071",
									"name": "电子工艺与管理",
									"parentId": "01058",
									"hot": false,
									"children": null,
									"universityList": null
								},
								{
									"id": "1292",
									"name": "集成电路技术应用",
									"parentId": "01058",
									"hot": false,
									"children": null,
									"universityList": null
								},
								{
									"id": "1705",
									"name": "智能产品开发与应用",
									"parentId": "01058",
									"hot": false,
									"children": null,
									"universityList": null
								},
								{
									"id": "1703",
									"name": "电子产品制造技术",
									"parentId": "01058",
									"hot": false,
									"children": null,
									"universityList": null
								},
								{
									"id": "1704",
									"name": "电子产品检测技术",
									"parentId": "01058",
									"hot": false,
									"children": null,
									"universityList": null
								},
								{
									"id": "1573",
									"name": "智能光电技术应用",
									"parentId": "01058",
									"hot": false,
									"children": null,
									"universityList": null
								},
								{
									"id": "1862",
									"name": "移动互联应用技术 ",
									"parentId": "01058",
									"hot": false,
									"children": null,
									"universityList": null
								}
							],
							"universityList": null
						}],
						"universityList": null
					},
					{
						"id": "0032",
						"name": "公共管理与服务大类",
						"parentId": null,
						"hot": false,
						"children": [{
								"id": "096",
								"name": "公共管理类",
								"parentId": "0032",
								"hot": false,
								"children": [{
										"id": "101",
										"name": "行政管理",
										"parentId": "096",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "99",
										"name": "网络舆情监测",
										"parentId": "096",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "104",
										"name": "知识产权管理",
										"parentId": "096",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "103",
										"name": "质量管理与认证",
										"parentId": "096",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "97",
										"name": "人力资源管理",
										"parentId": "096",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "96",
										"name": "民政管理",
										"parentId": "096",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "98",
										"name": "劳动与社会保障",
										"parentId": "096",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "100",
										"name": "公共事务管理",
										"parentId": "096",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1670",
										"name": "标准化技术",
										"parentId": "096",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1668",
										"name": "民政服务与管理",
										"parentId": "096",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1669",
										"name": "职业指导与服务",
										"parentId": "096",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0105",
								"name": "公共服务类",
								"parentId": "0032",
								"hot": false,
								"children": [{
										"id": "109",
										"name": "现代殡葬技术与管理",
										"parentId": "0105",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "106",
										"name": "家政服务与管理",
										"parentId": "0105",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "107",
										"name": "婚庆服务与管理",
										"parentId": "0105",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "105",
										"name": "老年服务与管理",
										"parentId": "0105",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "108",
										"name": "社区康复",
										"parentId": "0105",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "293",
										"name": "幼儿发展与健康管理",
										"parentId": "0105",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1672",
										"name": "智慧健康养老服务与管理",
										"parentId": "0105",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1297",
										"name": "陵园服务与管理",
										"parentId": "0105",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1671",
										"name": "现代家政服务与管理",
										"parentId": "0105",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1673",
										"name": "殡葬设备维护技术",
										"parentId": "0105",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "089",
								"name": "公共事业类",
								"parentId": "0032",
								"hot": false,
								"children": [{
										"id": "89",
										"name": "社会工作",
										"parentId": "089",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "95",
										"name": "人民武装",
										"parentId": "089",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "90",
										"name": "社会福利事业管理",
										"parentId": "089",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "93",
										"name": "社区管理与服务",
										"parentId": "089",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "92",
										"name": "青少年工作与管理",
										"parentId": "089",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "94",
										"name": "公共关系",
										"parentId": "089",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "351",
										"name": "公益慈善事业管理",
										"parentId": "089",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1667",
										"name": "党务工作",
										"parentId": "089",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "032",
								"name": "文秘类",
								"parentId": "0032",
								"hot": false,
								"children": [{
										"id": "33",
										"name": "文秘速录",
										"parentId": "032",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "32",
										"name": "文秘",
										"parentId": "032",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1716",
										"name": "现代文秘",
										"parentId": "032",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							}
						],
						"universityList": null
					},
					{
						"id": "00820",
						"name": "水利大类",
						"parentId": null,
						"hot": false,
						"children": [{
								"id": "0831",
								"name": "水利水电设备类",
								"parentId": "00820",
								"hot": false,
								"children": [{
										"id": "831",
										"name": "水电站动力设备",
										"parentId": "0831",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "834",
										"name": "水利机电设备运行与管理",
										"parentId": "0831",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "833",
										"name": "水电站运行与管理",
										"parentId": "0831",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "832",
										"name": "水电站电气设备",
										"parentId": "0831",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1490",
										"name": "水电站设备安装与管理",
										"parentId": "0831",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1492",
										"name": "水利机电设备智能管理",
										"parentId": "0831",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1491",
										"name": "水电站运行与智能管理",
										"parentId": "0831",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0823",
								"name": "水利工程与管理类",
								"parentId": "00820",
								"hot": false,
								"children": [{
										"id": "824",
										"name": "水利水电工程技术",
										"parentId": "0823",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "827",
										"name": "机电排灌工程技术",
										"parentId": "0823",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "825",
										"name": "水利水电工程管理",
										"parentId": "0823",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "823",
										"name": "水利工程",
										"parentId": "0823",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "826",
										"name": "水利水电建筑工程",
										"parentId": "0823",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "829",
										"name": "水务管理",
										"parentId": "0823",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "828",
										"name": "港口航道与治河工程",
										"parentId": "0823",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1488",
										"name": "治河与航道工程技术",
										"parentId": "0823",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1485",
										"name": "智慧水利技术",
										"parentId": "0823",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1486",
										"name": "水利水电工程智能管理",
										"parentId": "0823",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1489",
										"name": "智能水务管理",
										"parentId": "0823",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0820",
								"name": "水文水资源类",
								"parentId": "00820",
								"hot": false,
								"children": [{
										"id": "820",
										"name": "水文与水资源工程",
										"parentId": "0820",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "821",
										"name": "水文测报技术",
										"parentId": "0820",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "822",
										"name": "水政水资源管理",
										"parentId": "0820",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1484",
										"name": "水文与水资源技术",
										"parentId": "0820",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "0835",
								"name": "水土保持与水环境类",
								"parentId": "00820",
								"hot": false,
								"children": [{
										"id": "836",
										"name": "水环境监测与治理",
										"parentId": "0835",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "835",
										"name": "水土保持技术",
										"parentId": "0835",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1494",
										"name": "水生态修复技术",
										"parentId": "0835",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1493",
										"name": "水环境智能监测与治理",
										"parentId": "0835",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							}
						],
						"universityList": null
					},
					{
						"id": "001293",
						"name": "旅游大类",
						"parentId": null,
						"hot": false,
						"children": [{
								"id": "01533",
								"name": "餐饮类",
								"parentId": "001293",
								"hot": false,
								"children": [{
										"id": "1544",
										"name": "烹调工艺与营养",
										"parentId": "01533",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1566",
										"name": "中西面点工艺",
										"parentId": "01533",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1533",
										"name": "餐饮管理",
										"parentId": "01533",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1555",
										"name": "营养配餐",
										"parentId": "01533",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1577",
										"name": "西餐工艺",
										"parentId": "01533",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1629",
										"name": "烹饪工艺与营养",
										"parentId": "01533",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1630",
										"name": "西式烹饪工艺",
										"parentId": "01533",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1628",
										"name": "餐饮智能管理",
										"parentId": "01533",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							},
							{
								"id": "01293",
								"name": "旅游类",
								"parentId": "001293",
								"hot": false,
								"children": [{
										"id": "1522",
										"name": "休闲服务与管理",
										"parentId": "01293",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1465",
										"name": "旅游管理",
										"parentId": "01293",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1511",
										"name": "酒店管理",
										"parentId": "01293",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1487",
										"name": "旅行社经营管理",
										"parentId": "01293",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1588",
										"name": "会展策划与管理",
										"parentId": "01293",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1476",
										"name": "导游",
										"parentId": "01293",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1498",
										"name": "景区开发与管理",
										"parentId": "01293",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1293",
										"name": "研学旅行管理与服务",
										"parentId": "01293",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1624",
										"name": "葡萄酒文化与营销",
										"parentId": "01293",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1294",
										"name": "葡萄酒营销与服务",
										"parentId": "01293",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1620",
										"name": "酒店管理与数字化运营",
										"parentId": "01293",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1625",
										"name": "茶艺与茶文化",
										"parentId": "01293",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1619",
										"name": "定制旅行管理与服务",
										"parentId": "01293",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1627",
										"name": "智慧旅游技术应用",
										"parentId": "01293",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1623",
										"name": "民宿管理与运营",
										"parentId": "01293",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1618",
										"name": "旅行社经营与管理",
										"parentId": "01293",
										"hot": false,
										"children": null,
										"universityList": null
									},
									{
										"id": "1626",
										"name": "智慧景区开发与管理",
										"parentId": "01293",
										"hot": false,
										"children": null,
										"universityList": null
									}
								],
								"universityList": null
							}
						],
						"universityList": null
					}
				]

				items.map((item) => {
					item.text = item.name.substring(0, item.name.length - 2)
					item.badge = 'number'
					if (item.children.length > 0) {
						item.children.map((newitem) => {
							newitem.text = newitem.name
						})
					}
				})
				this.items = items
			},
			changemyItem(item) {
				item.checked = !item.checked
			},
			changeLeftNav(item) {
				this.leftNavList.map((items) => {
					items.checked = false
					if (items.id == item.id) {
						items.checked = true
					}
				})
				// item.checked = !item.checked
			},
			getRecordAnswer() {
				this.$apis.getRecordAnswer({
					answerNo: this.answerNo
				}).then((res) => {
					if (res.code == 0) {
						let obj = res.data
						let myarr = Object.values(obj)
						let keys = Object.keys(obj)
						keys.map((newitem, newindex) => {
							myarr.map((item, index) => {
								if (item.length == 1) {
									item = item[0]
									obj[newitem] = item
								} else {
									obj[newitem] = item
								}
							})
						})
						this.questions.map((myitems, myindexs) => {
							if (myitems.questionChoiceDoS.length > 0) {
								myitems.questionChoiceDoS.map((newitems, newindexs) => {
									if (myitems.type == 1 && newitems.id == obj[myindexs]) {
										newitems.checked = true
									} else {
										newitems.checked = false
									}
								})
							}
						})
						this.currentQuestion = myarr.length
						this.answers = obj
					}
				})
			},
			getquestionList() {
				this.$apis.getqList({
					type: this.ver
				}).then((res) => {
					if (res.code == 0) {
						res.data.map((item) => {
							if (item.questionChoiceDoS.length > 0) {
								item.questionChoiceDoS.map((newitem) => {
									newitem.checked = false
								})
							}
						})
						this.questions = res.data
						if (this.answerNo && this.answerNo != '-1') {
							this.$nextTick(() => {
								this.getRecordAnswer()
							})
						}
					}
				})
			},
			goHome() {
				uni.switchTab({
					url: '/pages/index/index'
				})
			},
			close() {
				this.show = false
			},
			open() {
				this.show = true
			},
			submitQuestionnaire() {
				this.show = true
				setTimeout(() => {
					uni.navigateTo({
						url: "/pages/plan/report"
					})
				}, 6000)

			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			},
			checkAnswerStatus(myval) {
				const currentQ = this.questions[this.currentQuestion];
				const answer = this.answers[currentQ.id];
				// console.log(this.currentQuestion)
				console.log(answer)
				console.log(this.answers)


				// console.log(currentQ)
				// console.log(this.answers)
				// console.log(this.answers[4])
				let isAnswered = false;
				if (currentQ.id == 4) {
					if (this.fkList.length == 2) {
						isAnswered = true;
					} else {
						isAnswered = false;
					}
				}
				if (currentQ.id == 7) {
					if (this.yxzyList.length <= 5) {
						isAnswered = true;
					} else {
						isAnswered = false;
					}
				}
				switch (currentQ.type) {
					case 1:
						isAnswered = !!answer;
						break;
					case 2:
						break;
					case 3:
						isAnswered = !!answer;
						break;
					default:
						isAnswered = true;
				}


				this.isCurrentQuestionAnswered = isAnswered;

				if (myval) {
					let arrs = []
					console.log(this)
					if (this.questions[this.currentQuestion].type == 1) {
						console.log('444')
						console.log(this.answers[this.questions[this.currentQuestion].id])
						arrs.push(this.answers[this.questions[this.currentQuestion].id])
					} else if (this.questions[this.currentQuestion].type == 2) {
						if (this.questions[this.currentQuestion].id == 7) {
							arrs = this.yxzy_ids
						} else {
							arrs = this.answers[this.questions[this.currentQuestion].id]
						}

					} else {
						if (this.questions[this.currentQuestion].id == 6) {
							arrs = this.fsList
						} else {
							arrs = []
						}

					}
					this.$apis.commitQuestion({
						type: this.ver,
						questionId: this.questions[this.currentQuestion].id,
						writeContent: this.questions[this.currentQuestion].type == 3 ? this.answers[this.questions[
							this.currentQuestion].id] : '',
						answerChoices: arrs,
						answerNo: this.answerNo
					}).then((res) => {
						if (res.code == 0) {
							this.answerNo = res.data
						}
					})
				}

			},
			onItem(ret) {
				console.log(ret);
			},
			bindChange(ids, key, popupRef) {
				this[key] = ids;

				this.$set(this.answers, 7, this[key]);
				this.checkAnswerStatus();
				console.log(this.yxzy_ids)
				if (popupRef) {
					this.$refs[popupRef].close();
				}
			},
			clickProvince(item, id) {
				// 修改为Vue的响应式数据更新方式
				this.$set(this.answers, id, item);
				this.checkAnswerStatus();
			},
			clickSex(item, id) {



				// 修改为Vue的响应式数据更新方式
				this.$set(this.answers, id, item);
				this.checkAnswerStatus();
			},
			// 在所有事件处理函数中调用检查方法
			handleSelect(e) {
				const {
					questionId
				} = e.currentTarget.dataset;
				const {
					id
				} = e.currentTarget.dataset;

				// 修改为Vue的响应式数据更新方式
				this.$set(this.answers, questionId, id);
				this.checkAnswerStatus();
			},
			handleInput(e) {
				const questionId = e.currentTarget.dataset.questionId;
				const value = e.detail.value;

				// 更新答案
				this.$set(this.answers, questionId, value);
				this.checkAnswerStatus();
			},
			handleSubjectScore(e) {
				const {
					questionId,
					subjectIndex
				} = e.currentTarget.dataset;
				const {
					value
				} = e.detail;

				// 验证分数是否为数字且在0-150之间
				const score = Number(value);
				if (isNaN(score) || score < 0 || score > 150) {
					uni.showToast({
						title: '请输入0-150之间的数字',
						icon: 'none'
					});
					return;
				}

				// 初始化answers[questionId]为数组（如果尚未初始化）
				if (!this.answers[questionId]) {
					this.$set(this.answers, questionId, []);
				}

				// 更新对应科目的分数
				this.$set(this.answers[questionId], subjectIndex, {
					...this.answers[questionId][subjectIndex],
					score: value
				});

				// this.fsList.push(value)

				// 计算总分
				this.calculateTotalScore();
				this.checkAnswerStatus();
			},
			calculateTotalScore() {
				if (!this.answers[6]) return 0;
				this.total = this.answers[6].reduce((sum, subject) => {
					const score = Number(subject.score) || 0;
					return sum + score;
				}, 0);

				let obj = Object.values(this.answers)
				console.log(obj)
				let arr5 = obj[obj.length - 1]
				this.fsList = []
				arr5.map((item, index) => {
					if (item.score > 0) {
						this.fsList.push(this.subjects[index].name + ':' + item.score)
					}
				})
				console.log(this.fsList)
			},
			LabelFn(val, arr) {
				let str = ''
				arr.forEach((item) => {
					if (item.id == val) {
						str = item.choiceContent
					}
				})
				return str
			},
			handleMultiSelect(currentItem, id, myItems) {
				let questionId = id;
				let arrs = []
				currentItem.checked = !currentItem.checked
				myItems.map((myitem) => {
					if (myitem.checked) {
						arrs.push(myitem.id)
					}
				})
				if (arrs.length > 2) {
					uni.showToast({
						title: "学生副科最多选择两门,请重新选择",
						icon: 'none'
					})
				}
				let values = arrs;
				if (questionId === 7) {
					this.yxzyList = values
				}
				if (questionId === 4) {
					const subjects = values.map(subject => ({
						name: this.LabelFn(subject, this.questions[this.currentQuestion].questionChoiceDoS),
						score: ''
					}));

					this.fkList = subjects
					let arr = [{
							name: '语文',
							score: ''
						},
						{
							name: '数学',
							score: ''
						},
						{
							name: '外语',
							score: ''
						}
					];
					let myname = ''
					this.questions[this.currentQuestion - 1].questionChoiceDoS.map((items) => {
						if (items.id == this.answers[this.questions[this.currentQuestion - 1].id]) {
							myname = items.choiceContent
						}
					})
					let myarr = arr.concat([{
						name: myname,
						score: ''
					}]).concat(subjects);
					this.subjects = myarr

					// 修改为Vue的响应式数据更新方式
					this.$set(this.answers, questionId, values);
					this.$set(this.questions[5], 'subjects', myarr);
				} else {
					// 修改为Vue的响应式数据更新方式
					this.$set(this.answers, questionId, values);
				}
				this.checkAnswerStatus();
			},



			// 切换题目时重置状态
			prevQuestion() {
				if (this.currentQuestion > 0) {
					this.currentQuestion = this.currentQuestion - 1
					uni.pageScrollTo({
						scrollTop: 0,
						duration: 100
					})
					this.checkAnswerStatus()
				}
			},

			nextQuestion() {
				if (this.currentQuestion < this.questions.length - 1) {

					this.currentQuestion = this.currentQuestion + 1
					uni.pageScrollTo({
						scrollTop: 0,
						duration: 100
					})
					this.checkAnswerStatus()
				}
			}
		}
	}
</script>
<style>
	/* 自定义单选按钮样式 */
	radio {
		transform: scale(0.8);
	}

	radio .wx-radio-input {
		border-color: #fb6b3d !important;
	}

	radio .wx-radio-input.wx-radio-input-checked {
		background-color: #fb6b3d !important;
	}

	/* 自定义多选按钮样式 */
	checkbox {
		transform: scale(0.8);
	}

	checkbox .wx-checkbox-input {
		border-color: #fb6b3d !important;
	}

	checkbox .wx-checkbox-input.wx-checkbox-input-checked {
		background-color: #fb6b3d !important;
	}

	/* 修改选中对号颜色为白色 */
	checkbox .wx-checkbox-input.wx-checkbox-input-checked::before {
		color: #fff !important;
		font-size: 32rpx;
	}

	checkbox .wx-checkbox-input {
		border-radius: 50%;
		width: 40rpx;
		height: 40rpx;
		background: #ffffff !important;
	}

	checkbox .wx-checkbox-input.wx-checkbox-input-checked {
		border-color: #fb6d3f !important;
		background: #ffffff !important;
	}

	checkbox .wx-checkbox-input.wx-checkbox-input-checked::before {
		border-radius: 50%;
		width: 40rpx;
		height: 40rpx;
		line-height: 40rpx;
		text-align: center;
		font-size: 28rpx;
		color: #fff;
		background: #fb6d3f !important;
		transform: translate(-50%, -50%) scale(1);
		-webkit-transform: translate(-50%, -50%) scale(1);
	}

	page {
		background: #fef1db
	}
</style>
<style lang="scss" scoped>
	.radio-list {
		display: flex;
		flex-direction: column;
		padding: 10rpx 0;

		.item {
			padding: 10rpx 0;
			display: flex;
			align-items: center;

			.icon {
				margin-right: 20rpx;
				display: flex;
				align-items: center;
				padding-top: 6rpx;

				image {
					width: 48rpx;
					height: 48rpx;
				}
			}

			.text {
				flex: 1;
				font-size: 32rpx;
				color: #333;
			}

			&.cur {
				.text {
					color: #fb6d40;
				}
			}
		}
	}

	.test {
		.title {
			padding: 20rpx;
			font-weight: 700;
		}

		.val {
			display: flex;
			align-items: center;
			height: 80rpx;
			padding: 0 20rpx;
			background: #ffffff;
			margin-bottom: 20rpx;
		}
	}

	.dx-part {
		padding: 20rpx;
		display: flex;
		position: relative;
		flex-direction: row;

		.dx-left {
			width: 160rpx;
			background: #f7f7f7;
			display: flex;
			flex-direction: column;
			padding: 0rpx 0;
			border-radius: 20rpx 0 0 20rpx;

			.item {
				display: flex;
				flex-direction: row;
				align-items: center;
				margin-bottom: 5rpx;
				justify-content: center;
				font-size: 26rpx;
				color: #999;
				position: relative;
				padding: 22rpx 0;

				.zy-list {
					position: absolute;
					left: 200rpx;
					background: #ff0000;
					display: none;
					top: 10rpx;
					display: flex;
					flex-direction: column;

					.myitem {
						font-size: 28rpx;
						padding: 20rpx 0;
						color: #333;

						&.active {
							color: #fb6b3d;
						}
					}
				}

				&.active {
					background: #fff0eb;
					color: #fb6b3d;
					border-radius: 20rpx 0 0 20rpx;

					.zy-list {
						display: block;
					}
				}
			}
		}

		.dx-right {
			padding-top: 10rpx;
			margin-left: 20rpx;
		}
	}

	.closeicon {
		position: absolute;
		right: -25rpx;
		top: 5rpx;

		image {
			width: 64rpx;
			height: 64rpx;
		}
	}

	.picbox {
		background-repeat: no-repeat;
		background-size: contain;
		width: 600rpx;
	}

	.myboxs {
		margin: 100rpx 30rpx 20rpx 30rpx;
		height: 400rpx;
		flex-direction: column;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;

		.tit2 {
			margin-bottom: 40rpx;
			color: #AA7248;
			margin-top: 30rpx;
			font-size: 42rpx;
			font-weight: 700;
		}

		.cont {
			margin-bottom: 20rpx;

			text {
				font-weight: 700;
				letter-spacing: 0rpx;
				font-size: 26rpx;
				color: #AA7248;
				line-height: 42rpx;

				.bold {
					color: #e6702b;
					margin: 0 4rpx;
				}
			}
		}

		.jiazaizhong {
			margin-bottom: 20rpx;
		}

		.btn {
			button {
				color: #fff;
				padding: 5rpx 80rpx;
				border-radius: 70rpx;
				font-size: 32rpx;
				background-image: linear-gradient(90deg, #FF8C38 0, #FD5819 100%);

				&::after {
					border: none;
				}
			}
		}
	}

	.major-container {
		display: flex;
		margin-top: 20rpx;
	}

	.major-categories {
		width: 200rpx;
		border-right: 1rpx solid #eee;
	}

	.category-item {
		padding: 20rpx;
		font-size: 28rpx;
		color: #333;
		border-bottom: 1rpx solid #eee;

		&.active {
			color: #FB6B3D;
			background-color: #FFF0EB;
		}
	}

	.major-subcategories {
		flex: 1;
		padding-left: 20rpx;
	}

	.subcategory-item {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #eee;

		text {
			margin-left: 20rpx;
			font-size: 28rpx;
			color: #333;
		}
	}

	.sexList {
		padding: 35rpx 0;
		margin: 0 -40rpx;
		display: flex;
		flex-direction: row;
		justify-content: space-between;

		.myitem {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;

			.img {
				position: relative;

				image {
					width: 270rpx;
					height: 270rpx;
				}
			}

			.text {
				margin-top: 15rpx;
				font-size: 32rpx;
				font-weight: 700;
				color: #333;
			}

			&.active {
				.text {
					color: #FB6B3D;
				}
			}
		}
	}

	.cityList {
		margin: 0 -20rpx;
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;

		.myitem {
			width: calc(25% - 24rpx);
			margin: 0 10rpx;
			box-sizing: border-box;
			margin-bottom: 20rpx;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			background: #f6f6f6;
			border: 2rpx solid #f6f6f6;
			height: 64rpx;
			line-height: 64rpx;
			font-size: 26rpx;
			color: #333;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			border-radius: 8rpx;

			&.active {
				border-color: #FB6B3D;
				background: #FFF0EB;
				color: #FB6B3D
			}
		}
	}

	.input-field {
		height: 80rpx;
		border: 1rpx solid #ddd;
		border-radius: 8rpx;
		padding: 0 20rpx;
		font-size: 30rpx;
		margin-top: 20rpx;
		line-height: 80rpx;
		background-color: #fff;
	}

	.bg {
		// min-height: 100vh;
		// background-image: linear-gradient(180deg, #fc9a6f 0, #fff6e5 100%);

	}

	.container {
		padding: 20rpx;
		position: relative;
		padding-bottom: 120rpx;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.question-container {
		background-color: #fff;
		border-radius: 12rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.question-header {
		padding: 25rpx 0;
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.question-number {
		font-size: 36rpx;
		color: #fff;
		font-weight: 700;
		min-width: 45rpx;
		text-align: center;
		padding: 10rpx 30rpx 10rpx 20rpx;
		border-radius: 0 40rpx 40rpx 0;
		margin-right: 20rpx;
		background: #fb6d3f;
	}

	.question-title {
		font-size: 32rpx;
		color: #333;
		font-weight: 700;
		line-height: 1.5;
	}

	.question-content {
		padding: 0 40rpx;
		padding-bottom: 50rpx;
	}

	.option-item {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #eee;
	}

	.option-item:last-child {
		border-bottom: none;
	}

	.option-item text {
		margin-left: 20rpx;
		font-size: 30rpx;
		color: #333;
	}



	.input-field {
		height: 80rpx;
		border: 1rpx solid #ddd;
		border-radius: 8rpx;
		padding: 0 20rpx;
		font-size: 30rpx;
		margin-top: 20rpx;
	}

	.subjects-container {
		display: flex;
		margin: 20rpx -20rpx;
		flex-direction: row;
		flex-wrap: wrap;
	}

	.subject-item {
		width: 50%;
		display: flex;
		align-items: center;
		padding: 10rpx 20rpx;
		margin-bottom: 20rpx;
		box-sizing: border-box;
		flex-direction: row;

		.unit {
			font-size: 24rpx;
		}
	}

	.subject-item:last-child {
		border-bottom: none;
	}

	.subject-name {
		font-size: 28rpx;
		color: #333;
	}

	.subject-score {
		flex: 1;
		height: 80rpx;
		border: 1rpx solid #ddd;
		border-radius: 8rpx;
		padding: 0 20rpx;
		font-size: 30rpx;
		margin-left: 20rpx;
	}

	.button-container {
		display: flex;
		justify-content: space-between;
		padding: 20rpx 20rpx;
		box-sizing: border-box;
		background-color: #fff;
		position: fixed;
		padding-bottom: 25rpx;
		padding-bottom: calc(constant(safe-area-inset-bottom) + 25rpx);
		padding-bottom: calc(env(safe-area-inset-bottom) + 25rpx);
		bottom: 0;
		left: 0;
		right: 0;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.nav-button {
		flex: 1;
		margin: 0 10rpx;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 8rpx;
		font-size: 30rpx;

		&::after {
			border: none;
		}
	}

	.prev-button {
		background-color: #fff0eb;
		color: #fb6b3d;
	}

	.next-button {
		background-color: #fff0eb;
		color: #fb6b3d;
	}

	.submit-button {
		width: 320rpx;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		background-color: #fb6b3d;
		color: #fff;
		border-radius: 8rpx;
		font-size: 30rpx;
	}
</style>



<style>
	.input-field {
		height: 80rpx;
		border: 1rpx solid #ddd;
		border-radius: 8rpx;
		padding: 0 20rpx;
		font-size: 30rpx;
		margin-top: 20rpx;
		line-height: 80rpx;
		background-color: #fff;
	}
</style>