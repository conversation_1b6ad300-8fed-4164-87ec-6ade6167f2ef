<template>
	<view>
		<view class="head" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="left">
					<view class="itemList">
						<view class="item" @tap="back">
							<button>
								<image style="width:20rpx"
									src="https://pacetupian.yuyuexiaobao.com/PACE/mall/back-light.png">
								</image>
							</button>
						</view>
					</view>
				</view>
				<view class="text">AI聊天</view>
			</view>
		</view>

		<view class="chat-container">

			<!-- 消息列表区域 -->
			<scroll-view class="messages-container" 
    scroll-y="true" 
    :scroll-top="scrollTop"
    :scroll-with-animation="true"
    :scroll-anchoring="true"
    :refresher-enabled="false"
    :bounces="false"
    ref="messagesContainer"
    show-scrollbar="false">
				<view v-for="(message, index) in messages" :key="index" :class="['message', message.role]">
					<!-- <view class="avatar">
					{{ message.role === 'user' ? '👤' : '🤖' }}
				</view> -->
					<view class="message-content">
						{{ message.content }}
					</view>
					<!-- 在消息列表部分添加加载状态显示 -->
					
					        <!-- {{ message.content }} -->
					        <view v-if="message.role === 'assistant' && isLoading && index === messages.length - 1" class="loading-dots">
					            <text>.</text>
					            <text>.</text>
					            <text>.</text>
					        </view>
					    
				</view>
			</scroll-view>

			<!-- 固定在底部的输入区域 -->
			<view class="input-wrapper">
				<view class="input-container">
					<textarea v-model="newMessage" @keyup.enter.exact="sendMessage" :disabled="isLoading"
						placeholder="输入问题内容" :auto-height="true" :maxlength="-1" class="message-input" />
					<button @click="sendMessage" :disabled="isLoading" class="send-button">
						{{ isLoading ? '发送中...' : '发送' }}
					</button>
				</view>
				<!-- 适配底部安全区域 -->
				<view class="safe-area-inset-bottom"></view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'ChatInterface',
		data() {
			return {
				titleTop: 0,
            messages: [],
            newMessage: '',
            isLoading: false,
            scrollTop: 0,
            isShow: true,
            streamingMessage: '',
			}
		},
		watch: {
			// messages: {
			// 	handler() {
			// 		this.$nextTick(() => {
			// 			this.scrollToBottom()
			// 		})
			// 	},
			// 	deep: true
			// }
		},
		// watch: {
		//     messages: {
		//       handler() {
		//         this.scrollToBottom()
		//       },
		//       deep: true
		//     }
		//   },
		onShow() {
			this.getList()


		},
		onLoad(){
			this.initPage()
		},
		mounted() {
			// if (this.isShow) {
			// 	this.scrollToBottom()
			// }
			// this.scrollToBottom()
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top
		},
		methods: {
			initPage() {
            let res = uni.getMenuButtonBoundingClientRect();
            this.titleTop = res.top
        },
			back() {
				uni.navigateBack({
					delta: 1
				})
			},
			getList() {
				this.$apis.conversationList({
					userId: uni.getStorageSync('userId'),
					pageNo: 1,
					pageSize: 100
				}).then((res) => {
					if (res.code == 0) {
						const list = res.data.list.map((item) => {
                        if (item.roleId == 1) {
                            return {
                                role: 'user',
                                content: item.title
                            }
                        } else if (item.roleId == 2) {
                            return {
                                role: 'assistant',
                                content: item.systemMessage
                            }
                        }
                    }).filter(item => item); // 过滤掉可能的 undefined

                    this.messages = list;
                    
                    // 使用 nextTick 确保消息列表已渲染
                    this.$nextTick(() => {
                        setTimeout(() => {
                            this.scrollToInitialPosition();
                        }, 100);
                    });
					}
				})
			},
			// 新增初始滚动方法
			scrollToInitialPosition() {
            const query = uni.createSelectorQuery().in(this);
            query.select('.messages-container').boundingClientRect((containerRect) => {
                if (containerRect) {
                    query.selectAll('.message').boundingClientRect((messageRects) => {
                        if (messageRects && messageRects.length > 0) {
                            // 计算所有消息的总高度
                            const totalHeight = messageRects.reduce((sum, rect) => sum + rect.height, 0);
                            // 设置滚动位置
                            this.scrollTop = totalHeight + 1000; // 加上一个较大的值确保滚动到底部
                        }
                    }).exec();
                }
            }).exec();
        },
			async sendMessage() {
            if (!this.newMessage.trim() || this.isLoading) return;

            const userMessage = this.newMessage.trim();
            this.messages.push({
                role: 'user',
                content: userMessage
            });

            this.newMessage = '';
            this.isLoading = true;

            const assistantMessageIndex = this.messages.length;
            this.messages.push({
                role: 'assistant',
                content: ''
            });
			// 添加空的助手消息后再次滚动
            this.$nextTick(() => {
                this.scrollToBottom();
            });

            try {
                await new Promise((resolve, reject) => {
                    uni.request({
                        url: 'http://192.168.10.205:48080/admin-api/system/ai/chat-conversation/create',
                        method: 'POST',
                        data: {
                            userId: uni.getStorageSync('userId'),
                            title: userMessage
                        },
                        header: {
                            'Accept': 'text/event-stream',
                            'Content-Type': 'application/json',
                        },
                        success: (res) => {
							console.log(res)
                            if (res.data) {
                                const chunks = res.data.split('\n');
                                this.processStreamResponse(chunks, assistantMessageIndex);
                            }
                            resolve();
                        },
                        fail: (err) => {
                            reject(err);
                        }
                    });
                });
            } catch (error) {
                console.error('发送消息失败:', error);
                uni.showToast({
                    title: '发送失败，请重试',
                    icon: 'none'
                });
            } finally {
                this.isLoading = false;
            }
        },

        // 新增处理流式响应的方法
        processStreamResponse(chunks, messageIndex) {
            let currentIndex = 0;
            
            const processChunk = () => {
                if (currentIndex >= chunks.length) {
                    return;
                }

                const chunk = chunks[currentIndex].replace('data:', '').trim();
				console.log(chunk)
                if (chunk) {
					let str = JSON.parse(chunk).data.content
                    this.$set(this.messages[messageIndex], 'content', 
                        this.messages[messageIndex].content + str);
                    
                    this.$nextTick(() => {
                        this.scrollToBottom();
                    });
                }

                currentIndex++;
                setTimeout(processChunk, 50);
            };

            processChunk();
        },
			scrollToLatestMessage() {
				const query = uni.createSelectorQuery().in(this)
				query.selectAll('.message').boundingClientRect(data => {
					if (data && data.length > 0) {
						const totalHeight = data.reduce((sum, item) => sum + item.height, 0)
						this.scrollTop = totalHeight
					}
				}).exec()
			},
			scrollToBottom() {
				setTimeout(() => {
					const query = uni.createSelectorQuery().in(this)
					query.selectAll('.message').boundingClientRect(data => {
						if (data && data.length > 0) {
							const totalHeight = data.reduce((sum, item) => sum + item.height, 0)
							this.scrollTop = totalHeight + 1000
						}
					}).exec()
				}, 100)
			},
			// scrollToBottom() {

			// 使用 uniapp 的选择器方法
			// const query = uni.createSelectorQuery().in(this)
			// query.select('.messages-container').boundingClientRect(data => {
			//   if (data) {
			//     uni.pageScrollTo({
			//       scrollTop: data.height,
			//       duration: 300
			//     })
			//   }
			// }).exec()

			// const query = uni.createSelectorQuery().in(this)
			// query.select('.messages-container').boundingClientRect(data => {
			// if (data) {
			//	this.scrollTop = data.height
			//}
			//}).exec()

			// this.$nextTick(() => {
			//        if (this.$refs.messagesContainer) {
			//          this.$refs.messagesContainer.scrollTop = this.$refs.messagesContainer.scrollHeight
			//        } else {
			//          console.log('未找到 messagesContainer ref')
			//        }
			//      })
			//}
		}
	}
</script>

<style scoped>
/* 添加加载动画样式 */
.loading-dots {
    display: inline-flex;
    gap: 4rpx;
}

.loading-dots text {
    animation: loading 1s infinite;
}

.loading-dots text:nth-child(2) {
    animation-delay: 0.2s;
}

.loading-dots text:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes loading {
    0%, 100% {
        opacity: 0.2;
    }
    50% {
        opacity: 1;
    }
}
	.message.loading .message-content {
		display: flex;
		align-items: center;
		gap: 8px;
	}

	.loading-dots {
		display: flex;
		gap: 4px;
	}

	.loading-dots span {
		width: 8px;
		height: 8px;
		background-color: #999;
		border-radius: 50%;
		animation: loading 1s infinite ease-in-out;
	}

	@keyframes loading {

		0%,
		100% {
			transform: scale(0.3);
		}

		50% {
			transform: scale(1);
		}
	}

	.chat-container {
		height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;
	}

	.messages-container {
		flex: 1;
		box-sizing: border-box;
		overflow-y: auto;
		padding: 20px;
	}

	.message {
		display: flex;
		margin-bottom: 20px;
		gap: 12px;
	}

	.avatar {
		width: 40px;
		height: 40px;
		border-radius: 50%;
		background-color: #e0e0e0;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 20px;
	}

	.message-content {
		background-color: white;
		padding: 12px 16px;
		border-radius: 12px;
		max-width: 70%;
		box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
	}

	.user {
		flex-direction: row-reverse;
	}

	.user .message-content {
		background-color: #1a8cff;
		color: white;
	}

	.input-container {
		padding: 20px;
		background-color: white;
		border-top: 1px solid #e0e0e0;
		display: flex;
		gap: 10px;
	}

	.message-input {
		flex: 1;
		padding: 12px;
		border: 1px solid #e0e0e0;
		border-radius: 8px;
		resize: none;
		font-family: inherit;
		font-size: 14px;
	}

	.send-button {
		padding: 0 20px;
		background-color: #1a8cff;
		color: white;
		border: none;
		border-radius: 8px;
		cursor: pointer;
		font-size: 14px;
	}

	.send-button:hover {
		background-color: #0066cc;
	}

	.chat-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
	}

	.messages-container {
		flex: 1;
		padding: 20rpx;
		/* 为底部输入框留出空间 */
		padding-bottom: 180rpx;
	}

	.message {
		display: flex;
		margin-bottom: 20rpx;
		gap: 24rpx;
	}

	.avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		background-color: #e0e0e0;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 40rpx;
	}

	.message-content {
		background-color: white;
		padding: 24rpx 32rpx;
		border-radius: 24rpx;
		max-width: 70%;
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}

	.user {
		flex-direction: row-reverse;
	}

	.user .message-content {
		background-color: #1a8cff;
		color: white;
	}

	.input-wrapper {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.input-container {
		padding: 20rpx;
		display: flex;
		gap: 20rpx;
		align-items: flex-end;
		background-color: white;
	}

	.message-input {
		flex: 1;
		padding: 20rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 16rpx;
		font-size: 28rpx;
		min-height: 80rpx;
		max-height: 200rpx;
	}

	.send-button {
		padding: 0 40rpx;
		height: 80rpx;
		line-height: 80rpx;
		background-color: #1a8cff;
		color: white;
		border: none;
		border-radius: 16rpx;
		font-size: 28rpx;
	}

	.send-button:active {
		background-color: #0066cc;
	}

	/* 适配底部安全区域 */
	.safe-area-inset-bottom {
		padding-bottom: constant(safe-area-inset-bottom);
		/* iOS 11.2+ */
		padding-bottom: env(safe-area-inset-bottom);
		/* iOS 11.2+ */
		background-color: #fff;
	}
</style>