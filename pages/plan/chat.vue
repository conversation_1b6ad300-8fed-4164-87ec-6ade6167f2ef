<template>
	<view>
		<view class="head" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="left" @tap="back">
					<view class="itemList">
						<view class="item">
							<button>
								<image style="width:20rpx"
									:src="imgUrl+'/qdkbm/newimage/fhui/back-light.png'">
								</image>
							</button>
						</view>
					</view>
				</view>
				<view class="text">智能问答</view>
			</view>
		</view>

		<u-popup :round="8" mode="center" bgColor="transparent" :show="showbox">
			<view class="picbox"
				:style="{'background-image': 'url('+imgUrl+'/qdkbm/newimage/fhpic/pop-bg.png)'}">
				<view class="closeicon" @tap="closebox">
					<image :src="imgUrl+'/qdkbm/newimage/fhpic/fh-cancelbtn.png'"></image>
				</view>
				<view class="successimg" style="position: absolute;left:50%;margin-left:-90rpx;top:-10rpx;z-index:99;">
					<image style="width:220rpx;" mode="widthFix"
						:src="imgUrl+'/qdkbm/newimage/fhpic/popicon1.png'"></image>
				</view>
				<view class="myboxs">
					<view class="cont">
						<text>10次免费提问已用完！\n请购买智能问答礼包</text>
					</view>
					<view class="btn"><button @tap="confirmPay">去购买</button> <button class="btn2"
							@tap="closebox">关闭页面</button>
					</view>
				</view>
			</view>

		</u-popup>

		<view class="chat-container">

			<!-- 消息列表区域 -->
			<scroll-view :scroll-into-view="scrollToId" class="messages-container"
				:style="{'height':'calc(100vh - '+height+'px )' }" scroll-y="true" :scroll-top="scrollTop"
				:scroll-with-animation="true" :scroll-anchoring="true" ref="messagesContainer" show-scrollbar="false">
				<view class="tipbox">
					作为您的志愿规划小伙伴，可以帮您解决一些相关问题。

				</view>
				<view v-for="(message, index) in messages" :key="index" :id="'item-' + message.id"
					:class="['message', message.role]">
					<!-- <view class="avatar">
					{{ message.role === 'user' ? '👤' : '🤖' }}
				</view> -->
					<view class="message-content" style="line-height: 1.5;" v-if="message.content">
						<!-- <markdown-render :sourceMdContent="message.content" :showCursor="false"></markdown-render> -->
						<mpHtml  :selectable="true"	 :content="message.content"></mpHtml>
					</view>
					<!-- 在消息列表部分添加加载状态显示 -->

					<!-- {{ message.content }} -->
					<view v-if="message.role === 'assistant' && isLoading && index === messages.length - 1&&!message.content"
						class="loading-dots2"
						style="display: flex;align-items: center; gap: 4px; font-size: 30rpx;line-height: 1.8;background:#f5f5f5;border-radius: 12rpx;padding:12rpx 15rpx">
						<image :src="imgUrl+'/qdkbm/newimage/fhui/loading.svg'" mode="aspectFill"
							style="width: 14px;height: 14px;" /> 请稍等，正在卖力思考中 🤔
						<!-- <text>.</text>
						<text>.</text>
						<text>.</text> -->
					</view>


				</view>
				<view id="scroll-bottom" style="width: 100%;height: 2px;"></view>
			</scroll-view>

			<!-- 固定在底部的输入区域 -->
			<view class="input-wrapper">
				<view class="input-container">
					<image @tap="refresh" class="refreshicon"
						:src="imgUrl+'/qdkbm/newimage/fhui/icon-refresh.png'"></image>

					<textarea v-model="newMessage" @keyup.enter.exact="sendMessage" :disabled="isLoading"
						placeholder="输入问题内容"  maxlength="1024"  confirm-type="send" :adjust-position="true" cursor-spacing="40" :auto-height="true" :show-confirm-bar="false"  placeholder-style="color:#999"
						class="message-input" />
					<button @tap="sendMessage" :disabled="isLoading" class="send-button">
						<!-- {{ isLoading ? '发送中...' : '发送' }} -->
						<image style="width:56rpx;height:56rpx;"
							:src="isLoading?imgUrl+'/qdkbm/newimage/fhui/icon-sendbtn-gray.png':imgUrl+'/qdkbm/newimage/fhui/icon-sendbtn.png'">
						</image>
					</button>
				</view>
				<!-- 适配底部安全区域 -->
				<view class="safe-area-inset-bottom"></view>
			</view>
		</view>
	</view>
</template>
<script>
	import Request from '@/utils/luch/index.js'
	import mpHtml from '@/uni_modules/mp-html/components/mp-html/mp-html.vue'
	import uPopup from '@/uni_modules/uview-ui/components/u-popup/u-popup.vue'
	import simpleMarkdown from '@/utils/simple-markdown.js'

	const http = new Request();
	const markdownit = simpleMarkdown;

	export default {
		components: {
			mpHtml,
			uPopup
		},
		name: 'ChatInterface',
		data() {
			return {
				total: 0,
				page: 1,
				size: 100,
				imgUrl: this.$base.uploadImgUrl,
				baseUrl: this.$base.baseUrl,
				height: 0,
				showbox: false,
				count: 0,
				titleTop: 0,
				scrollToId: "",
				messages: [],
				newMessage: '',
				isLoading: false,
				scrollTop: 0,
				isShow: true,
				streamingMessage: '',
			}
		},
		watch: {
			// messages: {
			// 	handler() {
			// 		this.$nextTick(() => {
			// 			this.scrollToBottom()
			// 		})
			// 	},
			// 	deep: true
			// }
		},
		// watch: {
		//     messages: {
		//       handler() {
		//         this.scrollToBottom()
		//       },
		//       deep: true
		//     }
		//   },
		onShow() {
			this.getList()
		},
		mounted() {
			// if (this.isShow) {
			// 	this.scrollToBottom()
			// }
			// this.scrollToBottom()
			// console.log(markdownit)
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top
		},
		methods: {
			confirmPay() {
				uni.navigateTo({
					url: '/pages/plan/buycount'
				})
				this.isLoading = false
				this.showbox = false
			},
			closebox() {
				this.showbox = false
				this.isLoading = false
			},
			openbox() {
				this.showbox = true
			},
			refresh() {
				this.getList()
			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			},
			getList() {
				this.$apis.conversationList({
					userId: uni.getStorageSync('userId'),
					pageNo: this.page,
					pageSize: this.size
				}).then((res) => {
					if (res.code == 0) {
						this.total = res.data.total
						console.log(this.total)
						if (this.total / this.size > this.page) {
							this.page++
							this.getList()
						}
						let arr = res.data.list;
						if (this.page === 1) this.messages = [];
						this.messages = this.messages.concat(arr);
						if (this.messages.length > 0) {
							this.messages.map((item) => {
								if (item.roleId == 1) {
									item.role = 'user'
									item.content = item.title
								} else if (item.roleId = 2) {
									item.role == 'assistant'
									// item.content = item.systemMessage
									item.content = item.systemMessage ? markdownit().render(item
										.systemMessage) : item.systemMessage
								}
							})
						}
						this.messages = this.messages.slice(-20)
						this.height = 0
						const query = uni.createSelectorQuery().in(this)


						query.selectAll('.head').boundingClientRect(data2 => {
							query.selectAll('.input-wrapper').boundingClientRect(data => {
								this.height = data2[0].height + data[0].height + 0
							}).exec()
						}).exec()
						// console.log(this.messages)
						setTimeout(() => {
							//  this.scrollToId = 'item-' + this.messages[this.messages.length - 1].id

							this.scrollToId = "scroll-bottom"
							// console.log(this.scrollToId)
						}, 1000)
					}
				})
			},

			async sendMessage() {
				if (!this.newMessage.trim() || this.isLoading) return;
				this.count = uni.getStorageSync('tws') || 0
				let token = uni.getStorageSync('token')


				if (token) {
					let res = await this.$store.dispatch('getuseInfo')
					const isVip = uni.getStorageSync('isVip') || false;
					this.count = uni.getStorageSync('tws') || 0
					if (isVip) {
						uni.navigateTo({
							url: "/pages/major/sxdetail?id=" + item.id
						})
					} else {
						if (this.count <= 0) {
							this.isLoading = true;
							this.showbox = true
						} else {
							this.count--
							uni.setStorageSync('tws', this.count)
							const userMessage = this.newMessage.trim();
							this.messages.push({
								role: 'user',
								content: userMessage,
								id: this.messages.length>=1?this.messages[this.messages.length - 1].id + 1:0
							});
							this.newMessage = '';
							this.isLoading = true;
							const assistantMessageIndex = this.messages.length;
							this.messages.push({
								role: 'assistant',
								content: '',
								id: this.messages.length>=1?this.messages[this.messages.length - 1].id + 2:1
							});
							setTimeout(() => {
								if(this.messages.length>=1){
									this.scrollToId = 'item-' + this
										.messages[this.messages.length - 1]
										.id
								}

							}, 800)
							let baseurl = this.baseUrl
							// try {
							// 	await new Promise((resolve, reject) => {
							http.post(
								baseurl+'admin-api/system/ai/chat-conversation/create', {
									userId: uni.getStorageSync('userId'),
									title: userMessage
								}, {
									header: {
										'Content-Type': 'application/json',
										'Accept': 'text/event-stream'
									},
									timeout: 300000
								}
							).then(res => {
								// console.log('收到数据:', res.data);
								let chunks = res.data.split('\n');
								// console.log(chunks)
								this.processStreamResponse(chunks,
									assistantMessageIndex);
								setTimeout(() => {
									if(this.messages.length>=1){
										this.scrollToId = 'item-' + this
											.messages[this.messages.length - 1]
											.id
									}

									// this.scrollToBottom();
								}, 1500);
								// 处理数据后立即发起下一个请求
								// startSSE();
							}).catch(error => {
								uni.showModal({
									title:"提示",
									content:"发送失败",
									showCancel:false,
									success(res8){
										if(res8.confirm){

										} else {

										}
									}
								})
								console.error('请求错误:', error);
							});
							// uni.request({
							// 	url: this.$base.baseUrl +
							// 		'admin-api/system/ai/chat-conversation/create',
							// 	method: 'POST',
							// 	data: {
							// 		userId: uni.getStorageSync('userId'),
							// 		title: userMessage
							// 	},
							// 	header: {
							// 		'Accept': 'text/event-stream',
							// 		'Content-Type': 'application/json',
							// 	},
							// 	success: (res) => {
							// 		console.log(res)
							// 		if (res.data) {
							// 			const chunks = res.data.split('\n');
							// 			console.log(chunks)
							// 			this.processStreamResponse(chunks,
							// 				assistantMessageIndex);
							// 			setTimeout(() => {
							// 				this.scrollToId = 'item-' + this
							// 					.messages[this.messages.length - 1]
							// 					.id
							// 				// this.scrollToBottom();
							// 			}, 1500);
							// 		}
							// 		resolve();
							// 	},
							// 	fail: (err) => {
							// 		reject(err);
							// 	}
							// });
							// });
							// } catch (error) {
							// 	console.error('发送消息失败:', error);
							// 	uni.showToast({
							// 		title: '发送失败，请重试',
							// 		icon: 'none'
							// 	});
							// } finally {
							// 	this.isLoading = false;
							// }

						}
					}
				} else {
					uni.showModal({
						title: '提示',
						content: '您还未登录，请先登录',
						confirmText: '去登录',
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/login/login'
								});
							}
						}
					});
				}



			},

			// 新增处理流式响应的方法
			processStreamResponse(chunks, messageIndex) {
				// console.log(chunks)
				 let currentIndex = 0;
				 let mystr2 = ''
				let mystr = ''
				// chunks.forEach((items,indexs)=>{
				// 	let chunk = items.replace('data:','').trim();
				// 	if(chunk){
				// 		let str = JSON.parse(chunk).data.content
				// 		mystr = mystr + str
				// 		console.log(mystr)
				// 		this.$set(this.messages[messageIndex], 'content',
				// 			this.messages[messageIndex].content + str);
				// 		if(indexs == chunks.length-1){
				// 			this.$set(this.messages[messageIndex], 'content',
				// 				markdownit().render(mystr));
				// 			this.isLoading = false
				// 			this.scrollToId = 'scroll-bottom'
				// 		}
				// 	}
				// })
				const processChunk = () => {
					if (currentIndex >= chunks.length) {
						return;
					}
					const chunk = chunks[currentIndex].replace('data:', '').trim();
					// console.log(currentIndex)
					if (chunk) {
						let str = JSON.parse(chunk).data.content
					    mystr2 += str
							this.$set(this.messages[messageIndex], 'content',
								this.messages[messageIndex].content + str);


						// setTimeout(() => {
						// 	this.scrollToId = 'item-' + this.messages[this.messages.length - 1].id
						// 	// this.scrollToBottom();
						// }, 1500);

					}

					currentIndex++;
					if(currentIndex == chunks.length){
						this.$set(this.messages[messageIndex], 'content',
							markdownit().render(mystr2));
						this.isLoading = false
						this.scrollToId = 'scroll-bottom'
					}
					setTimeout(processChunk, 10);
				};
				processChunk();
				// if(currentIndex === chunks.length){
				// 	this.$set(this.messages[messageIndex], 'content',
				// 		markdownit().render(this.messages[messageIndex].content));
				// }
				// this.refresh()
			},
			scrollToLatestMessage() {
				const query = uni.createSelectorQuery().in(this)
				query.selectAll('.message').boundingClientRect(data => {
					if (data && data.length > 0) {
						const totalHeight = data.reduce((sum, item) => sum + item.height, 0)
						this.scrollTop = totalHeight
					}
				}).exec()
			},
			// 修改 scrollToBottom 方法
			scrollToBottom() {
				this.$nextTick(() => {
					const query = uni.createSelectorQuery().in(this);
					query.select('.messages-container').boundingClientRect((data) => {
						if (data) {
							// 设置滚动位置为容器的高度
							const scrollView = uni.createSelectorQuery().in(this).select(
								'.messages-container');
							scrollView.boundingClientRect((rect) => {
								if (rect) {
									this.scrollTop = rect.height; // 加一个较大的值确保滚动到底部
								}
							}).exec();
						}
					}).exec();
				});
			},
			// scrollToBottom() {
			// 	setTimeout(() => {
			// 		const query = uni.createSelectorQuery().in(this)
			// 		query.selectAll('.message').boundingClientRect(data => {
			// 			if (data && data.length > 0) {
			// 				const totalHeight = data.reduce((sum, item) => sum + item.height, 0)
			// 				this.scrollTop = totalHeight
			// 			}
			// 		}).exec()
			// 	}, 100)
			// },
			// scrollToBottom() {

			// 使用 uniapp 的选择器方法
			// const query = uni.createSelectorQuery().in(this)
			// query.select('.messages-container').boundingClientRect(data => {
			//   if (data) {
			//     uni.pageScrollTo({
			//       scrollTop: data.height,
			//       duration: 300
			//     })
			//   }
			// }).exec()

			// const query = uni.createSelectorQuery().in(this)
			// query.select('.messages-container').boundingClientRect(data => {
			// if (data) {
			//	this.scrollTop = data.height
			//}
			//}).exec()

			// this.$nextTick(() => {
			//        if (this.$refs.messagesContainer) {
			//          this.$refs.messagesContainer.scrollTop = this.$refs.messagesContainer.scrollHeight
			//        } else {
			//          console.log('未找到 messagesContainer ref')
			//        }
			//      })
			//}
		}
	}
</script>

<style scoped lang="scss">
	.tipbox{margin-top:24rpx;
		margin-bottom:30rpx;font-size: 28rpx;color:#333;background:#f5f5f5;border-radius: 16rpx;padding:20rpx;
	}
	.refreshicon{
		width: 44rpx;height:44rpx;padding:10rpx 15rpx;margin-right: 0rpx;
	}
	.closeicon {
		position: absolute;
		right: -25rpx;
		top: 5rpx;

		image {
			width: 64rpx;
			height: 64rpx;
		}
	}

	.picbox {
		background-repeat: no-repeat;
		background-size: 100% 100%;
		width: 600rpx;
	}

	.myboxs {
		margin: 100rpx 30rpx 20rpx 30rpx;
		height: 350rpx;
		flex-direction: column;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;

		.cont {
			margin-bottom: 40rpx;

			text {
				font-weight: 700;
				letter-spacing: 2rpx;
				font-size: 32rpx;
				color: #AA7248;
				line-height: 54rpx;

				.bold {
					color: #e6702b;
					margin: 0 4rpx;
				}
			}
		}

		.btn {
			display: flex;
			width: 100%;
			flex-direction: row;

			button {
				flex: 1;
				margin: 0 15rpx;
				color: #fff;
				font-weight: 700;
				padding: 5rpx 50rpx;
				border-radius: 70rpx;
				font-size: 32rpx;
				border: 2rpx solid transparent;
				background-image: linear-gradient(90deg, #FF8C38 0, #FD5819 100%);

				&.btn2 {
					background-image: linear-gradient(90deg, #fff 0, #fff 100%);
					border-color: #FD5819;
					color: #FD5819;
				}

				&::after {
					border: none;
				}
			}
		}
	}

	.popbox {
		display: flex;
		position: relative;
		flex-direction: column;

		.popcont {
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
		}

		.cont {
			font-size: 28rpx;
			padding: 40rpx 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.btn {
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;

			button {
				width: 170rpx;
				height: 60rpx;
				font-size: 28rpx;
				line-height: 60rpx;
				padding: 0;
				margin: 0;
				background: #fb6b3d;
				color: #fff;

				&::after {
					border: none
				}
			}
		}
	}

	/* 添加加载动画样式 */
	.loading-dots {
		display: inline-flex;
		gap: 4rpx;
	}

	.loading-dots text {
		animation: loading 1s infinite;
	}

	.loading-dots text:nth-child(2) {
		animation-delay: 0.2s;
	}

	.loading-dots text:nth-child(3) {
		animation-delay: 0.4s;
	}

	@keyframes loading {

		0%,
		100% {
			opacity: 0.2;
		}

		50% {
			opacity: 1;
		}
	}

	.message.loading .message-content {
		display: flex;
		align-items: center;
		gap: 8px;
	}

	.loading-dots {
		display: flex;
		gap: 4px;
	}

	.loading-dots span {
		width: 8px;
		height: 8px;
		background-color: #999;
		border-radius: 50%;
		animation: loading 1s infinite ease-in-out;
	}

	@keyframes loading {

		0%,
		100% {
			transform: scale(0.3);
		}

		50% {
			transform: scale(1);
		}
	}

	.chat-container {
		// height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #fff;
	}

	.messages-container {
		/* flex: 1; */
		box-sizing: border-box;
		overflow-y: auto;
		height: calc(100vh - 160rpx);
		padding:0 20px;
	}

	.message {
		display: flex;
		margin-bottom: 20px;
		gap: 12px;
	}

	.avatar {
		width: 40px;
		height: 40px;
		border-radius: 50%;
		background-color: #e0e0e0;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 20px;
	}

	.message-content {
		background-color: white;
		padding: 12px 16px;
		border-radius: 12px;
		max-width: 70%;
		box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

		&.a1 {
			padding: 0;
			box-shadow: none;
		}
	}

	.user {
		flex-direction: row-reverse;
	}

	.user .message-content {
		background-color: #1a8cff;
		margin-right: 20rpx;
		color: white;
		position: relative;

		&::before {
			position: absolute;
			right: -18rpx;
			content: "";
			top: 50%;
			width: 20rpx;
			height: 30rpx;
			margin-top: -12rpx;
			background: url('https://pic.kefeichangduo.top/qdkbm/newimage/fhui/member-icon-right4.png') no-repeat;
			background-size: 80% auto;
		}
	}

	.input-container {
		padding:0 20px;
		background-color: white;
		border-top: 1px solid #e0e0e0;
		display: flex;
		position: relative;
		align-items: center;
		gap: 20;

		.icon {
			width: 50rpx;

			image {
				width: 40rpx;
				height: 40rpx;
			}
		}

		.inputwrap {
			flex: 1;
			display: flex;
			gap: 10px;
			flex-direction: row;
		}
	}

	.message-input {
		flex: 1;
		padding: 12px;
		/* border: 1px solid #e0e0e0; */
		/* border-radius: 8px; */
		resize: none;
		font-family: inherit;
		font-size: 14px;
	}

	.send-button {
		padding:20rpx 20rpx;
		background: transparent;
		color: #fff;
		display: flex;
		align-items: center;
		border: none;
		border-radius: 8px;
		cursor: pointer;
		font-size: 28rpx;

		&[disabled]:not([type]) {
			background: none !important;
		}

		&::after {
			border: none
		}
	}

	.send-button:hover {
		// background-color: #FF8B1B;
	}

	.chat-container {
		display: flex;
		flex-direction: column;
		// height: 100vh;
		background-color: #fff;
	}

	.messages-container {
		/* flex: 1; */
		padding:0 20rpx;
		/* 为底部输入框留出空间 */
		/* padding-bottom: 320rpx; */
	}

	.message {
		display: flex;
		margin-bottom: 20px;
		gap: 24rpx;
	}

	.avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		background-color: #e0e0e0;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 40rpx;
	}

	.message-content {
		background-color: #f5f5f5;
		padding: 24rpx 32rpx;
		border-radius: 24rpx;
		max-width: 70%;
	}

	.user {
		flex-direction: row-reverse;
	}

	.user .message-content {
		background-color: #FF8B1B;
		color: white;
	}

	.input-wrapper {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.input-container {
		padding: 20rpx;
		display: flex;
		gap: 0rpx;
		background-color: white;
	}

	.message-input {
		flex: 1;
		padding: 25rpx 30rpx;
		background: #f0f0f0;
		/* border: 2rpx solid #e0e0e0; */
		border-radius: 25rpx;
		line-height: 36rpx;
		font-size: 30rpx;
		min-height: 36rpx;
		max-height: 200rpx;
	}

	// .send-button {
	// 	padding: 0 40rpx;
	// 	height: 80rpx;
	// 	line-height: 80rpx;
	// 	background-color: #FF8B1B;
	// 	color: white;
	// 	border: none;
	// 	border-radius: 16rpx;
	// 	font-size: 28rpx;
	// }

	// .send-button:active {
	// 	background-color: #FF8B1B;
	// }

	/* 适配底部安全区域 */
	.safe-area-inset-bottom {
		padding-bottom: constant(safe-area-inset-bottom);
		/* iOS 11.2+ */
		padding-bottom: env(safe-area-inset-bottom);
		/* iOS 11.2+ */
		background-color: #fff;
	}
</style>