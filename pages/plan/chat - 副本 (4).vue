<template>
	<view>
		<view class="head" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="left" @tap="back">
					<view class="itemList">
						<view class="item" >
							<button>
								<image style="width:20rpx"
									src="https://pacetupian.yuyuexiaobao.com/PACE/mall/back-light.png">
								</image>
							</button>
						</view>
					</view>
				</view>
				<view class="text">智能问答</view>
			</view>
		</view>

		<u-popup :round="8" mode="center"  closeOnClickOverlay bgColor="transparent" :show="showbox" @close="closebox" @open="openbox">
			<view class="picbox"
				style="background-image: url('https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/pop-bg.png');">
		        <view class="closeicon" @tap="closebox">
					<image src="https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/fh-cancelbtn.png"></image>
				</view>
				<view class="successimg" style="position: absolute;left:50%;margin-left:-90rpx;top:-10rpx;z-index:99;">
					<image style="width:220rpx;" mode="widthFix" src="https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/popicon1.png"></image>
				</view>
				<view class="myboxs">
					<view class="cont">
						<text>10次免费提问已用完！请购买智能问答礼包</text>
					</view>
					<view class="btn"><button @tap="confirmPay">去购买</button> <button @tap="closebox">关闭页面</button></view>
				</view>
			</view>

		</u-popup>

		<view class="chat-container">

			<!-- 消息列表区域 -->
			<scroll-view :scroll-into-view="scrollToId" class="messages-container" :style="{'height':'calc(100vh - '+height+'px )' }"  scroll-y="true"
				:scroll-top="scrollTop" :scroll-with-animation="true" :scroll-anchoring="true" ref="messagesContainer"
				show-scrollbar="false">
				<view style="font-size: 28rpx;color:#333;background:#f5f5f5;border-radius: 16rpx;padding:20rpx;">作为您的志愿规划小伙伴，可以帮您解决一些相关问题。

</view>
				<view v-for="(message, index) in messages" :key="index" :id="'item-' + index"
					:class="['message', message.role]">
					<!-- <view class="avatar">
					{{ message.role === 'user' ? '👤' : '🤖' }}
				</view> -->
					<view class="message-content" style="line-height: 1.5;">
						<mpHtml :content="message.content"></mpHtml>
					</view>
					<!-- 在消息列表部分添加加载状态显示 -->

					<!-- {{ message.content }} -->
					<view v-if="message.role === 'assistant' && isLoading && index === messages.length - 1"
						class="loading-dots">
						<text>.</text>
						<text>.</text>
						<text>.</text>
					</view>

				</view>
			</scroll-view>

			<!-- 固定在底部的输入区域 -->
			<view class="input-wrapper">
				<view class="input-container">
						<image @tap="refresh" style="width: 40rpx;height:40rpx;margin-right: 0rpx;" src="https://pic.kefeichangduo.top/qdkbm/newimage/fhui/icon-refresh.png"></image>

						<textarea v-model="newMessage" @keyup.enter.exact="sendMessage" :disabled="isLoading"
							placeholder="输入问题内容" placeholder-style="color:#999" :auto-height="true" :maxlength="-1" class="message-input" />
						<button @click="sendMessage" :disabled="isLoading" class="send-button">
							<!-- {{ isLoading ? '发送中...' : '发送' }} -->
							<image style="width:56rpx;height:56rpx;" :src="isLoading?imgUrl+'/qdkbm/newimage/fhui/icon-sendbtn-gray.png':imgUrl+'/qdkbm/newimage/fhui/icon-sendbtn.png'"></image>
						</button>


				</view>
				<!-- 适配底部安全区域 -->
				<view class="safe-area-inset-bottom"></view>
			</view>
		</view>
	</view>
</template>

<script>
	import simpleMarkdown from '@/utils/simple-markdown.js'
	import mpHtml from '@/components/mp-html/mp-html'
	// import marked from 'marked';
	const markdownit = simpleMarkdown;

	export default {
		components: {
			mpHtml
		},
		name: 'ChatInterface',
		data() {
			return {
				imgUrl:this.$base.uploadImgUrl,
				height:0,
				showbox:false,
				count:0,
				titleTop: 0,
				scrollToId: "",
				messages: [],
				newMessage: '',
				isLoading: false,
				scrollTop: 0,
				isShow: true,
				streamingMessage: '',
			}
		},
		watch: {
			// messages: {
			// 	handler() {
			// 		this.$nextTick(() => {
			// 			this.scrollToBottom()
			// 		})
			// 	},
			// 	deep: true
			// }
		},
		// watch: {
		//     messages: {
		//       handler() {
		//         this.scrollToBottom()
		//       },
		//       deep: true
		//     }
		//   },
		onShow() {
			this.getList()


		},
		mounted() {
			// if (this.isShow) {
			// 	this.scrollToBottom()
			// }
			// this.scrollToBottom()
			console.log(markdownit)
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top

			const query = uni.createSelectorQuery().in(this)
			query.selectAll('.input-wrapper').boundingClientRect(data => {
				this.height = data[0].height + 10
			}).exec()
		},
		methods: {
			confirmPay(){
				uni.navigateTo({
					url:'/pages/plan/buycount'
				})
				this.showbox = false
			},
			closebox(){
				this.showbox = false
			},
			openbox(){
				this.showbox = true
			},
			refresh(){
				this.getList()
			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			},
			getList() {
				// let list = [{
				// 	"id": "1781604279872581850",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮你解答的问题吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744265027000
				// }, {
				// 	"id": "1781604279872581849",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744265023000
				// }, {
				// 	"id": "1781604279872581848",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮助你的吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744264572000
				// }, {
				// 	"id": "1781604279872581847",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744264568000
				// }, {
				// 	"id": "1781604279872581846",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮你解答的问题吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263801000
				// }, {
				// 	"id": "1781604279872581845",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263798000
				// }, {
				// 	"id": "1781604279872581844",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮你解答的问题吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263747000
				// }, {
				// 	"id": "1781604279872581843",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263745000
				// }, {
				// 	"id": "1781604279872581842",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮你解答的问题吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263635000
				// }, {
				// 	"id": "1781604279872581841",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263630000
				// }, {
				// 	"id": "1781604279872581840",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以帮助你的吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263596000
				// }, {
				// 	"id": "1781604279872581839",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263594000
				// }, {
				// 	"id": "1781604279872581838",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以帮助你的吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263539000
				// }, {
				// 	"id": "1781604279872581837",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263537000
				// }, {
				// 	"id": "1781604279872581836",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮你解答的问题吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263507000
				// }, {
				// 	"id": "1781604279872581835",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263505000
				// }, {
				// 	"id": "1781604279872581834",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以帮助你的吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263430000
				// }, {
				// 	"id": "1781604279872581833",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263428000
				// }, {
				// 	"id": "1781604279872581832",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮你解答的问题吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263389000
				// }, {
				// 	"id": "1781604279872581831",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263387000
				// }, {
				// 	"id": "1781604279872581830",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮你解答的问题吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263330000
				// }, {
				// 	"id": "1781604279872581829",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263328000
				// }, {
				// 	"id": "1781604279872581828",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮你解答的问题吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263286000
				// }, {
				// 	"id": "1781604279872581827",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263284000
				// }, {
				// 	"id": "1781604279872581826",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以帮助你的吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263237000
				// }, {
				// 	"id": "1781604279872581825",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263234000
				// }, {
				// 	"id": "1781604279872581824",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮你解答的问题吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263163000
				// }, {
				// 	"id": "1781604279872581823",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744263159000
				// }, {
				// 	"id": "1781604279872581822",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮你解答的问题吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744259477000
				// }, {
				// 	"id": "1781604279872581821",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744259474000
				// }, {
				// 	"id": "1781604279872581820",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以帮助你的吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744259400000
				// }, {
				// 	"id": "1781604279872581819",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744259398000
				// }, {
				// 	"id": "1781604279872581818",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮你解答的问题吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744259187000
				// }, {
				// 	"id": "1781604279872581817",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744259185000
				// }, {
				// 	"id": "1781604279872581816",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮助你的吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744259134000
				// }, {
				// 	"id": "1781604279872581815",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744259131000
				// }, {
				// 	"id": "1781604279872581814",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮你解答的问题吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744258959000
				// }, {
				// 	"id": "1781604279872581813",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744258956000
				// }, {
				// 	"id": "1781604279872581812",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮助你的吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744258795000
				// }, {
				// 	"id": "1781604279872581811",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744258792000
				// }, {
				// 	"id": "1781604279872581810",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮你解答的问题吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744258318000
				// }, {
				// 	"id": "1781604279872581809",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744258316000
				// }, {
				// 	"id": "1781604279872581808",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮你解答的问题吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744258206000
				// }, {
				// 	"id": "1781604279872581807",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744258203000
				// }, {
				// 	"id": "1781604279872581806",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮你解答的问题吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744258149000
				// }, {
				// 	"id": "1781604279872581805",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744258146000
				// }, {
				// 	"id": "1781604279872581804",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮你解答的问题吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744258013000
				// }, {
				// 	"id": "1781604279872581803",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744258011000
				// }, {
				// 	"id": "1781604279872581802",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！有什么我可以帮助你的吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744257842000
				// }, {
				// 	"id": "1781604279872581801",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744257839000
				// }, {
				// 	"id": "1781604279872581800",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮你解答的问题吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744257506000
				// }, {
				// 	"id": "1781604279872581799",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744257503000
				// }, {
				// 	"id": "1781604279872581798",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮你解答的问题吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744257324000
				// }, {
				// 	"id": "1781604279872581797",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744257321000
				// }, {
				// 	"id": "1781604279872581796",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮你解答的问题吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744257045000
				// }, {
				// 	"id": "1781604279872581795",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744257041000
				// }, {
				// 	"id": "1781604279872581794",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮你解答的问题吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744255971000
				// }, {
				// 	"id": "1781604279872581793",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744255968000
				// }, {
				// 	"id": "1781604279872581792",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以帮助你的吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744255881000
				// }, {
				// 	"id": "1781604279872581791",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744255876000
				// }, {
				// 	"id": "1781604279872581790",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮你解答的问题吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744255734000
				// }, {
				// 	"id": "1781604279872581789",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744255732000
				// }, {
				// 	"id": "1781604279872581788",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以帮助你的吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744255589000
				// }, {
				// 	"id": "1781604279872581787",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744255587000
				// }, {
				// 	"id": "1781604279872581786",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮你解答的问题吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744255513000
				// }, {
				// 	"id": "1781604279872581785",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744255510000
				// }, {
				// 	"id": "1781604279872581784",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮助你的吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744255403000
				// }, {
				// 	"id": "1781604279872581783",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744255396000
				// }, {
				// 	"id": "1781604279872581782",
				// 	"userId": 140,
				// 	"roleId": 2,
				// 	"title": null,
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": "你好！请问有什么我可以快速帮你解答的问题吗？",
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744255153000
				// }, {
				// 	"id": "1781604279872581781",
				// 	"userId": 140,
				// 	"roleId": 1,
				// 	"title": "你好",
				// 	"modelId": null,
				// 	"model": null,
				// 	"pinned": null,
				// 	"pinnedTime": null,
				// 	"systemMessage": null,
				// 	"temperature": null,
				// 	"content": null,
				// 	"maxTokens": null,
				// 	"maxContexts": null,
				// 	"createTime": 1744255145000
				// }]
				// list.map((item) => {
				// 	if (item.roleId == 1) {
				// 		item.role = 'user'
				// 		item.content = item.title
				// 	} else if (item.roleId = 2) {
				// 		item.role == 'assistant'
				// 		item.content = item.systemMessage
				// 	}

				// })
				// this.messages = list


				// this.$nextTick(() => {
				// 	this.scrollToId = 'item-' + (this.messages.length - 1)
				// 	console.log(this.scrollToId)
				// })

				// 在消息列表渲染完成后滚动到指定消息
				// this.$nextTick(() => {
				//     setTimeout(() => {
				//         const query = uni.createSelectorQuery().in(this);
				//         // 找到目标消息之前的所有消息
				//         const targetIndex = this.messages.findIndex(msg => msg.id === '1781604279872581781');
				//         if (targetIndex !== -1) {
				//             query.selectAll('.message').boundingClientRect(rects => {
				//                 if (rects && rects.length > 0) {
				//                     // 计算目标消息之前所有消息的高度总和
				//                     const scrollHeight = rects.slice(0, targetIndex + 1).reduce((sum, rect) => {
				//                         return sum + rect.height;
				//                     }, 0);
				//                     this.scrollTop = scrollHeight;
				//                 }
				//             }).exec();
				//         }
				//     }, 100);
				// });
				this.$apis.conversationList({
					userId: uni.getStorageSync('userId'),
					pageNo: 1,
					pageSize: 100
				}).then((res) => {
					if (res.code == 0) {
						res.data.list.map((item) => {
							if (item.roleId == 1) {
								item.role = 'user'
								item.content = item.title
							} else if (item.roleId = 2) {
								item.role == 'assistant'
								item.content = markdownit().render(item.systemMessage)
							}


						})
						this.messages = res.data.list
						this.$nextTick(() => {
							this.scrollToId = 'item-' + (this.messages.length - 1)
							console.log(this.scrollToId)
						})
					}
				})
			},

			async sendMessage() {
				if (!this.newMessage.trim() || this.isLoading) return;
				this.count = uni.getStorageSync('tws') || 0
				let count = this.count
				if(count>0){
					count--
					uni.setStorageSync('tws',count)
					const userMessage = this.newMessage.trim();
					this.messages.push({
						role: 'user',
						content: userMessage
					});
					this.newMessage = '';
					this.isLoading = true;
					const assistantMessageIndex = this.messages.length;
					this.messages.push({
						role: 'assistant',
						content: ''
					});
					try {
						await new Promise((resolve, reject) => {
							uni.request({
								url: this.$base.baseUrl+'admin-api/system/ai/chat-conversation/create',
								method: 'POST',
								data: {
									userId: uni.getStorageSync('userId'),
									title: userMessage
								},
								header: {
									'Accept': 'text/event-stream',
									'Content-Type': 'application/json',
								},
								success: (res) => {
									console.log(res)
									if (res.data) {
										const chunks = res.data.split('\n');
										this.processStreamResponse(chunks, assistantMessageIndex);
									}
									resolve();
								},
								fail: (err) => {
									reject(err);
								}
							});
						});
					} catch (error) {
						console.error('发送消息失败:', error);
						uni.showToast({
							title: '发送失败，请重试',
							icon: 'none'
						});
					} finally {
						this.isLoading = false;
					}
				} else {
					this.showbox = true
				}
			},

			// 新增处理流式响应的方法
			processStreamResponse(chunks, messageIndex) {
				let currentIndex = 0;
				const processChunk = () => {
					if (currentIndex >= chunks.length) {
						return;
					}

					const chunk = chunks[currentIndex].replace('data:', '').trim();
					console.log(chunk)
					if (chunk) {
						let str = JSON.parse(chunk).data.content
						this.$set(this.messages[messageIndex], 'content',
							this.messages[messageIndex].content + str);

						this.$nextTick(() => {
							this.scrollToId = 'item-' + (this.messages.length - 1)
							// this.scrollToBottom();
						});
					}

					currentIndex++;
					setTimeout(processChunk, 50);
				};

				processChunk();
			},
			scrollToLatestMessage() {
				const query = uni.createSelectorQuery().in(this)
				query.selectAll('.message').boundingClientRect(data => {
					if (data && data.length > 0) {
						const totalHeight = data.reduce((sum, item) => sum + item.height, 0)
						this.scrollTop = totalHeight
					}
				}).exec()
			},
			// 修改 scrollToBottom 方法
			scrollToBottom() {
				this.$nextTick(() => {
					const query = uni.createSelectorQuery().in(this);
					query.select('.messages-container').boundingClientRect((data) => {
						if (data) {
							// 设置滚动位置为容器的高度
							const scrollView = uni.createSelectorQuery().in(this).select(
								'.messages-container');
							scrollView.boundingClientRect((rect) => {
								if (rect) {
									this.scrollTop = rect.height; // 加一个较大的值确保滚动到底部
								}
							}).exec();
						}
					}).exec();
				});
			},
			// scrollToBottom() {
			// 	setTimeout(() => {
			// 		const query = uni.createSelectorQuery().in(this)
			// 		query.selectAll('.message').boundingClientRect(data => {
			// 			if (data && data.length > 0) {
			// 				const totalHeight = data.reduce((sum, item) => sum + item.height, 0)
			// 				this.scrollTop = totalHeight
			// 			}
			// 		}).exec()
			// 	}, 100)
			// },
			// scrollToBottom() {

			// 使用 uniapp 的选择器方法
			// const query = uni.createSelectorQuery().in(this)
			// query.select('.messages-container').boundingClientRect(data => {
			//   if (data) {
			//     uni.pageScrollTo({
			//       scrollTop: data.height,
			//       duration: 300
			//     })
			//   }
			// }).exec()

			// const query = uni.createSelectorQuery().in(this)
			// query.select('.messages-container').boundingClientRect(data => {
			// if (data) {
			//	this.scrollTop = data.height
			//}
			//}).exec()

			// this.$nextTick(() => {
			//        if (this.$refs.messagesContainer) {
			//          this.$refs.messagesContainer.scrollTop = this.$refs.messagesContainer.scrollHeight
			//        } else {
			//          console.log('未找到 messagesContainer ref')
			//        }
			//      })
			//}
		}
	}
</script>

<style scoped lang="scss">
	.closeicon{
		position: absolute;right:-25rpx;top:5rpx;
		image{
			width:64rpx;height:64rpx;
		}
	}
	.picbox {
		background-repeat: no-repeat;
		background-size: contain;
		width: 600rpx;
	}

	.myboxs {
		margin: 100rpx 30rpx 20rpx 30rpx;
		height: 400rpx;
		flex-direction: column;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;
		.cont{
			margin-bottom:40rpx;
			text{font-weight: 700;letter-spacing: 2rpx;
				font-size:32rpx;color:#AA7248;line-height:54rpx;
				.bold{
					color:#e6702b;margin:0 4rpx;
				}
			}
		}
		.btn{display: flex;flex-direction: row;
			button{margin:0 10rpx;
				color:#fff;padding:5rpx 50rpx;border-radius: 70rpx;font-size:32rpx;
				background-image: linear-gradient(90deg, #FF8C38 0, #FD5819 100%);
				&::after{border:none;}
			}
		}
	}

	.popbox {
		display: flex;
		position: relative;
		flex-direction: column;

		.popcont {
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
		}

		.cont {
			font-size: 28rpx;
			padding: 40rpx 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.btn {
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;

			button {
				width: 170rpx;
				height: 60rpx;
				font-size: 28rpx;
				line-height: 60rpx;
				padding: 0;
				margin: 0;
				background: #fb6b3d;
				color: #fff;

				&::after {
					border: none
				}
			}
		}
	}
	/* 添加加载动画样式 */
	.loading-dots {
		display: inline-flex;
		gap: 4rpx;
	}

	.loading-dots text {
		animation: loading 1s infinite;
	}

	.loading-dots text:nth-child(2) {
		animation-delay: 0.2s;
	}

	.loading-dots text:nth-child(3) {
		animation-delay: 0.4s;
	}

	@keyframes loading {

		0%,
		100% {
			opacity: 0.2;
		}

		50% {
			opacity: 1;
		}
	}

	.message.loading .message-content {
		display: flex;
		align-items: center;
		gap: 8px;
	}

	.loading-dots {
		display: flex;
		gap: 4px;
	}

	.loading-dots span {
		width: 8px;
		height: 8px;
		background-color: #999;
		border-radius: 50%;
		animation: loading 1s infinite ease-in-out;
	}

	@keyframes loading {

		0%,
		100% {
			transform: scale(0.3);
		}

		50% {
			transform: scale(1);
		}
	}

	.chat-container {
		height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #fff;
	}

	.messages-container {
		/* flex: 1; */
		box-sizing: border-box;
		overflow-y: auto;
		height: calc(100vh - 160rpx);
		padding: 20px;
	}

	.message {
		display: flex;
		margin-bottom: 20px;
		gap: 12px;
	}

	.avatar {
		width: 40px;
		height: 40px;
		border-radius: 50%;
		background-color: #e0e0e0;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 20px;
	}

	.message-content {
		background-color: white;
		padding: 12px 16px;
		border-radius: 12px;
		max-width: 70%;
		box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
	}

	.user {
		flex-direction: row-reverse;
	}

	.user .message-content {
		background-color: #1a8cff;
		margin-right: 20rpx;
		color: white;position: relative;
		&::before{
			position: absolute;right: -18rpx;content:"";top:50%;width:20rpx;height:30rpx;margin-top:-12rpx;
			background:url('https://pic.kefeichangduo.top/qdkbm/newimage/fhui/member-icon-right4.png') no-repeat;
			background-size: 80% auto;
		}
	}

	.input-container {
		padding: 20px;
		background-color: white;
		border-top: 1px solid #e0e0e0;
		display: flex;
		position: relative;
		align-items: center;
		gap:20;

		.icon{
			width:50rpx;
			image{
				width:40rpx;height:40rpx;
			}
		}
		.inputwrap{
			flex:1;display: flex;gap: 10px;flex-direction: row;
		}
	}

	.message-input {
		flex: 1;
		padding: 12px;
		/* border: 1px solid #e0e0e0; */
		/* border-radius: 8px; */
		resize: none;
		font-family: inherit;
		font-size: 14px;
	}

	.send-button {
		padding: 0 10rpx;
		background: transparent;
		color: #fff;
		display: flex;
		align-items: center;
		border: none;
		border-radius: 8px;
		cursor: pointer;
		font-size: 28rpx;
		&::after{border:none}
	}

	.send-button:hover {
		// background-color: #FF8B1B;
	}

	.chat-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #fff;
	}

	.messages-container {
		/* flex: 1; */
		padding: 20rpx;
		/* 为底部输入框留出空间 */
		/* padding-bottom: 320rpx; */
	}

	.message {
		display: flex;
		margin-bottom: 20rpx;
		gap: 24rpx;
	}

	.avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		background-color: #e0e0e0;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 40rpx;
	}

	.message-content {
		background-color: #f5f5f5;
		padding: 24rpx 32rpx;
		border-radius: 24rpx;
		max-width: 70%;
	}

	.user {
		flex-direction: row-reverse;
	}

	.user .message-content {
		background-color: #FF8B1B;
		color: white;
	}

	.input-wrapper {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.input-container {
		padding: 20rpx;
		display: flex;
		gap: 20rpx;
		background-color: white;
	}

	.message-input {
		flex: 1;
		padding: 20rpx 35rpx;
		background:#f0f0f0;
		/* border: 2rpx solid #e0e0e0; */
		border-radius: 25rpx;
		line-height: 40rpx;
		font-size: 28rpx;
		min-height: 40rpx;
		max-height: 200rpx;
	}

	// .send-button {
	// 	padding: 0 40rpx;
	// 	height: 80rpx;
	// 	line-height: 80rpx;
	// 	background-color: #FF8B1B;
	// 	color: white;
	// 	border: none;
	// 	border-radius: 16rpx;
	// 	font-size: 28rpx;
	// }

	// .send-button:active {
	// 	background-color: #FF8B1B;
	// }

	/* 适配底部安全区域 */
	.safe-area-inset-bottom {
		padding-bottom: constant(safe-area-inset-bottom);
		/* iOS 11.2+ */
		padding-bottom: env(safe-area-inset-bottom);
		/* iOS 11.2+ */
		background-color: #fff;
	}
</style>