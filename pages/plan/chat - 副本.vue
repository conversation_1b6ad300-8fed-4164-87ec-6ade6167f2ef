<template>
	<view>
		<view class="head" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="left">
					<view class="itemList">
						<view class="item" @tap="back">
							<button>
								<image style="width:20rpx"
									src="https://pacetupian.yuyuexiaobao.com/PACE/mall/back-light.png">
								</image>
							</button>
						</view>
					</view>
				</view>
				<view class="text">AI聊天</view>
			</view>
		</view>

		<view class="chat-container">

			<!-- 消息列表区域 -->
			<scroll-view class="messages-container" scroll-y="true" :scroll-top="scrollTop"
				:scroll-with-animation="true" ref="messagesContainer">
				<view v-for="(message, index) in messages" :key="index" :class="['message', message.role]">
					<!-- <view class="avatar">
					{{ message.role === 'user' ? '👤' : '🤖' }}
				</view> -->
					<view class="message-content">
						{{ message.content }}
					</view>
				</view>
			</scroll-view>

			<!-- 固定在底部的输入区域 -->
			<view class="input-wrapper">
				<view class="input-container">
					<textarea v-model="newMessage" @keyup.enter.exact="sendMessage" :disabled="isLoading"
						placeholder="输入问题内容" :auto-height="true" :maxlength="-1" class="message-input" />
					<button @click="sendMessage" :disabled="isLoading" class="send-button">
						{{ isLoading ? '发送中...' : '发送' }}
					</button>
				</view>
				<!-- 适配底部安全区域 -->
				<view class="safe-area-inset-bottom"></view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'ChatInterface',
		data() {
			return {
				titleTop: 0,
				messages: [],
				newMessage: '',
				isLoading: false,
				scrollTop: 0,
				isShow: true,
				currentAssistantMessage: '',
			}
		},
		watch: {
			// messages: {
			// 	handler() {
			// 		this.$nextTick(() => {
			// 			this.scrollToBottom()
			// 		})
			// 	},
			// 	deep: true
			// }
		},
		// watch: {
		//     messages: {
		//       handler() {
		//         this.scrollToBottom()
		//       },
		//       deep: true
		//     }
		//   },
		onShow() {
			this.getList()


		},
		mounted() {
			// if (this.isShow) {
			// 	this.scrollToBottom()
			// }
			// this.scrollToBottom()
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top
		},
		methods: {
			back() {
				uni.navigateBack({
					delta: 1
				})
			},
			getList() {
				this.$apis.conversationList({
					userId: uni.getStorageSync('userId'),
					pageNo: 1,
					pageSize: 100
				}).then((res) => {
					if (res.code == 0) {
						res.data.list.map((item) => {
							if (item.roleId == 1) {
								item.role = 'user'
								item.content = item.title
							} else if (item.roleId = 2) {
								item.role == 'assistant'
								item.content = item.systemMessage
							}

						})
						this.messages = res.data.list
						this.$nextTick(() => {
							this.scrollToBottom()
						})
					}
				})
			},
			async sendMessage() {
				if (!this.newMessage.trim() || this.isLoading) return

				// 添加用户消息
				this.messages.push({
					role: 'user',
					content: this.newMessage.trim()
				})

				const userMessage = this.newMessage.trim()
				this.newMessage = ''
				this.isLoading = true

				// 添加一个空的助手消息用于流式显示
				this.currentAssistantMessage = ''
				this.messages.push({
					role: 'assistant',
					content: this.currentAssistantMessage
				})
				console.log('🚀 开始发送消息')
				console.log('用户ID:', uni.getStorageSync('userId'))
				console.log('发送的消息:', userMessage)

				 uni.request({
						url: 'http://192.168.10.205:48080/admin-api/system/ai/chat-conversation/create',
						method: 'POST',
						data: {
							userId: uni.getStorageSync('userId'),
							title: userMessage
						},
						header: {
							 'Accept': 'text/event-stream',
							        'Content-Type': 'application/json',
							        'Connection': 'keep-alive',
							        'Cache-Control': 'no-cache'
						},
						responseType: 'text',
						enableChunked: true,
						timeout: 30000,
						// onChunkReceived: (response) => {
						// 	console.log('收到数据块:', response)
						// 	if (!response.data) {
						// 		console.log('数据块为空')
						// 		return
						// 	}

						// 	try {
						// 		// 尝试不同的解码方式
						// 		let chunk
						// 		if (response.data instanceof ArrayBuffer) {
						// 			chunk = new TextDecoder().decode(response.data)
						// 		} else if (typeof response.data === 'string') {
						// 			chunk = response.data
						// 		} else {
						// 			chunk = JSON.stringify(response.data)
						// 		}

						// 		console.log('解码后的数据:', chunk)

						// 		// 更新消息
						// 		this.currentAssistantMessage += chunk
						// 		this.messages[this.messages.length - 1].content = this
						// 			.currentAssistantMessage

						// 		// 强制更新视图
						// 		this.$forceUpdate()
						// 	} catch (error) {
						// 		console.error('数据处理错误:', error)
						// 	}
						// },
						success: (res) => {
							this.processStreamResponse(res.data);
							console.log('请求成功:', res)
							console.log('✅ 请求成功:', res)
							console.log('响应状态码:', res.statusCode)
							console.log('响应头:', res.header)
						},
						fail: (error) => {
							console.error('请求失败:', error)
							uni.showToast({
								title: '发送失败，请重试',
								icon: 'none'
							})
						}
					})

				
			},
			
			processStreamResponse(data, aiIndex) {
			  const chunks = data.split('\n');
			  let chunkIndex = 0;
			  const interval = setInterval(() => {
			    if (chunkIndex >= chunks.length) {
			      clearInterval(interval);
			      this.isLoading = false;
			      return;
			    }
			 
			    const chunk = chunks[chunkIndex].replace('data:', '').trim();
			    if (chunk) {
			      this.messages[aiIndex].text += chunk;
			      this.$forceUpdate();
			    }
			    chunkIndex++;
			  }, 50);
			},
			scrollToLatestMessage() {
				const query = uni.createSelectorQuery().in(this)
				query.selectAll('.message').boundingClientRect(data => {
					if (data && data.length > 0) {
						const totalHeight = data.reduce((sum, item) => sum + item.height, 0)
						this.scrollTop = totalHeight
					}
				}).exec()
			},
			scrollToBottom() {
				setTimeout(() => {
					const query = uni.createSelectorQuery().in(this)
					query.selectAll('.message').boundingClientRect(data => {
						if (data && data.length > 0) {
							const totalHeight = data.reduce((sum, item) => sum + item.height, 0)
							this.scrollTop = totalHeight
						}
					}).exec()
				}, 100)
			},
			// scrollToBottom() {

			// 使用 uniapp 的选择器方法
			// const query = uni.createSelectorQuery().in(this)
			// query.select('.messages-container').boundingClientRect(data => {
			//   if (data) {
			//     uni.pageScrollTo({
			//       scrollTop: data.height,
			//       duration: 300
			//     })
			//   }
			// }).exec()

			// const query = uni.createSelectorQuery().in(this)
			// query.select('.messages-container').boundingClientRect(data => {
			// if (data) {
			//	this.scrollTop = data.height
			//}
			//}).exec()

			// this.$nextTick(() => {
			//        if (this.$refs.messagesContainer) {
			//          this.$refs.messagesContainer.scrollTop = this.$refs.messagesContainer.scrollHeight
			//        } else {
			//          console.log('未找到 messagesContainer ref')
			//        }
			//      })
			//}
		}
	}
</script>

<style scoped>
	.message.loading .message-content {
		display: flex;
		align-items: center;
		gap: 8px;
	}

	.loading-dots {
		display: flex;
		gap: 4px;
	}

	.loading-dots span {
		width: 8px;
		height: 8px;
		background-color: #999;
		border-radius: 50%;
		animation: loading 1s infinite ease-in-out;
	}

	@keyframes loading {

		0%,
		100% {
			transform: scale(0.3);
		}

		50% {
			transform: scale(1);
		}
	}

	.chat-container {
		height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;
	}

	.messages-container {
		flex: 1;
		box-sizing: border-box;
		overflow-y: auto;
		padding: 20px;
	}

	.message {
		display: flex;
		margin-bottom: 20px;
		gap: 12px;
	}

	.avatar {
		width: 40px;
		height: 40px;
		border-radius: 50%;
		background-color: #e0e0e0;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 20px;
	}

	.message-content {
		background-color: white;
		padding: 12px 16px;
		border-radius: 12px;
		max-width: 70%;
		box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
	}

	.user {
		flex-direction: row-reverse;
	}

	.user .message-content {
		background-color: #1a8cff;
		color: white;
	}

	.input-container {
		padding: 20px;
		background-color: white;
		border-top: 1px solid #e0e0e0;
		display: flex;
		gap: 10px;
	}

	.message-input {
		flex: 1;
		padding: 12px;
		border: 1px solid #e0e0e0;
		border-radius: 8px;
		resize: none;
		font-family: inherit;
		font-size: 14px;
	}

	.send-button {
		padding: 0 20px;
		background-color: #1a8cff;
		color: white;
		border: none;
		border-radius: 8px;
		cursor: pointer;
		font-size: 14px;
	}

	.send-button:hover {
		background-color: #0066cc;
	}

	.chat-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
	}

	.messages-container {
		flex: 1;
		padding: 20rpx;
		/* 为底部输入框留出空间 */
		padding-bottom: 180rpx;
	}

	.message {
		display: flex;
		margin-bottom: 20rpx;
		gap: 24rpx;
	}

	.avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		background-color: #e0e0e0;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 40rpx;
	}

	.message-content {
		background-color: white;
		padding: 24rpx 32rpx;
		border-radius: 24rpx;
		max-width: 70%;
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}

	.user {
		flex-direction: row-reverse;
	}

	.user .message-content {
		background-color: #1a8cff;
		color: white;
	}

	.input-wrapper {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.input-container {
		padding: 20rpx;
		display: flex;
		gap: 20rpx;
		align-items: flex-end;
		background-color: white;
	}

	.message-input {
		flex: 1;
		padding: 20rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 16rpx;
		font-size: 28rpx;
		min-height: 80rpx;
		max-height: 200rpx;
	}

	.send-button {
		padding: 0 40rpx;
		height: 80rpx;
		line-height: 80rpx;
		background-color: #1a8cff;
		color: white;
		border: none;
		border-radius: 16rpx;
		font-size: 28rpx;
	}

	.send-button:active {
		background-color: #0066cc;
	}

	/* 适配底部安全区域 */
	.safe-area-inset-bottom {
		padding-bottom: constant(safe-area-inset-bottom);
		/* iOS 11.2+ */
		padding-bottom: env(safe-area-inset-bottom);
		/* iOS 11.2+ */
		background-color: #fff;
	}
</style>