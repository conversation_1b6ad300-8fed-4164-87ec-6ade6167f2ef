<template>
	<view>
		<view class="head" style="background: #fff;" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="left" @tap="back">
					<view class="itemList">
						<view class="item">
							<button>
								<image style="width:20rpx"
									src="https://pic.kefeichangduo.top/qdkbm/newimage/fhui/back.png">
								</image>
							</button>
						</view>
					</view>
				</view>
				<view class="text" style="color: #333;">专业解析</view>
			</view>
			<view class="search-input" @tap="search">
				<view class="icon">
					<image :src="imgUrl+'/qdkbm/newimage/fhui/icon-search4.png'"></image>
				</view>
				<view class="inputwrap">
					<input type="text" disabled="true" class="inputstyle" placeholder="请输入" placeholder-class="gray" />
					<button>搜索</button>
				</view>
			</view>
		</view>
		<view class="loading-mask" v-if="loading">
			<view class="loading-spinner"></view>
		</view>
		<view class="counter-warp">

			<wyh-tree-select2 :height="height" :items="items" :activeIds="yxzy_ids" :max="1" @clickItem="onItem"
				@onChange="bindChange($event, 'yxzy_ids')" />
		</view>
	</view>
</template>
<script>
	import wyhTreeSelect2 from '@/components/wyh-tree-select/wyh-tree-select2.vue'
	export default {
		components: {
			wyhTreeSelect2
		},
		name: 'major',
		data() {
			return {
				imgUrl: this.$base.uploadImgUrl,
				yxzy_ids: [],
				loading:false,
				items: [],
				height: 0,
				titleTop: 0
			}
		},
		watch: {

		},
		onShow() {
			this.getSubject()
		},
		mounted() {},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top
			const query = uni.createSelectorQuery().in(this)
			query.selectAll('.head').boundingClientRect(data => {
				console.log(data)
				this.height = data[0].height + this.titleTop - 4
				console.log(this.height)
			}).exec()
		},
		methods: {
			search(){
				uni.navigateTo({
					url:'/pages/plan/majorSearch'
				})
			},
			onItem(e) {
				uni.navigateTo({
					url: '/pages/plan/majorDetail?majorName=' + e.name
				})
			},
			getSubject() {
				this.loading = true
				this.$apis.getmajordata({
					name: ""
				}).then((res) => {
					if (res.code == 0) {
						let items = JSON.parse(res.data).body
						items.map((item) => {
							item.text = item.name.substring(0, item.name.length - 2)
							item.badge = 'number'
							if (item.children.length > 0) {
								item.children.map((newitem) => {
									newitem.text = newitem.name
									newitem.isshow = true
									if (newitem.children.length > 0) {
										newitem.children.map((mynewitem) => {
											mynewitem.text = mynewitem.name
										})
									}
								})
							}
						})
						this.items = items
						setTimeout(()=>{
							this.loading = false
						},600)
					} else{
						setTimeout(()=>{
							this.loading = false
						},600)
					}
				}).catch((err)=>{
					setTimeout(()=>{
						this.loading = false
					},600)
				})
			},
			bindChange(ids, key, popupRef) {
				this[key] = ids;
				console.log(this.yxzy_ids)
				if (popupRef) {
					this.$refs[popupRef].close();
				}
			},
			handelShowDialog() {
				this.showDialog = true;
			},
			doConfirmCategorys(items) {
				let arr = [];
				for (let i = 0; i < items.length; i++) {
					arr.push(items[i].label);
				}
				this.selectedItemsStr = arr.join(',');
			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			}
		}
	}
</script>
<style>
	page {
		background: #f5f5f5
	}
	.gray{
		color:#999;
	}
</style>
<style scoped lang="scss">
	@-webkit-keyframes countloading{0%{transform:rotate(180deg)}100%{transform:rotate(0deg)}}	
	@keyframes countloading{0%{transform:rotate(180deg)}100%{transform:rotate(0deg)}}
	.loading-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
	
		.loading-spinner {
			width: 50rpx;
			height: 50rpx;
			// border: 8rpx solid #c8a178;
			// border-top-color: transparent;
			// border-radius: 50%;
			 animation:countloading 1s ease infinite;
			// animation:progress-active 2.4s cubic-bezier(.23,1,.32,1) infinite;
			 background-image:url('https://pic.kefeichangduo.top/qdkbm/newimage/fhui/waiting.png');background-position:50%;background-repeat:no-repeat;background-size:contain;
		}
	}
	
	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
	
		100% {
			transform: rotate(360deg);
		}
	}
	.search-input {
		margin: 20rpx 30rpx;
		align-items: center;
		height: 72rpx;
		background: #f1f1f1;
		border-radius: 48rpx;
		display: flex;
		flex-direction: row;

		.icon {
			width: 100rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			image {
				width: 40rpx;
				height: 40rpx;
			}
		}

		.inputwrap {
			flex: 1;
			position: relative;

			.inputstyle {
				width: 100%;
				height: 72rpx;
				line-height: 72rpx;
				font-size: 28rpx;
				color: #333;
			}

			button {
				z-index: 99;
				position: absolute;
				right: 0;
				top: 0;
				height: 72rpx;
				line-height: 72rpx;
				color: #fff;
				font-size: 32rpx;
				border-radius: 48rpx;
				background: #FF8918;
				padding: 0 50rpx;
				margin: 0;
				min-width: inherit;

				&::after {
					border: none
				}
			}
		}
	}

	.counter-warp {}

	.label {
		margin: 10px;
	}

	button {
		&::after {
			border: none
		}

	}
</style>