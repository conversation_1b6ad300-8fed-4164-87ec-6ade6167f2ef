<template>
	<view class="agent-ui">
		<view class="head" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="left" @tap="back">
					<view class="itemList">
						<view class="item" >
							<button>
								<image style="width:20rpx"
									src="https://pacetupian.yuyuexiaobao.com/PACE/mall/back-light.png">
								</image>
							</button>
						</view>
					</view>
				</view>
				<view class="text">智能问答</view>
			</view>
		</view>
		<u-popup :round="8" mode="center" closeOnClickOverlay bgColor="transparent" :show="showbox" @close="closebox"
			@open="openbox">
			<view class="picbox"
				style="background-image: url('https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/pop-bg.png');">
				<view class="closeicon" @tap="closebox">
					<image src="https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/fh-cancelbtn.png"></image>
				</view>
				<view class="successimg" style="position: absolute;left:50%;margin-left:-90rpx;top:-10rpx;z-index:99;">
					<image style="width:220rpx;" mode="widthFix"
						src="https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/popicon1.png"></image>
				</view>
				<view class="myboxs">
					<view class="cont">
						<text>10次免费提问已用完！请购买智能问答礼包</text>
					</view>
					<view class="btn"><button @tap="confirmPay">去购买</button> <button @tap="closebox">关闭页面</button>
					</view>
				</view>
			</view>

		</u-popup>



			<!-- 消息列表区域 -->
			<scroll-view ref="messagesContainer" @wheel="onWheel" :enhanced="true" @scroll="onScroll"
				@dragstart="handleScrollStart" class="main"
				:style="{height: windowInfo.screenHeight - footerHeight - (chatMode === 'bot' ? 40 : 0) + 'px'}"
				scroll-y="true" :scroll-top="viewTop" :scroll-into-view="scrollTo" lower-threshold="1"
				@scrolltolower="handleScrollToLower" :show-scrollbar="false" :refresher-enabled="showPullRefresh"
				refresher-threshold="80" @refresherrefresh="handelRefresh" :refresher-triggered="triggered"
				:bounces="false">
				<view style="font-size: 28rpx;color:#333;background:#f5f5f5;border-radius: 16rpx;padding:20rpx;">
					作为您的志愿规划小伙伴，可以帮您解决一些相关问题。
				</view>

				<view v-if="chatMode === 'bot' && showPullRefresh" class="tips">
					{{refreshText}}
				</view>
				<view v-if="chatMode === 'model'" class="nav">
					<image :src="bot.avatar||modelConfig.logo" mode="aspectFill" class="avatar" />
					<view style="line-height: 47px; font-size: 20px; font-weight: 500;">
						{{chatMode==='bot'?bot.name:modelConfig.modelProvider}}
					</view>
					<view style="line-height: 26px;padding: 0px 16px; font-size: 32rpx;">
						{{chatMode==='bot'?"":modelConfig.welcomeMsg}}
					</view>
				</view>
				<block v-for="(item, index) in chatRecords" :key="item.record_id">
					<!-- 系统聊天 -->
					<view class="system" :style="{'padding-left': showBotAvatar?'80rpx':'0rpx'}"
						v-if="item.role==='assistant'">
						<view class="avatar-left" v-if="showBotAvatar">
							<image :src="chatMode==='bot'?bot.avatar:modelConfig.logo" mode="aspectFill"
								style="width: 56rpx;height: 56rpx; border-radius: 28rpx;" />
						</view>
						<view>
							<!-- 最后一条消息，并且是发送状态显示发送中 -->
							<block v-if="(chatRecords.length-1)===index&&chatStatus===1">
								<view
									style="display: flex;align-items: center; gap: 4px; font-size: 32rpx;line-height: 1.8;">
									<image src="https://pic.kefeichangduo.top/qdkbm/newimage/fhui/tools/loading.svg"
										mode="aspectFill" style="width: 14px;height: 14px;" /> 请稍等，正在卖力思考中 🤔
								</view>
							</block>
							<block v-else>
								<!-- 推理过程 -->
								<mpHtml :content="item.content"></mpHtml>

							</block>
						</view>
					</view>
					<!-- 用户输入 -->
					<view class="userContent" v-if="item.role==='user'">
						<view class="user" :style="{'padding-left': showBotAvatar?'80rpx':'0rpx'}">
							<view class="user_content">
								{{item.content}}
							</view>
						</view>
					</view>
				</block>
				<!-- 推荐问题 -->
				<block v-for="(item, index) in questions" :key="index">
					<view class="questions" :style="{'padding-left': showBotAvatar?'80rpx':'0rpx'}">
						<view class="question_content" @tap="handleSendMessage" :data-message="item">{{item}}</view>
					</view>
				</block>
				<view id="scroll-bottom" style="width: 100%;height: 20px;"></view>
			</scroll-view>

			<!-- 固定在底部的输入区域 -->
			<view class="footer">
				<view class="feature_list" v-if="showFeatureList">
					<view @tap="handleClickWebSearch"
						:class="'webSearchSwitch ' + (useWebSearch ? 'feature_enable' : '')">
						<image
							:src="useWebSearch ? 'https://pic.kefeichangduo.top/qdkbm/newimage/fhui/tools/internetUse.svg' : 'https://pic.kefeichangduo.top/qdkbm/newimage/fhui/tools/internet.svg'"
							mode="" style="width: 40rpx;height:30px;margin-right: 10rpx" />
						<text>联网搜索</text>
					</view>
				</view>
				<view class="file_list" v-if="showFileList">
					<chatFile :enableDel="true" v-for="(item, index) in sendFileList" :key="item.tempId"
						:fileData="item" @removeChild="handleRemoveChild" @changeChild="handleChangeChild"></chatFile>
				</view>
				<view class="foot_function">
					<!-- <scroll-view class="img-box" scroll-x="true" v-if="!!imageList.length">
			        <block v-for="(item, index) in imageList" :key="item.tempFilePath">
			          <view class="img-preview">
			            <image :src="item.tempFilePath" alt="" model='aspectFill' class="img-preview-image" />
			            <view class="img-preview-loading" v-if="!!!item.base64Url"></view>
			            <image src="https://pic.kefeichangduo.top/qdkbm/newimage/fhui/tools/close.svg" mode="aspectFill" class="img-preview-close" @tap="deleteImg" :data-index="index" />
			          </view>
			        </block>
			      </scroll-view> -->

					<view class="input_box">
						<view class="input_inner_box">
							<textarea class="input" :value="inputValue" maxlength="1024" @focus="bindInputFocus"
								@input="bindKeyInput" placeholder="说点什么吧" @confirm="sendMessage" confirm-type="send"
								adjust-position cursor-spacing="40" :auto-height="true" :show-confirm-bar="false"
								@linechange="handleLineChange" />
						</view>
						<view class="right_btns">
							<!-- 加号 -->
							<image src="https://pic.kefeichangduo.top/qdkbm/newimage/fhui/tools/set.svg" class="set"
								mode="widthFix" @tap="handleClickTools" />
							<!-- 发送按钮 -->
							<image src="https://pic.kefeichangduo.top/qdkbm/newimage/fhui/tools/send.svg" class="set"
								mode="widthFix" v-if="!!inputValue&&chatStatus===0" @tap="sendMessage"
								style="transform: rotate(-40deg);transform-origin: 8px 8px" />
							<!-- 暂停按钮 -->
							<image src="https://pic.kefeichangduo.top/qdkbm/newimage/fhui/tools/stop.svg" class="set"
								mode="widthFix" v-if="!(chatStatus===0)" @tap="stop" />
						</view>
					</view>
				</view>
				<!-- 底部工具栏 -->
				<view class="tool_box" v-if="showTools">
					<view class="function" @tap="clearChatRecords">
						<image src="https://pic.kefeichangduo.top/qdkbm/newimage/fhui/tools/clear.svg" alt="widthFix"
							class="icon" />
						<text class="text_desc">清除</text>
					</view>
					<view v-if="showUploadImg && chatMode === 'bot'" class="function" @tap="handleAlbum">
						<image src="https://pic.kefeichangduo.top/qdkbm/newimage/fhui/tools/uploadImg.svg"
							alt="widthFix" class="icon" />
						<text class="text_desc">图片</text>
					</view>
					<view v-if="showUploadFile && chatMode === 'bot'" class="function" @tap="handleUploadMessageFile">
						<image src="https://pic.kefeichangduo.top/qdkbm/newimage/fhui/tools/wechat.svg" alt="widthFix"
							class="icon" />
						<text class="text_desc">微信文件</text>
					</view>
					<view v-if="showUploadImg && chatMode === 'bot'" class="function" @tap="handleCamera">
						<image src="https://pic.kefeichangduo.top/qdkbm/newimage/fhui/tools/camera.svg" alt="widthFix"
							class="icon" />
						<text class="text_desc">相机</text>
					</view>
				</view>
			</view>

	</view>
</template>

<script>
	import simpleMarkdown from '@/utils/simple-markdown.js'
	import mpHtml from '@/components/mp-html/mp-html'
	// import marked from 'marked';
	const markdownit = simpleMarkdown;

	export default {
		components: {
			mpHtml
		},
		name: 'ChatInterface',
		data() {
			return {
				chatMode:'bot',
				showBotAvatar: false,
				modelConfig: {
				      modelProvider: "hunyuan-open", // 大模型服务厂商
				      quickResponseModel: "hunyuan-lite", // 快速响应模型 （混元 turbo, gpt4 turbo版，deepseek v3等）
				      logo: "", // model 头像
				      welcomeMsg: "欢迎语", // model 欢迎语
				    },
				agentConfig: {
				      botId: "bot-256da09b", // agent id,
				      allowWebSearch: true, // 允许客户端选择启用联网搜索
				      allowUploadFile: true, // 允许上传文件
				      allowPullRefresh: true // 允许下拉刷新
				    },
				imgUrl: this.$base.uploadImgUrl,
				titleTop: 0,
				isLoading: true, // 判断是否尚在加载中
				article: {},
				windowInfo: uni.getSystemInfoSync(),
				bot: {},
				inputValue: "",
				output: "",
				chatRecords: [],
				setPanelVisibility: false,
				questions: [],
				scrollTop: 0, // 文字撑起来后能滚动的最大高度
				viewTop: 0, // 根据实际情况，可能用户手动滚动，需要记录当前滚动的位置
				scrollTo: "", // 快速定位到指定元素，置底用
				scrollTimer: null, //
				manualScroll: false, // 当前为手动滚动/自动滚动
				showTools: false, // 展示底部工具栏
				showFileList: false, // 展示输入框顶部文件行
				showTopBar: false, // 展示顶部bar
				sendFileList: [],
				footerHeight: 73,
				lastScrollTop: 0,
				showUploadFile: true,
				showUploadImg: false,
				showWebSearchSwitch: false,
				showPullRefresh: true,
				useWebSearch: false,
				showFeatureList: false,
				chatStatus: 0, // 页面状态： 0-正常状态，可输入，可发送， 1-发送中 2-思考中 3-输出content中
				triggered: false,
				page: 1,
				size: 10,
				total: 0,
				refreshText: "下拉加载历史记录",
				contentHeightInScrollViewTop: 0, // scroll区域顶部固定区域高度
				shouldAddScrollTop: false,
				isShowFeedback: false,
				feedbackRecordId: '',
				feedbackType: "",
				textareaHeight: 50,
				curLineCount: 1
			}
		},
		watch: {
			// messages: {
			// 	handler() {
			// 		this.$nextTick(() => {
			// 			this.scrollToBottom()
			// 		})
			// 	},
			// 	deep: true
			// }
		},
		// watch: {
		//     messages: {
		//       handler() {
		//         this.scrollToBottom()
		//       },
		//       deep: true
		//     }
		//   },
		onShow() {
			this.getList()

		},
		mounted() {
			// if (this.isShow) {
			// 	this.scrollToBottom()
			// }
			// this.scrollToBottom()
			console.log(markdownit)
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top

			// const query = uni.createSelectorQuery().in(this)
			// query.selectAll('.input-wrapper').boundingClientRect(data => {
			// 	this.height = data[0].height + 10
			// }).exec()
		},

		methods: {
			getList(){
				this.$apis.conversationList({
					userId: uni.getStorageSync('userId'),
					pageNo: 1,
					pageSize: 100
				}).then((res) => {
					if (res.code == 0) {
						res.data.list.map((item) => {
							if (item.roleId == 1) {
								item.role = 'user'
								item.content = item.title
							} else if (item.roleId = 2) {
								item.role == 'assistant'
								item.content = markdownit().render(item.systemMessage)
							}


						})
						this.chatRecords = res.data.list

					}
				})
			},
			handleLineChange: function(e) {
				console.log('linechange', e.detail.lineCount)
				// 查foot-function height
				const self = this
				const query = uni.createSelectorQuery().in(this)
				query.select('.foot_function').boundingClientRect(function(res) {
					if (res) {
						if (res.height < self.textareaHeight) {

							self.footerHeight = self.footerHeight - (self.textareaHeight - res.height)

						}
						if (res.height > self.textareaHeight) {

							self.footerHeight = self.footerHeight + (res.height - self.textareaHeight)

						}

						self.textareaHeight = res.height

					} else {
						console.log('未找到指定元素');
					}
				}).exec();
			},
			closefeedback: function() {

				this.isShowFeedback = false
				this.feedbackRecordId = ''
				this.feedbackType = ''

			},
			// 滚动相关处理
			calculateContentHeight() {
				return new Promise((resolve) => {
					const query = uni.createSelectorQuery().in(this);
					query
						.selectAll(".main >>> .system, .main >>> .userContent")
						.boundingClientRect((rects) => {
							let totalHeight = 0;
							rects.forEach((rect) => {
								totalHeight += rect.height;
							});
							resolve(totalHeight);
						})
						.exec();
				});
			},
			calculateContentInTop() {
				return new Promise((resolve) => {
					const query = uni.createSelectorQuery().in(this);
					query
						.selectAll(".main >>> .nav, .main >>> .tips")
						.boundingClientRect((rects) => {
							let totalHeight = 0;
							rects.forEach((rect) => {
								totalHeight += rect.height;
							});
							resolve(totalHeight);
						})
						.exec();
				});
			},
			onWheel: function(e) {
				if (!this.manualScroll && e.detail.deltaY < 0) {

					this.manualScroll = true

				}
			},
			onScroll: function(e) {
				if (e.detail.scrollTop < this.lastScrollTop) {

					this.manualScroll = true

				}


				this.lastScrollTop = e.detail.scrollTop


				if (this.scrollTimer) {
					clearTimeout(this.scrollTimer);
				}


				this.scrollTimer = setTimeout(() => {
					const newTop = Math.max(this.scrollTop, e.detail.scrollTop);
					if (this.manualScroll) {

						this.scrollTop = newTop

					} else {

						this.scrollTop = newTop
						this.viewTop = newTop

					}
				}, 100)

			},
			handleScrollStart: function(e) {
				if (e.detail.scrollTop > 0 && !this.manualScroll) {
					this.manualScroll = true
				}
			},
			handleScrollToLower: function(e) {

				this.manualScroll = false

			},
			autoToBottom: function() {

				this.manualScroll = false
				this.scrollTo = "scroll-bottom"

			},
			bindInputFocus: function(e) {

				this.manualScroll = false

				this.autoToBottom();
			},
			bindKeyInput: function(e) {

				this.inputValue = e.detail.value

			},
			handelRefresh: function(e) {

				this.triggered = true
				this.refreshText = "刷新中"
			},
		clearChatRecords: function() {
			const chatMode = this.chatMode;
			const {
				bot
			} = this;

			this.showTools = false

			if (chatMode === "model") {

				this.chatRecords = []
				this.chatStatus = 0

				return;
			}
			if (this.chatRecords.length === 1) {
				return;
			}
			const record = {
				content: bot.welcomeMessage,
				record_id: "record_id" + String(+new Date() + 10),
				role: "assistant",
				hiddenBtnGround: true,
			};
			const questions = randomSelectInitquestion(bot.initQuestions, 3);

			this.chatRecords = [record]
			this.chatStatus = 0
			this.page = 1

		},




		stop: function() {
			this.autoToBottom();
			const {
				chatRecords,
				chatStatus
			} = this.data;
			const newChatRecords = [...chatRecords];
			const record = newChatRecords[newChatRecords.length - 1];
			if (chatStatus === 1) {
				record.content = "已暂停生成";
			}
			if (chatStatus === 2) {
				record.pauseThinking = true;
			}

				this.chatRecords = newChatRecords
				this.manualScroll = false
				this.chatStatus = 0

		},

		sendMessage: async function(event) {
			const {
				message
			} = event.currentTarget.dataset;
			let {
				inputValue,
				bot,
				agentConfig,
				chatRecords,
				chatStatus,
				modelConfig,
			} = this;
			if (chatStatus !== 0) {
				return;
			}
			if (message) {
				inputValue = message;
			}
			if (!inputValue) {
				return;
			}
			const {
				modelProvider,
				quickResponseModel
			} = modelConfig;
			const chatMode = this.chatMode;
			const userRecord = {
				content: inputValue,
				record_id: "record_id" + String(+new Date() - 10),
				role: "user"
			};
			const record = {
				content: "",
				record_id: "record_id" + String(+new Date() + 10),
				role: "assistant",
				hiddenBtnGround: true,
			};

				this.inputValue = ""
				this.questions = []
				this.chatRecords = [...chatRecords, userRecord, record]
				this.chatStatus =  1


			this.autoToBottom();

			if (chatMode === "bot") {
				const res = await new Promise((resolve, reject) => {
							uni.request({
								url: this.$base.baseUrl+'admin-api/system/ai/chat-conversation/create',
								method: 'POST',
								data: {
									userId: uni.getStorageSync('userId'),
									title: inputValue
								},
								header: {
									'Accept': 'text/event-stream',
									'Content-Type': 'application/json',
								},
								success: (res) => {
									console.log(res)
									if (res.data) {
										const chunks = res.data.split('\n');
										this.processStreamResponse(chunks, assistantMessageIndex);
									}
									resolve();
								},
								fail: (err) => {
									reject(err);
								}
							});
						});
				let contentText = "";
				let reasoningContentText = "";
				let isManuallyPaused = false;
				let startTime = null;
				let endTime = null;
				let index = 0;
				const data = res.result;
				const newValue = [...this.chatRecords];
				const lastValueIndex = newValue.length - 1;
				const lastValue = newValue[lastValueIndex];

				lastValue.content = data.content || "";
				lastValue.record_id = data.record_id || "record_id" + String(+new Date());

					this[`chatRecords[${lastValueIndex}].content`] = lastValue.content
					this[`chatRecords[${lastValueIndex}].record_id`] = lastValue.record_id
					this.chatStatus = 0



			}

		},
		processStreamResponse(chunks, messageIndex) {
			let currentIndex = 0;
			const processChunk = () => {
				if (currentIndex >= chunks.length) {
					return;
				}

				const chunk = chunks[currentIndex].replace('data:', '').trim();
				console.log(chunk)
				if (chunk) {
					let str = JSON.parse(chunk).data.content
					this.$set(this.chatRecords[messageIndex], 'content',
						this.chatRecords[messageIndex].content + str);


				}
		        console.log(this.chatRecords)
				currentIndex++;
				setTimeout(processChunk, 50);
			};

			processChunk();
		},
		toBottom: async function(unit) {
			const addUnit = unit === undefined ? 4 : unit;
			if (this.shouldAddScrollTop) {
				const newTop = this.scrollTop + addUnit;
				if (this.manualScroll) {

						this.scrollTop = newTop

				} else {

						this.scrollTop = newTop
						this.viewTop = newTop

				}
				return;
			}

			const clientHeight =
				this.windowInfo.screenHeight -
				this.footerHeight -
				(this.chatMode === "bot" ? 40 : 0);
			const contentHeight =
				(await this.calculateContentHeight()) +
				(this.contentHeightInScrollViewTop ||
					(await this.calculateContentInTop()));
			if (clientHeight - contentHeight < 10) {

					this.shouldAddScrollTop = true
			}
		},
		copyChatRecord: function(e) {
			const {
				content
			} = e.currentTarget.dataset;
			uni.setClipboardData({
				data: content,
				success: function(res) {
					uni.showToast({
						title: "复制成功",
						icon: "success",
					});
				},
			});
		},


		copyUrl: function(e) {
			const {
				url
			} = e.currentTarget.dataset;
			console.log(url);
			uni.setClipboardData({
				data: url,
				success: function(res) {
					uni.showToast({
						title: "复制成功",
						icon: "success",
					});
				},
			});
		}
	}

	}
</script>

<style scoped lang="scss">
	/* components/agent-ui/index.wxss */
	.agent-ui {
		width: 750rpx;
		height: 100vh;
		position: relative;
	}

	.nav {
		width: 750rpx;
		padding: 20px 0px 0px 0px;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.navBar {
		height: 40px;
		width: 100%;
		box-shadow: 0 16px 16px #fff;
		/* background: linear-gradient(to bottom,
	    rgba(245, 245, 245, 0.98) 60%,
	    rgba(235, 235, 235, 0.92)
	  ); */
		/* backdrop-filter: blur(20rpx); */
		/* border-bottom: 1rpx solid rgba(0, 0, 0, 0.08); */
		/* box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
	  border-bottom: none; */
		/* margin-bottom: 10px */
	}

	/* .tips {
	  width: 100%;
	  font-size: 12px;
	  display: flex;
	  align-items: center;
	  justify-content: center;
	  height: 30px;
	  color: rgb(95, 92, 92)
	} */

	.tips {
		display: flex;
		align-items: center;
		gap: 12px;
		width: 100%;
		color: rgb(128, 128, 128);
		font-size: 12px;
		height: 52px;
	}

	.tips::before,
	.tips::after {
		content: '';
		display: inline-block;
		height: 1px;
		transform: scaleY(.5);
		flex-grow: 1;
		border-radius: 2px;
	}

	.tips::before {
		background-image: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.5));
	}

	.tips::after {
		background-image: linear-gradient(to left, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.5));
	}

	.nav-content {
		height: 40px;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 12px;
	}

	.bot-avatar {
		width: 36px;
		height: 36px;
		border-radius: 50%;
		border: 2px solid rgba(255, 255, 255, 0.8);
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	}

	.bot-name {
		font-size: 18px;
		font-weight: 500;
		color: #333;
	}

	.main {
		background-color: #fff;
		/* padding-top:10px; */
	}

	.share_btn {
		background-color: #fff;
		margin: 0px !important;
		padding: 0px !important;
		width: 36rpx !important;
		height: 36rpx;
	}

	.avatar {
		width: 160rpx;
		height: 160rpx;
		border-radius: 75rpx;
	}

	.questions {
		margin: 0px 16px 10px 16px;
		/* background-color: blueviolet; */
	}

	.question_content {
		background-color: #f5f5f5;
		padding: 16rpx 24rpx;
		border-radius: 12px;
		display: inline-block;
		font-size: 14px;
		font-weight: 300;
	}

	.footer {
		width: 100%;
		min-height: 80px;
		max-height: 380px;
		/* background-color: aquamarine; */
		position: absolute;
		bottom: 0;
	}

	.foot_function {
		margin: 8px 8px 15px;
	}

	.footer .file_list {
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
		gap: 10px;
		padding: 0px 16px;
		overflow-x: scroll;
		height: 80px;
	}

	.img-box {
		position: absolute;
		top: -100px;
		left: 0px;
		white-space: nowrap;
		/* 防止内部元素换行 */
		width: 100%;
		/* 设置容器宽度 */
		background-color: #fff;
	}

	.img-preview {
		display: inline-block;
		width: 80px;
		height: 80px;
		margin-right: 8px;
		position: relative;
		margin-top: 10px;
	}

	.img-preview-image {
		width: 80px;
		height: 80px;
		border-radius: 10px;
	}

	.img-preview-loading {
		width: 100%;
		height: 100%;
		position: absolute;
		top: 0px;
		left: 0px;
		background-color: #eee;
		border-radius: 10px;
	}

	.img-preview-close {
		width: 16px;
		height: 16px;
		position: absolute;
		right: -8px;
		top: -8px;
		/* background-color: blueviolet; */
	}

	.input_box {
		/* display: flex; */
		/* width: 100%; */
		display: flex;
		align-items: flex-end;
		/* padding: 0 16px; */
		/* flex: 1; */
		flex-direction: row;
		/* align-items: center; */
		gap: 10rpx;
		position: relative;
		min-height: 40px;
		/* max-height: 120px; */
		/* overflow-y: scroll;
	  overflow-x: hidden; */
		/* margin: 8px 0px 15px; */
		padding: 0px 8px;
		/* position: relative; */
		background-color: white;
		transition: all 0.3s;
	}

	.set_panel_modal {
		position: fixed;
		width: 750rpx;
		height: 100vh;
		left: 0px;
		top: 0px;
		background-color: rgba(0, 0, 0, 0.7);
		z-index: 1000;
	}

	.set_panel {
		background-color: #f3f3f3;
		position: absolute;
		left: 0px;
		bottom: 0px;
		width: 750rpx;
	}

	.set_panel_funtion {
		display: flex;
		flex-direction: row;
		padding: 10px 16px;
		box-sizing: border-box;
		gap: 10px;
	}

	.set_panel_cancel {
		height: 60px;
		text-align: center;
		line-height: 40px;
		color: black;
		border-top: #cfcdcd solid 1px;
	}

	.function {
		width: 50px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		gap: 8px;
		background-color: #fff;
		color: black;
		padding: 0px 6px;
		border-radius: 16px;
		box-shadow: 0 0px 2px rgba(0, 0, 0, 0.253);
	}

	.icon {
		width: 48rpx;
		height: 48rpx;
	}

	.text_desc {
		font-weight: 300;
		font-size: 24rpx;
	}

	.input_inner_box {
		width: 100%;
		min-height: 50px;
		background-color: #fff;
		padding: 12px 12px;
		color: black;
		border: #f3f3f3 solid 1px;
		box-sizing: border-box;
		border-radius: 16px;
		box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);
	}

	.input {
		width: 100%;
		flex: 1;
		/* height: 40px; */
		max-height: 160px;
		/* overflow-y: hidden; */
		/* background-color: #fff;
	  padding: 6px 12px;
	  color: black;
	  border: #f3f3f3 solid 1px;
	  border-radius: 16px;
	  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2); */
		/* line-height: 22px; */
		font-size: 16px;
		/* display: flex;
	  align-items: center; */
		height: 20px;
		/* padding-top: 10px; */
		/* display: flex; */
		/* align-items: center; */
	}

	.right_btns {
		height: 50px;
		display: flex;
		align-items: center;
		/* flex: 1; */
		/* flex-shrink: 0; */
		gap: 10rpx;
		/* padding: 0 8px; */
		/* margin: 0 8px; */
	}

	.set {
		width: 58rpx;
		height: 58rpx;
	}

	.system {
		margin-left: 32rpx;
		margin-right: 32rpx;
		border-radius: 12rpx;
		padding-bottom: 16px;
		box-sizing: border-box;
		position: relative;
	}

	.avatar-left {
		position: absolute;
		top: 0px;
		left: 0px;
	}

	.guide_system {
		padding-left: 32rpx;
		padding-right: 32rpx;
		border-radius: 12rpx;
		padding-bottom: 16px;
		box-sizing: border-box;
	}

	.bot_intro_system {
		padding-left: 32rpx;
		padding-right: 32rpx;
		border-radius: 12rpx;
		padding-bottom: 16px;
		box-sizing: border-box;
	}

	.user {
		display: flex;
		justify-content: flex-end;
		margin-bottom: 16px;
	}

	.userContent .fileBar {
		/* display: flex;
	  overflow-x: scroll;
	  justify-content: flex-end; */
		/* width: 100%; */
		display: flex;
		flex-direction: row;
		flex-direction: row-reverse;
		flex-wrap: nowrap;
		padding: 0px 16px;
		overflow-x: scroll;
		max-height: 80px;
		gap: 10px;
	}

	.user .user_content {
		background-color: #f3f5fb;
		border-radius: 12rpx 0rpx 12rpx 12rpx;
		margin-left: 32rpx;
		margin-right: 32rpx;
		padding: 24rpx;
		word-wrap: break-word;
		word-break: break-all;
		font-size: 32rpx;
	}

	.feedback_modal {
		position: fixed;
		top: 0px;
		left: 0px;
		width: 750rpx;
		height: 100vh;
		background-color: rgba(0, 0, 0, 0.7);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1000;
	}

	.modal {
		background-color: #fff;
		width: 700rpx;
		border-radius: 16rpx;
		overflow: hidden;
	}

	.modal_head {
		height: 40px;
		line-height: 40px;
		padding: 0px 10px;
	}

	.modal_body {
		padding: 10px;
	}

	.modal_footer {
		display: flex;
	}

	.link-box {
		padding: 0px 16px 6px 16px;
	}

	.tool_box {
		height: 80px;
		display: flex;
		flex-direction: row;
		padding: 5px 16px;
		box-sizing: border-box;
		gap: 10px;
		justify-content: flex-start;
		flex-wrap: nowrap;
		overflow-x: scroll;
	}

	.tool_box .function {
		flex: 0 0 calc(25% - 20px);
	}

	.webSearchSwitch {
		width: 200rpx;
		height: 30px;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 25px;
		border: 1px solid rgba(0, 0, 0, 0.08);
		font-size: 14px;
	}

	.feature_enable {
		background-color: rgb(219, 234, 254);
		color: rgb(77, 107, 254);
		border-color: rgba(0, 122, 255, 0.15);
	}

	.feature_list {
		margin: 0 16px;
	}

	.closeicon {
		position: absolute;
		right: -25rpx;
		top: 5rpx;

		image {
			width: 64rpx;
			height: 64rpx;
		}
	}

	.picbox {
		background-repeat: no-repeat;
		background-size: contain;
		width: 600rpx;
	}

	.myboxs {
		margin: 100rpx 30rpx 20rpx 30rpx;
		height: 400rpx;
		flex-direction: column;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;

		.cont {
			margin-bottom: 40rpx;

			text {
				font-weight: 700;
				letter-spacing: 2rpx;
				font-size: 32rpx;
				color: #AA7248;
				line-height: 54rpx;

				.bold {
					color: #e6702b;
					margin: 0 4rpx;
				}
			}
		}

		.btn {
			display: flex;
			flex-direction: row;

			button {
				margin: 0 10rpx;
				color: #fff;
				padding: 5rpx 50rpx;
				border-radius: 70rpx;
				font-size: 32rpx;
				background-image: linear-gradient(90deg, #FF8C38 0, #FD5819 100%);

				&::after {
					border: none;
				}
			}
		}
	}

	.popbox {
		display: flex;
		position: relative;
		flex-direction: column;

		.popcont {
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
		}

		.cont {
			font-size: 28rpx;
			padding: 40rpx 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.btn {
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;

			button {
				width: 170rpx;
				height: 60rpx;
				font-size: 28rpx;
				line-height: 60rpx;
				padding: 0;
				margin: 0;
				background: #fb6b3d;
				color: #fff;

				&::after {
					border: none
				}
			}
		}
	}

	/* 添加加载动画样式 */
	.loading-dots {
		display: inline-flex;
		gap: 4rpx;
	}

	.loading-dots text {
		animation: loading 1s infinite;
	}

	.loading-dots text:nth-child(2) {
		animation-delay: 0.2s;
	}

	.loading-dots text:nth-child(3) {
		animation-delay: 0.4s;
	}

	@keyframes loading {

		0%,
		100% {
			opacity: 0.2;
		}

		50% {
			opacity: 1;
		}
	}

	.message.loading .message-content {
		display: flex;
		align-items: center;
		gap: 8px;
	}

	.loading-dots {
		display: flex;
		gap: 4px;
	}

	.loading-dots span {
		width: 8px;
		height: 8px;
		background-color: #999;
		border-radius: 50%;
		animation: loading 1s infinite ease-in-out;
	}

	@keyframes loading {

		0%,
		100% {
			transform: scale(0.3);
		}

		50% {
			transform: scale(1);
		}
	}

	.chat-container {
		height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #fff;
	}

	.messages-container {
		/* flex: 1; */
		box-sizing: border-box;
		overflow-y: auto;
		height: calc(100vh - 160rpx);
		padding: 20px;
	}

	.message {
		display: flex;
		margin-bottom: 20px;
		gap: 12px;
	}

	.avatar {
		width: 40px;
		height: 40px;
		border-radius: 50%;
		background-color: #e0e0e0;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 20px;
	}

	.message-content {
		background-color: white;
		padding: 12px 16px;
		border-radius: 12px;
		max-width: 70%;
		box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
	}

	.user {
		flex-direction: row-reverse;
	}

	.user .message-content {
		background-color: #1a8cff;
		margin-right: 20rpx;
		color: white;
		position: relative;

		&::before {
			position: absolute;
			right: -18rpx;
			content: "";
			top: 50%;
			width: 20rpx;
			height: 30rpx;
			margin-top: -12rpx;
			background: url('https://pic.kefeichangduo.top/qdkbm/newimage/fhui/member-icon-right4.png') no-repeat;
			background-size: 80% auto;
		}
	}

	.input-container {
		padding: 20px;
		background-color: white;
		border-top: 1px solid #e0e0e0;
		display: flex;
		position: relative;
		align-items: center;
		gap: 20;

		.icon {
			width: 50rpx;

			image {
				width: 40rpx;
				height: 40rpx;
			}
		}

		.inputwrap {
			flex: 1;
			display: flex;
			gap: 10px;
			flex-direction: row;
		}
	}

	.message-input {
		flex: 1;
		padding: 12px;
		/* border: 1px solid #e0e0e0; */
		/* border-radius: 8px; */
		resize: none;
		font-family: inherit;
		font-size: 14px;
	}

	.send-button {
		padding: 0 10rpx;
		background: transparent;
		color: #fff;
		display: flex;
		align-items: center;
		border: none;
		border-radius: 8px;
		cursor: pointer;
		font-size: 28rpx;

		&::after {
			border: none
		}
	}

	.send-button:hover {
		// background-color: #FF8B1B;
	}

	.chat-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #fff;
	}

	.messages-container {
		/* flex: 1; */
		padding: 20rpx;
		/* 为底部输入框留出空间 */
		/* padding-bottom: 320rpx; */
	}

	.message {
		display: flex;
		margin-bottom: 20rpx;
		gap: 24rpx;
	}

	.avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		background-color: #e0e0e0;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 40rpx;
	}

	.message-content {
		background-color: #f5f5f5;
		padding: 24rpx 32rpx;
		border-radius: 24rpx;
		max-width: 70%;
	}

	.user {
		flex-direction: row-reverse;
	}

	.user .message-content {
		background-color: #FF8B1B;
		color: white;
	}

	.input-wrapper {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.input-container {
		padding: 20rpx;
		display: flex;
		gap: 20rpx;
		background-color: white;
	}

	.message-input {
		flex: 1;
		padding: 20rpx 35rpx;
		background: #f0f0f0;
		/* border: 2rpx solid #e0e0e0; */
		border-radius: 25rpx;
		line-height: 40rpx;
		font-size: 28rpx;
		min-height: 40rpx;
		max-height: 200rpx;
	}

	// .send-button {
	// 	padding: 0 40rpx;
	// 	height: 80rpx;
	// 	line-height: 80rpx;
	// 	background-color: #FF8B1B;
	// 	color: white;
	// 	border: none;
	// 	border-radius: 16rpx;
	// 	font-size: 28rpx;
	// }

	// .send-button:active {
	// 	background-color: #FF8B1B;
	// }

	/* 适配底部安全区域 */
	.safe-area-inset-bottom {
		padding-bottom: constant(safe-area-inset-bottom);
		/* iOS 11.2+ */
		padding-bottom: env(safe-area-inset-bottom);
		/* iOS 11.2+ */
		background-color: #fff;
	}
</style>