<template>
	<view>
		<view class="head" style="background: #fff;" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="left" @tap="back">
					<view class="itemList">
						<view class="item">
							<button>
								<image style="width:20rpx"
									src="https://pic.kefeichangduo.top/qdkbm/newimage/fhui/back.png">
								</image>
							</button>
						</view>
					</view>
				</view>
				<view class="text" style="color: #333;">查专业</view>
			</view>
			<view class="search-input">
				<view class="icon">
					<image :src="imgUrl+'/qdkbm/newimage/fhui/icon-search4.png'"></image>
				</view>
				<view class="inputwrap">
					<input type="text"  max-length="20" v-model="keyword"  class="inputstyle"
						placeholder="请输入"   @blur="bindBlur"  @confirm="bindConfirm" @focus="bindFocus" @input="handleInput" confirmType="search" :focus="focus"   placeholder-class="placeholder-class" />
					<button @tap="search">搜索</button>
				</view>
			</view>

		</view>
		<view class="search-result" v-if="keyword">
			<view class="tit"><text>{{keyword}}</text>的搜索结果</view>
			<block v-if="filteredSearchList.length>0">
				<view class="searchlist">
					<view class="item" @tap="searchDetail(item)" v-for="(item,index) in filteredSearchList" :key="index">
						<view class="major-name">{{item.name}}</view>
						<view class="major-code" v-if="item.code">{{item.code}}</view>
					</view>
				</view>
			</block>
			<block v-if="filteredSearchList.length==0">
				<view class="none">暂无相关结果</view>
			</block>
		</view>
	</view>
</template>
<script>
	export default {
		components: {},
		name: 'major',
		data() {
			return {
				searchList: [],
				onFocus:true,
				focus:true,
				imgUrl: this.$base.uploadImgUrl,
				yxzy_ids: [],
				keyword: "",
				items: [],
				height: 0,
				titleTop: 0,
				inputTimer: null // 用于输入防抖
			}
		},
		watch: {
		},
		onLoad() {
			this.search()
		},
		computed: {
			filteredSearchList() {
				let filtered = this.searchList;
				if (this.keyword) {
					filtered = filtered.filter(item =>
						item.name.toLowerCase().includes(this.keyword.toLowerCase())
					);
				}
				return filtered;
			}
		},
		onShow() {},
		mounted() {},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top
			const query = uni.createSelectorQuery().in(this)
			query.selectAll('.head').boundingClientRect(data => {
				console.log(data)
				this.height = data[0].height + this.titleTop - 4
				console.log(this.height)
			}).exec()
		},
		methods: {
			bindConfirm(){
				this.search()
			},
			bindBlur(){
				this.search()
			},
			bindFocus(){
				this.onFocus = true
				this.focus = true
			},
			// 处理输入事件，实现实时搜索
			handleInput() {
				// 使用防抖技术，避免频繁请求
				if (this.inputTimer) {
					clearTimeout(this.inputTimer);
				}

				// 设置300毫秒的防抖延迟
				this.inputTimer = setTimeout(() => {
					this.search();
				}, 300);
			},
			searchDetail(item){
				// 如果有id，优先传递majorId，否则传递majorName
				let url = '/pages/plan/majorDetail?majorName=' + item.name;
				if (item.id) {
					url += '&majorId=' + item.id;
				}
				uni.navigateTo({
					url: url
				})
			},
			// 只查找最底层的专业（有数字ID和专业代码的项）
			findBottomLevelCategories(data, result = []) {
				data.forEach(item => {
					// 检查是否为最底层专业（通常有数字ID和专业代码）
					if (typeof item.id === 'number' && item.code) {
						result.push({
							id: item.id,
							name: item.name,
							code: item.code
						});
					} else if (item.children && item.children.length > 0) {
						// 如果不是最底层专业且有子项，则继续递归查找
						this.findBottomLevelCategories(item.children, result);
					}
				});
				return result;
			},
			search() {
				this.$apis.getmajordata({
					majorName: this.keyword
				}).then((res) => {
					if (res.code == 0) {
						let arr = [];
						// 处理新的API响应格式
						if (res.data && res.data.tree && res.data.tree.categories) {
							// 从所有类别中提取专业数据
							arr = this.findBottomLevelCategories(res.data.tree.categories);
						}
						this.searchList = arr;
						console.log('搜索结果:', arr.length);
					} else {
						this.searchList = [];
						console.error('搜索失败:', res.msg);
					}
				}).catch(err => {
					console.error('搜索出错:', err);
					this.searchList = [];
				});
			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			}
		}
	}
</script>
<style>
	page {
		background: #f5f5f5
	}
	.placeholder-class{color:#999}
</style>
<style scoped lang="scss">
	.none {
		padding: 150rpx 0;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #999;
	}

	.search-result {
		padding: 0 30rpx;
		.tit {
			padding: 25rpx 0;
			color: #666;
			word-break: break-all;
			font-size: 28rpx;
			text {
				font-weight: 700;
				margin-right:8rpx;
			}
		}
		.searchlist {
			display: flex;
			flex-direction: column;

			.item {
				margin-bottom: 10rpx;
				background: #fff;
				border-radius: 12rpx;
				padding: 35rpx 20rpx;
				font-size: 28rpx;
				color: #000;
				display: flex;
				justify-content: space-between;
				align-items: center;

				.major-name {
					font-weight: 500;
					flex: 1;
				}

				.major-code {
					color: #FF8510;
					font-size: 24rpx;
					padding: 6rpx 12rpx;
					background-color: rgba(255, 133, 16, 0.1);
					border-radius: 6rpx;
				}
			}
		}
	}

	.search-input {
		margin: 20rpx 30rpx;
		align-items: center;
		height: 72rpx;
		background: #f1f1f1;
		border-radius: 48rpx;
		display: flex;
		flex-direction: row;

		.icon {
			width: 100rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			image {
				width: 40rpx;
				height: 40rpx;
			}
		}

		.inputwrap {
			flex: 1;
			position: relative;

			.inputstyle {
				width: 100%;
				height: 72rpx;
				line-height: 72rpx;
				font-size: 28rpx;
				color: #333;
			}

			button {
				z-index: 99;
				position: absolute;
				right: 0;
				top: 0;
				height: 72rpx;
				line-height: 72rpx;
				color: #fff;
				font-size: 32rpx;
				border-radius: 48rpx;
				background: #FF8918;
				padding: 0 50rpx;
				margin: 0;
				min-width: inherit;

				&::after {
					border: none
				}
			}
		}
	}

	.counter-warp {}

	.label {
		margin: 10px;
	}

	button {
		&::after {
			border: none
		}

	}
</style>