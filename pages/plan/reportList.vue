<template>
	<view style="padding-bottom: 50rpx;">
		<view class="bgs">
			<image :src="imgUrl+'/qdkbm/newimage/fhpic/fh-bg11.png'" mode="widthFix"
				style="position: absolute;left:0;top:0;width:100%"></image>
		</view>

		<view class="head"  :style="{'padding-top': (titleTop-4) + 'px','background':scrollTop>titleTop?'#FF8B1B':'none'}">
			<view class="header1">
				<view class="left" @tap="back">
					<view class="itemList">
						<view class="item">
							<button>
								<image style="width:20rpx" :src="imgUrl+'/qdkbm/newimage/fhui/back-light.png'">
								</image>
							</button>
						</view>
					</view>
				</view>
				<view class="text">专业版报告列表</view>
			</view>
		</view>

		<!-- 筛选条件 -->
		<view class="filter-bar">
			<button :class="['filter-btn', { active: type === '' }]" @click="setFilter('')">
				全部
			</button>
			<button :class="['filter-btn', { active: type === '1' }]" @click="setFilter('1')">
				主报告
			</button>
			<button :class="['filter-btn', { active: type === '2' }]" @click="setFilter('2')">
				学校报告
			</button>
			<!-- <button :class="['filter-btn', { active: type === '3' }]" @click="setFilter('3')">
				专业报告
			</button> -->
			<button :class="['filter-btn', { active: type === '5' }]" @click="setFilter('5')">
				专业特色报告
			</button>
			<button :class="['filter-btn', { active: type === '4' }]" @click="setFilter('4')">
				专业就业分析报告
			</button>
		</view>

		<view class="loading-mask" v-if="loading">
			<view class="loading-spinner"></view>
		</view>

		<!-- 报告列表 -->
		<view class="report-list">
			<view class="report-count">共{{filteredReports.length}}份</view>
			<view class="none" v-if="filteredReports.length==0">
				<view class="icon">
					<image :src="imgUrl+'/qdkbm/newimage/nodata-express.png'"></image>
				</view>
				<view class="text">暂无数据</view>
			</view>
			<view class="report-item" @tap="goReportDetail(item,index)" v-for="(item, index) in filteredReports"
				:key="index">
				<!-- 将标签放在上方 -->
				<view class="report-tags">
					<view class="tag">{{LabelFn(item.type)}}
						<view class="rightbtns">
							<image :src="imgUrl+'/qdkbm/newimage/fhpic/fh-titsrightbtn.png'"></image>
						</view>
					</view>
				</view>
				<!-- 所有类型的报告都在标签下方显示学校名称 -->
				<view class="school-line">
					<text class="school-title">{{item.school}}</text>
				</view>
				<view class="report-info">
					<text class="report-time">生成时间：{{item.time}}</text>
				</view>
			</view>

		</view>
	</view>
</template>

<script>
	import dayjs from 'dayjs'
	export default {
		components: {},
		// 页面配置
		config: {
			enablePullDownRefresh: true,
			backgroundTextStyle: 'dark'
		},
		data() {
			return {
				loading: false,
				scrollTop:0,
				imgUrl: this.$base.uploadImgUrl,
				ver: "",
				type: "",
				reportId: "",
				titleTop: 0,
				types: [{
					id: 1,
					name: "主报告"
				}, {
					id: 2,
					name: "学校简介"
				}, {
					id: 3,
					name: "专业报告"
				}, {
					id: 5,
					name: "专业特色报告"
				}, {
					id: 4,
					name: "专业就业分析报告"
				}],
				filter: 'all', // 当前筛选条件
				reports: []
			}
		},
		onPageScroll(e){
			// console.log(e)
			this.scrollTop = e.scrollTop
		},
		onPullDownRefresh() {
			this.getList();
			uni.stopPullDownRefresh();
		},
		computed: {
			filteredReports() {
				let filtered = this.reports;
				if (this.type !== '') {
					filtered = filtered.filter(item => item.type === this.type);
				}
				this.refresh()
				return filtered
			}
		},
		onLoad(options) {
			if (options.reportId) {
				this.reportId = options.reportId
			}
			this.getList()
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top
		},
		methods: {
			refresh() {
				setTimeout(() => {
					this.loading = false
				}, 600)
			},
			getList() {
				this.$apis.getmajors({
					type: this.type,
					answerRecordId: this.reportId,
					userId: uni.getStorageSync('userId')
				}).then((res) => {
					if (res.code == 0) {
						res.data.map((item) => {
							item.type = item.analysisType
							item.time = dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss')
							if (item.type != 1) {
								if (item.question.includes('>')) {
									item.question = item.question.replaceAll('>', '  ·  ')
								}
								item.school = item.question
							} else {
								// 为主报告添加名称，使用返回值中的name
								item.school = item.name || "升学规划报告"
							}
						})
						this.reports = res.data
					}
				})
			},
			LabelFn(val) {
				let str = ''
				this.types.forEach((item) => {
					if (item.id == val) {
						str = item.name
					}
				})
				return str
			},
			goReportDetail(item, index) {
				uni.navigateTo({
					url: '/pages/plan/report?id=' + item.id + '&rid=' + Number(index + 1) + '&title=' + encodeURIComponent(item.school)
				})
			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			},
			setFilter(filter) {
				this.loading = true
				this.reports = []
				this.getList();
				this.type = filter;
			}
		}
	}
</script>
<style>
	page {
		background: #fef1db
	}
</style>
<style scoped lang="scss">
	.none {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background: #fff;
		padding: 220rpx 0;
		border-radius: 16rpx;

		.icon {
			image {
				width: 320rpx;
				height: 320rpx;
			}
		}

		.text {
			margin-top: 30rpx;
			color: #999;
			font-size: 28rpx;
		}
	}

	@-webkit-keyframes countloading {
		0% {
			transform: rotate(180deg)
		}

		100% {
			transform: rotate(0deg)
		}
	}

	@keyframes countloading {
		0% {
			transform: rotate(180deg)
		}

		100% {
			transform: rotate(0deg)
		}
	}


	// .progress-active-loading{animation:progress-active 2.4s cubic-bezier(.23,1,.32,1) infinite;background-color:#fff;height:100%;position:absolute;width:100%;z-index:999}
	@-webkit-keyframes progress-active {
		0% {
			opacity: .1;
			transform: translateX(-100%) scaleX(0)
		}

		20% {
			opacity: .5;
			transform: translateX(-100%) scaleX(0)
		}

		100% {
			opacity: 0;
			transform: translateX(0) scaleX(1)
		}
	}

	@keyframes progress-active {
		0% {
			opacity: .1;
			transform: translateX(-100%) scaleX(0)
		}

		20% {
			opacity: .5;
			transform: translateX(-100%) scaleX(0)
		}

		100% {
			opacity: 0;
			transform: translateX(0) scaleX(1)
		}
	}


	.loading-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;

		.loading-spinner {
			width: 50rpx;
			height: 50rpx;
			animation: countloading 1s ease infinite;
			// animation:progress-active 2.4s cubic-bezier(.23,1,.32,1) infinite;
			background-image: url('https://pic.kefeichangduo.top/qdkbm/newimage/fhui/waiting.png');
			background-position: 50%;
			background-repeat: no-repeat;
			background-size: contain;
		}
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(360deg);
		}
	}

	.filter-bar {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		padding: 30rpx 20rpx;
		padding-bottom: 10rpx;
		margin: 0 10rpx;
	}

	.filter-btn {
		font-size: 24rpx;
		color: #333;
		margin: 0;
		padding: 10rpx 20rpx;
		line-height: inherit;
		border-radius: 8rpx;
		margin-right: 20rpx;
		margin-bottom: 20rpx;
		background: #fff;

		&::after {
			border: none;
		}

		&.active {
			background: #FFF0E5;
			color: #FB6B3D;
		}
	}

	// 移除 info1 类，不再需要左右布局

	.report-list {
		padding: 24rpx;
		background: #fff;
		border-radius: 24rpx;
		margin: 0 30rpx;
		position: relative;

		.report-item {
			background: #FFF0E5;
			border-radius: 24rpx;
			padding: 30rpx 24rpx;
			margin-bottom: 20rpx;

			&:last-child {
				margin-bottom: 0;
			}
		}

		.report-count {
			font-size: 28rpx;
			color: #666;
			margin-bottom: 24rpx;
		}
	}

	.report-info {
		display: flex;
		align-items: center;
		margin-top: 10rpx;
	}

	.report-tags {
		display: flex;
		align-items: center;
		margin-bottom: 15rpx;
	}

	.tag {
		display: inline-block;
		padding: 8rpx 20rpx;
		padding-right: 10rpx;
		background-image: linear-gradient(90deg, #FECD97 0, #FB6E3F 100%);
		color: #fff;
		border-radius: 12rpx 0 0 12rpx;
		font-size: 24rpx;
		position: relative;
		margin-right: 40rpx;
		max-width: 220rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;

		.rightbtns {
			position: absolute;
			right: -20rpx;
			top: 0;

			image {
				width: 20rpx;
				height: 56rpx;
			}
		}
	}

	.school-name {
		font-size: 28rpx;
		color: #333;
		font-weight: bold;
		line-height: 1.4;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
		word-break: break-all;
	}

	.report-time {
		font-size: 24rpx;
		color: #999;
		margin-top: 10rpx;
		display: block;
	}

	.school-line {
		margin-top: 5rpx;
	}

	.school-title {
		font-size: 28rpx;
		color: #333;
		font-weight: bold;
		line-height: 1.4;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
		word-break: break-all;
		padding: 0 5rpx;
	}
</style>