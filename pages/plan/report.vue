<template>
	<view class="content">
		<view class="head" :style="'padding-top:' + titleTop + 'px'">
			<view class="header">
				<view class="back-btn" @tap="back">
					<button style="width: auto; color: #fff; background: none; margin: 0; padding: 0; line-height: normal;">
						<image v-if="!source" :src="imgUrl+'/qdkbm/newimage/fhui/back-light.png'" style="width: 20rpx;" mode="widthFix"></image>
						<image v-if="source" :src="imgUrl+'/qdkbm/newimage/fhui/logos4.png'" style="width: 64rpx; margin-right: 20rpx;" mode="widthFix"></image>
						<text v-if="source" style="font-size: 32rpx;">飞鸿AI志愿规划</text>
					</button>
				</view>
				<view class="title">{{ reportTitle || '报告详情' }}</view>
			</view>
		</view>
		<view class="report-container">
			<view class="report-content">
				<mpHtml :markdown="true" :content="myhtml" :tagStyle="mpHtmlStyle"></mpHtml>
			</view>
		</view>
		<view class="footer">
			<view class="branding" @tap="goHome">
				<image :src="imgUrl+'/qdkbm/newimage/fhui/logos4.png'" mode="widthFix"></image>
				<text>飞鸿AI志愿规划</text>
			</view>
			<view class="actions">
				<button open-type="share" class="share-btn">
					<image :src="imgUrl+'/qdkbm/newimage/fhpic/fh-shareicon.png'" mode="widthFix"></image>
					<text>分享</text>
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	// import tabBar from '@/components/tabBar.vue'
	import mpHtml from '@/uni_modules/mp-html/components/mp-html/mp-html.vue'
	export default {
		components: {
			mpHtml,
			// tabBar
		},
		data() {
			return {
				imgUrl: this.$base.uploadImgUrl,
				rid: "",
				source: "",
				titleTop: 0,
				reportId: "",
				reportTitle: "",
				myhtml: "",
				mpHtmlStyle: {
					h3: 'font-size: 36rpx; font-weight: 700; color: #FF8510; margin: 30rpx 0 20rpx; padding: 16rpx 20rpx; border-left: 8rpx solid #FF8510; background-color: rgba(255, 133, 16, 0.05); border-radius: 0 8rpx 8rpx 0;',
					p: 'font-size: 30rpx; line-height: 1.8; color: #333; margin-bottom: 20rpx; text-align: justify;',
					ol: 'padding-left: 40rpx; margin: 20rpx 0;',
					ul: 'padding-left: 40rpx; margin: 20rpx 0;',
					li: 'position: relative; margin-bottom: 16rpx; padding-left: 10rpx; font-size: 30rpx; line-height: 1.6; color: #333;',
					strong: 'color: #FF5B03; font-weight: 700;',
					a: 'color: #209BFF; text-decoration: underline;',
					hr: 'border: none; height: 2rpx; background: linear-gradient(to right, transparent, rgba(255, 133, 16, 0.5), transparent); margin: 30rpx 0;',
					img: 'max-width: 100%; height: auto; margin: 10rpx auto; display: block;'
				},
				markdownText: "根据考生分数（277分/物理类）、家庭背景及就业意向，结合江西省专科批次录取数据及行业就业特点，推荐以下院校及专业：\n\n  ### 冲刺档（录取概率40%）\n 1. **湖南高速铁路职业技术学院（铁道工程技术）** \n  		 - **录取数据**：2024年物理类最低分265分（全省25.1万名），计划招生80人  \n 						- **专业实力**：拥有国家级轨道交通实训基地，与广铁集团共建订单班  \n 						- **课程设置**：铁路轨道施工与维护、工程测量技术、BIM建模（需掌握CAD软件）\n - **就业方向**：铁路局工务段技术员，入职3年后平均薪资6500元/月，5年可竞聘工长岗位 \n2. **江西财经职业学院（金融科技应用）** \n - **录取数据**：2024年物理类最低分270分，计划招生45人 \n  - **专业特色**：银行模拟实训室+金融大数据分析实验室，与九江银行合作定向培养 \n - **课程重点**：银行柜面实务、金融数据分析（需基础数学能力） \n  - **就业优势**：家庭银行人脉可辅助进入县域支行，5年薪资可达当地公务员1.5倍 \n 3. **重庆传媒职业学院（游戏美术设计）** \n - **录取数据**：2023年最低分272分，计划招生30人 \n - **实训配置**：动作捕捉实验室+虚幻引擎实训室（需美术基础） \n - **就业方向**：手游公司原画师（起薪4000），3年经验者平均薪资8000元（数据来源：BOSS直聘2024游戏行业报告） \n ### 稳妥档（录取概率70%）\n 1. **华东交通大学理工学院（专科·铁道信号自动控制） ** \n - **录取数据**：2024年专科批最低分255分，计划招生120人 \n - **核心优势**：共享交大铁路实训资源，南昌铁路局校招指定院校 \n - **课程难点**：铁路信号系统维护（需物理电路知识）\n - **就业保障**：90%毕业生进入铁路系统，5年工龄薪资超地方事业单位30% \n 2. **江西电力职业技术学院（供用电技术）** \n - **录取数据**：2023年最低分260分，计划招生200人 \n - **校企合作**：国家电网三产单位定向培养，需考取电工证 \n - **就业方向**：县级供电公司运维岗，3年综合收入可达8-10万/年 \n 3. **南昌理工学院（专科·大数据与会计）** \n - **录取数据**：2024年最低分250分，计划招生150人 \n - **培养特色**：银行柜员业务模拟系统+财税机器人实训 \n - **就业通道**：农商行校招通过率超60%，5年可晋升支行主管 ### 保底档（录取概率95%）\n 1. **武汉铁路桥梁职业学院（专科·建筑工程技术）**\n - **录取数据**：2023年最低分240分，计划招生300人 \n - **行业优势**：中铁系统'现代学徒制'试点，实训含桥梁BIM技术 \n - **就业保障**：中铁各工程局年招超500人，野外作业岗首年包吃住收入5万 \n 2. **江西司法警官职业学院（司法信息安全）** \n - **录取数据**：2024年最低分235分，计划招生80人 \n - **特殊优势**：公务员考试通过率超30%，狱警岗位专业对口 \n - **薪资发展**：入职即享政法系统待遇，5年工龄公积金超3000元/月 \n 3. **九江职业技术学院（船舶电子电气技术）** \n - **录取数据**：2023年最低分230分，计划招生100人 \n - **行业前景**：长江航运集团定向培养，5G智能船舶方向紧缺人才 \n - **薪资待遇**：国企船厂技术员首年6万，持适任证书后薪资翻倍 \n  ### 特别建议  \n 1. **铁路方向优先选择**：湖南高速铁路职院/武汉铁路桥梁职院的订单班，注意查看《铁路行业体检标准》（色盲弱受限） \n 2. **银行方向备选方案**：江西财经职业学院'智能柜员'方向+自考本科（推荐江西财经大学继续教育学院） \n 3. **慎选游戏专业**：277分院校缺乏UE5引擎等先进设备，建议通过'Unity3D工程师认证'提升竞争力 注：以上数据综合江西省教育考试院2024年投档线、各校就业质量报告及智联招聘行业数据，薪资数据已扣除江西地区生活成本差异因素。",
			}
		},
		onShareAppMessage(res) {
			return {
				title: '好友邀您查看志愿规划报告',
				path: `/pages/plan/report?id=${this.reportId}&rid=${this.rid}&source=share&title=${encodeURIComponent(this.reportTitle)}`,
				imageUrl: 'https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/fh-sharepic.png'
			}
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top
		},
		onLoad(options) {
			if (options.rid) {
				this.rid = options.rid
			}
			if (options.id) {
				this.reportId = options.id
				this.getReport()
			}
			if (options.source) {
				this.source = options.source
			}
			if (options.title) {
				this.reportTitle = decodeURIComponent(options.title)
			}
		},
		methods: {
			goHome(){
				uni.switchTab({
					url:'/pages/index/index'
				})
			},
			getReport() {
				this.$apis.getReports({
					id: this.reportId
				}).then((res) => {
					if (res.code == 0) {
						if (res.data.content) {
							this.myhtml = res.data.content
						}
						if (res.data.title && !this.reportTitle) {
							this.reportTitle = res.data.title
						}
					}
				})
			},
			back() {
				if (this.source) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack({
						delta: 1
					})
				}
			}
		}
	}
</script>
<style>
	page {
		background: #fff;
	}
</style>
<style lang="scss" scoped>
	.content {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
	}
	
	.head {
		background: linear-gradient(135deg, #FF8510, #FF5B03);
		padding-bottom: 20rpx;
		
		.header {
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;
			padding: 10rpx 30rpx;
			
			.back-btn {
				position: absolute;
				left: 30rpx;
				z-index: 10;
				
				&::after {
					content: '';
					position: absolute;
					top: -20rpx;
					left: -20rpx;
					right: -20rpx;
					bottom: -20rpx;
					z-index: -1;
				}
				
				button::after {
					border: none;
				}
			}
			
			.title {
				font-size: 32rpx;
				font-weight: bold;
				color: #fff;
				max-width: 70%;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				line-clamp: 2;
				-webkit-box-orient: vertical;
				overflow: hidden;
				text-overflow: ellipsis;
				padding: 0 40rpx;
				text-align: center;
				line-height: 1.3;
			}
		}
	}
	
	.report-container {
		flex: 1;
		background: #fff;
		position: relative;
		margin-top: -20rpx;
		border-radius: 24rpx 24rpx 0 0;
		padding: 10rpx 0;
		padding-bottom: 140rpx; /* 为底部导航栏留出空间 */
		
		.report-content {
			padding: 40rpx 30rpx;
			background: #fff;
			border-radius: 24rpx 24rpx 0 0;
			box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.05);
		}
	}
	
	.footer {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 40rpx;
		background: #fff;
		border-top: 1px solid #f0f0f0;
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		box-sizing: border-box;
		padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
		z-index: 100;
		
		.branding {
			display: flex;
			align-items: center;
			
			image {
				width: 48rpx;
				margin-right: 10rpx;
			}
			
			text {
				font-size: 28rpx;
				color: #666;
			}
		}
		
		.actions {
			.share-btn {
				display: flex;
				align-items: center;
				background: #FF8510;
				border-radius: 30rpx;
				padding: 10rpx 20rpx;
				
				&::after {
					border: none;
				}
				
				image {
					width: 32rpx;
					margin-right: 8rpx;
				}
				
				text {
					font-size: 26rpx;
					color: #fff;
				}
			}
		}
	}
</style>