<template>
	<view class="trend-detail-container">
		<!-- 固定头部 -->
		<view class="head" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="left" @tap="back">
					<view class="itemList">
						<view class="item">
							<button>
								<image style="width:20rpx;" :src="imgUrl+'/qdkbm/newimage/fhui/back.png'"></image>
							</button>
						</view>
					</view>
				</view>
				<view class="text" style="color: #FF9933;">报告详情</view>
			</view>
		</view>
		
		<!-- 内容区域 -->
		<view class="content" :style="'margin-top:' + (titleTop + 44) + 'px'">
			<view class="contents">
				<view class="none" v-if="!myhtml">
					暂无数据
				</view>
				<view class="industry-content" v-if="myhtml">
					<mpHtml :content="myhtml" :tagStyle="mpHtmlStyle"></mpHtml>
				</view>
			</view>
		</view>
		<loading-popup :show="showpopbox" :imgUrl="imgUrl" @close="closepopbox" @back="backWithPopupClose"></loading-popup>
	</view>
</template>
<script>
	import mpHtml from '@/uni_modules/mp-html/components/mp-html/mp-html.vue'
	import LoadingPopup from '@/components/loading-popup/loading-popup.vue'
	export default {
		components: {
			mpHtml,
			LoadingPopup
		},
		data() {
			return {
				showpopbox: false,
				imgUrl: this.$base.uploadImgUrl,
				titleTop: 0,
				source: "",
				titleTop2: 0,
				industryName: '',
				content: '',
				industryId: '',
				myhtml: "",
				showBackPopup: false,
				showProvincePopup: false,
				selectedProvince: {},
				dsCus: "",
				version: "",
				mpHtmlStyle: {
					h3: 'font-size: 36rpx; font-weight: 700; color: #FF8510; margin: 30rpx 0 20rpx; padding: 16rpx 20rpx; border-left: 8rpx solid #FF8510; background-color: rgba(255, 133, 16, 0.05); border-radius: 0 8rpx 8rpx 0;',
					p: 'font-size: 30rpx; line-height: 1.8; color: #333; margin-bottom: 20rpx; text-align: justify;',
					ol: 'padding-left: 40rpx; margin: 20rpx 0;',
					ul: 'padding-left: 40rpx; margin: 20rpx 0;',
					li: 'position: relative; margin-bottom: 16rpx; padding-left: 10rpx; font-size: 30rpx; line-height: 1.6; color: #333;',
					strong: 'color: #FF5B03; font-weight: 700;',
					a: 'color: #209BFF; text-decoration: underline;',
					hr: 'border: none; height: 2rpx; background: linear-gradient(to right, transparent, rgba(255, 133, 16, 0.5), transparent); margin: 30rpx 0;',
					img: 'max-width: 100%; height: auto; margin: 10rpx auto; display: block;'
				}
			}
		},
		onLoad(options) {
			if (options.id) {
				this.industryId = options.id
				
			}
		},
		onShow(){
			if(this.industryId){
				this.getReport()
			}
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top;
			uni.createSelectorQuery().select('.head').boundingClientRect((res2) => {
				if (res2) {
					this.titleTop2 = res2.height + this.titleTop
				}
			}).exec()
		},
		methods: {
			getReport() {
				// uni.showLoading({
				// 	title:"玩命加载中..."
				// })
				this.showpopbox = true
				this.$apis.indexquestionContent({
					questionId: this.industryId,
					version: this.version,
					dsCus: this.dsCus
				}).then((res) => {
					if (res.code == 0) {
						this.showpopbox = false
						// uni.hideLoading()
						if (res.data.list) {
							this.myhtml = res.data.list[0]?.content
						} else {
							this.myhtml = ''
						}
					}
				}).catch((err) => {
					this.showpopbox = false
					// uni.hideLoading()
				})
			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			},
			closeBackPopup() {
				this.showBackPopup = false;
			},
			confirmBack() {
				this.closeBackPopup();
				uni.navigateBack({
					delta: 1
				});
			},
			openQA() {
				uni.navigateTo({
					url: '/pages_chat/chat/chat'
				});
			},
			downloadContent() {
				uni.showLoading({
					title: '复制中...'
				});

				// TODO: 实现下载逻辑


				setTimeout(() => {
					uni.hideLoading();

					uni.setClipboardData({
						data: this.myhtml,
						success: function(res) {
							uni.showToast({
								title: "复制成功",
								icon: "success",
							});
						},
					});

					// uni.showToast({
					// 	title: '下载成功',
					// 	icon: 'success'
					// });
				}, 1500);
			},
			// 关闭弹窗
			closepopbox() {
				this.showpopbox = false;
			},
			// 关闭弹窗并返回上一页
			backWithPopupClose() {
				this.showpopbox = false;
				uni.navigateBack({
					delta: 1
				});
			}
		}
	}
</script>
<style>
	page {
		background: #FFFBF3;
	}
</style>
<style lang="scss">
	.closeicon {
		position: absolute;
		right: -25rpx;
		top: 5rpx;

		image {
			width: 64rpx;
			height: 64rpx;
		}
	}

	.picbox {
		background-repeat: no-repeat;
		background-size: 100% 100%;
		width: 600rpx;
	}

	.myboxs {
		margin: 50rpx 30rpx 20rpx 30rpx;
		height: 320rpx;
		flex-direction: column;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;

		.tit2 {
			margin-bottom: 20rpx;
			color: #AA7248;
			margin-top: 30rpx;
			font-size: 42rpx;
			font-weight: 700;
		}

		.cont {
			margin-bottom: 20rpx;

			text {
				font-weight: 700;
				letter-spacing: 0rpx;
				font-size: 26rpx;
				color: #AA7248;
				line-height: 42rpx;

				.bold {
					color: #e6702b;
					margin: 0 4rpx;
				}
			}
		}

		.jiazaizhong {
			margin-bottom: 30rpx;
		}
		
		.btn-actions {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			width: 100%;
			padding: 0 20rpx;
			
			.action-btn {
				font-size: 28rpx;
				height: 70rpx;
				line-height: 70rpx;
				padding: 0 30rpx;
				border-radius: 35rpx;
				margin: 0 10rpx;
				box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
				transition: all 0.3s ease;
				
				&::after {
					border: none;
				}
				
				&:active {
					transform: scale(0.95);
				}
				
				&.cancel-btn {
					background-color: #F5F5F5;
					color: #666666;
				}
				
				&.back-btn {
					background-image: linear-gradient(90deg, #FF8C38 0, #FD5819 100%);
					color: #FFFFFF;
				}
			}
		}

		.btn {
			button {
				color: #fff;
				padding: 5rpx 80rpx;
				border-radius: 70rpx;
				font-size: 32rpx;
				background-image: linear-gradient(90deg, #FF8C38 0, #FD5819 100%);

				&::after {
					border: none;
				}
			}
		}
	}

	.contents {
		padding: 0 20rpx;
	}

	.mylist {
		margin: 0 -10rpx;
		display: flex;
		flex-direction: row;
		margin-bottom: 25rpx;

		.item {
			flex: 1;
			margin: 0 10rpx;
			background-repeat: no-repeat;
			width: 100%;
			background-size: 100% 100%;
			border-radius: 16rpx;

			.box11 {
				padding: 30rpx 30rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.left {
					font-size: 40rpx;
					font-weight: 700;
					color: #000;
				}

				.right {
					image {
						width: 15rpx;
						height: 24rpx;
					}
				}
			}
		}
	}

	.none {
		text-align: center;
		display: flex;
		padding: 180rpx 0;
		justify-content: center;
		align-items: center;
		font-size: 32rpx;
		color: #666;
	}

	.cityPart {
		padding: 40rpx 20rpx;

		.title {
			font-weight: 700;
			font-size: 32rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #000;
			margin-bottom: 35rpx;
		}

		.cityList {
			max-height: 600rpx;
			padding-bottom: 30rpx;
			display: flex;
			flex-direction: row;
			flex-wrap: wrap;
			overflow-y: auto;

			.item {
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0 10rpx;
				margin-bottom: 20rpx;
				width: calc(25% - 20rpx);
				background: #efefef;
				height: 70rpx;
				line-height: 70rpx;
				font-size: 28rpx;
				border-radius: 8rpx;

				&.active {
					color: #fff;
					background: #ff7e17;
				}

				&:active {
					transform: scale(0.98);
				}
			}
		}
	}

	.mybox {
		flex-direction: column;
		padding: 40rpx 60rpx 60rpx 60rpx;
		width: 480rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.tit {
			font-weight: 700;
			font-size: 32rpx;
			color: #000;
			margin-bottom: 40rpx;
		}

		.desc {
			font-size: 28rpx;
			color: #FF5500;
			text-align: justify;
			padding: 0 20rpx;
			margin-bottom: 40rpx;
		}

		.btnlist {
			display: flex;
			flex-direction: row;
			width: 100%;

			.btn {

				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 40rpx;
				font-size: 24rpx;

				white-space: nowrap;

				.btn-icon {
					width: 70rpx;
					height: 70rpx;
				}

				&.qa-btn {
					margin: 0;
					padding: 0;
				}

				&.province-btn {
					background-image: linear-gradient(180deg, #FF8510 0, #FFBD73 100%);
					flex: 1;
					margin: 0 20rpx;
					color: #fff;
					font-size: 32rpx;
					border: none;
				}

				&.download-btn {
					background-color: transparent;
					color: #333;
					width: auto;
					border: none;
					padding: 0;
					margin: 0;
				}

				&::after {
					border: none;
				}
			}
		}
	}

	.trend-detail-container {
		padding-bottom: 120rpx;
		
		.head {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			z-index: 999;
			background-color: #FFFAED; /* 淡黄色背景，符合用户喜好 */
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
			
			&::after {
				content: '';
				position: absolute;
				left: 0;
				right: 0;
				bottom: 0;
				height: 1rpx;
				background-color: rgba(0, 0, 0, 0.05);
			}
			
			.header1 {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 88rpx;
				position: relative;
				
				.left {
					position: absolute;
					left: 30rpx;
					top: 50%;
					transform: translateY(-50%);
					z-index: 2;
					
					.itemList {
						.item {
							button {
								background: transparent;
								padding: 0;
								margin: 0;
								line-height: 1;
								
								&::after {
									border: none;
								}
								
								image {
									width: 20rpx;
									height: 36rpx;
									display: block;
								}
							}
						}
					}
				}
				
				.text {
					font-size: 36rpx;
					font-weight: bold;
					color: #FF9933; /* 橙色，符合用户喜好 */
					text-align: center;
				}
			}
		}
		
		.content {
			background-color: #FFFBF3;
			min-height: 100vh;
			
			.contents {
				padding: 30rpx;
				
				.none {
					text-align: center;
					color: #999;
					padding: 50rpx 0;
				}
				
				.industry-content {
					line-height: 1.6;
				}
			}
		}
	}
	
	.content {
		margin-top: 120rpx;
		padding: 30rpx;
	}
	
	.contents {
		padding: 0 20rpx;
	}
</style>