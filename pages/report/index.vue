<template>
	<view class="report-container">
		<!-- 现代化的头部设计 - 规划案例主题 -->
		<view class="header" :style="{'padding-top': titleTop + 'px'}">
			<view class="header-content">
				<view class="back-button" @tap="back">
					<image :src="imgUrl+'/qdkbm/newimage/fhui/back.png'" mode="widthFix"></image>
				</view>
				<text class="title">规划案例</text>
			</view>
			
			<!-- 搜索功能 -->
			<view class="search-box">
				<view class="search-input-wrapper">
					<image class="search-icon" :src="imgUrl+'/qdkbm/newimage/fhui/search.png'" mode="widthFix"></image>
					<input 
						class="search-input" 
						type="text" 
						v-model="searchQuery" 
						placeholder="搜索感兴趣的报告" 
						confirm-type="search"
						@confirm="handleSearch"
					/>
					<image 
						v-if="searchQuery" 
						class="clear-icon" 
						:src="imgUrl+'/qdkbm/newimage/fhui/close.png'" 
						mode="widthFix"
						@tap="clearSearch"
					></image>
				</view>
			</view>
		</view>
		
		<!-- 统计信息 -->
		<view class="stats-area">
			<view class="counter">共<text class="counter-highlight">{{filteredQuestions.length}}</text>篇</view>
		</view>
		
		<!-- 分类标签区 - 使用网格布局而非滚动 -->
		<view class="filter-area">
			<view class="category-tabs">
				<view 
					v-for="(category, index) in categories" 
					:key="index" 
					class="category-tab"
					:class="{ active: currentCategory === category.id }" 
					@tap="switchCategory(category.id, index)"
				>
					{{category.name}}
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-overlay" v-if="loading">
			<view class="loading-content">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
		</view>
		
		<!-- 空数据状态 -->
		<view class="empty-state" v-if="filteredQuestions.length==0 && !loading">
			<image class="empty-icon" :src="imgUrl+'/qdkbm/newimage/fhui/empty.png'" mode="widthFix"></image>
			<text class="empty-text">暂无数据</text>
		</view>
		
		<!-- 报告列表 -->
		<view class="report-list" v-if="filteredQuestions.length>0" :style="{'height':'calc(100vh - '+height+'px)'}">
			<view class="report-list-content">
				<view class="report-item" v-for="(item, index) in filteredQuestions" :key="index" @tap="showAnswer(item)">
					<view class="report-number">{{index + 1}}</view>
					<view class="report-content">
						<view class="report-title">{{item.question}}</view>
						<view class="report-meta">
							<text class="report-type">{{item.type}}</text>
							<text class="report-date" v-if="item.createTime">{{item.createTime.substring(0, 10)}}</text>
						</view>
					</view>
					<view class="arrow-right">
						<image :src="imgUrl+'/qdkbm/newimage/fhui/arrow-right.png'" mode="widthFix"></image>
					</view>
				</view>
			</view>
		</view>
		
		<myDialog :showbox="showbox" @closebox="closebox" @openVip="openVip"></myDialog>
	</view>
</template>

<script>
	import myDialog from '@/components/dialog.vue'
	export default {
		components:{
			myDialog
		},
		data() {
			return {
				showbox: false,
				scrollLeft: 0,
				imgUrl: this.$base.uploadImgUrl,
				loading: true,
				viewCount: 0,
				titleTop: 0,
				height: 0,
				currentCategory: 'all',
				categories: [],
				questions: [],
				searchQuery: '', // 搜索查询文本
				isSearching: false, // 是否正在搜索
				pageAnimating: false // 页面动画状态
			}
		},
		onLoad() {
			this.getList()
			setTimeout(() => {
				this.loading = false
			}, 800)
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top;
			
			// 计算各部分高度以确定内容区域高度
			setTimeout(() => {
				let query = uni.createSelectorQuery().in(this);
				query.selectAll('.header, .stats-area, .filter-area').boundingClientRect(data => {
					let totalHeight = 0;
					data.forEach(item => {
						totalHeight += item.height;
					});
					this.height = totalHeight + 20; // 添加额外间距
				}).exec();
			}, 300);
		},
		computed: {
			filteredQuestions() {
				// 首先按分类筛选
				let filtered = this.questions;
				if (this.currentCategory !== 'all') {
					filtered = filtered.filter(item => item.type === this.categories[this.currentCategory].name);
				}
				
				// 如果有搜索关键词，进一步筛选
				if (this.searchQuery && this.searchQuery.trim() !== '') {
					const query = this.searchQuery.trim().toLowerCase();
					filtered = filtered.filter(item => {
						// 在问题中搜索关键词
						return item.question.toLowerCase().includes(query);
					});
				}
				
				this.refresh()
				return filtered;
			}
		},
		methods: {
			closebox(val){
				this.showbox = val
			},
			openbox(){
				this.showbox = true
			},
			openVip() {
				uni.navigateTo({
					url: '/pages/vip/index'
				});
				this.showbox = false
			},
			refresh() {
				setTimeout(() => {
					this.loading = false
				}, 600)
			},
			moveTo(index) {
				const query = uni.createSelectorQuery().in(this)
				query
					.selectAll(`.category-tab`)
					.boundingClientRect(rect => {
						const {
							windowWidth
						} = uni.getSystemInfoSync();
						let width = 0
			
						for (let i = 0; i < index; i++) {
							width += rect[i].width
						}
			
						if (width > windowWidth / 2) {
							this.scrollLeft = width + rect[index].width / 2 - windowWidth / 2
						} else {
							this.scrollLeft = 0
						}
					}).exec()
			},
			getList() {
				this.$apis.indexpage({
					parentType: 5,
					pageNo:1,
					pageSize:1000
				}).then((res) => {
					if (res.code == 0) {
						this.questions = res.data.list
						let arr = []
						if (res.data.list.length > 0) {
							res.data.list.forEach(item => {
								if (!arr.includes(item.type)) {
									arr.push(item.type);
								}
							});
						}
						let newarr = []
						if (arr.length > 0) {
							arr.map((myitem, myindex) => {
								newarr.push({
									id: Number(myindex + 1),
									name: myitem
								})
							})
						}
						newarr.unshift({
							id: 'all',
							name: "全部"
						})
						this.categories = newarr
					}
				})
			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			},
			handleSearch() {
				// 搜索功能已通过计算属性实现
				// 触发搜索动画
				this.isSearching = true;
				this.loading = true;
				
				// 模拟搜索加载过程
				setTimeout(() => {
					this.loading = false;
					this.isSearching = false;
				}, 300);
			},
			
			clearSearch() {
				this.searchQuery = '';
				// 重新触发搜索处理
				this.handleSearch();
			},
			 showAnswer(item) {
				// let token = uni.getStorageSync('token')
				// if (token) {
				// 	let res = await this.$store.dispatch('getuseInfo')
				// 	const isVip = uni.getStorageSync('isVip') || false;
				// 	this.viewCount = uni.getStorageSync('tys') || 0
					// if (isVip) {
						uni.navigateTo({
							url: "/pages/report/detail?id=" + item.id
						})
					// } else {
					// 	if (this.viewCount <= 0) {
					// 		this.openbox()
					// 	} else {
					// 		this.viewCount--
					// 		uni.setStorageSync('tys', this.viewCount)
					// 		uni.navigateTo({
					// 			url: "/pages/report/detail?id=" + item.id
					// 		})
					// 	}
					// }
				// } else {
				// 	uni.showModal({
				// 		title: '提示',
				// 		content: '您还未登录，请先登录',
				// 		confirmText: '去登录',
				// 		success: (res) => {
				// 			if (res.confirm) {
				// 				uni.navigateTo({
				// 					url: '/pages/login/login'
				// 				});
				// 			}
				// 		}
				// 	});
				// }

			},
			switchCategory(categoryId, index) {
				// 点击分类时添加加载动画
				this.loading = true;
				this.currentCategory = categoryId;
				
				// 不再需要调用moveTo，因为现在使用网格布局
				// 也不需要重新获取列表，只需基于现有数据筛选
				
				setTimeout(() => {
					this.loading = false;
				}, 300);
			}
		}
	}
</script>

<style lang="scss">
	/* 动画效果 */
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
	
	@keyframes fadeIn {
		0% { opacity: 0; }
		100% { opacity: 1; }
	}
	
	@keyframes slideUp {
		0% { transform: translateY(20rpx); opacity: 0; }
		100% { transform: translateY(0); opacity: 1; }
	}

	@keyframes countloading {
		0% {
			transform: rotate(180deg)
		}

		100% {
			transform: rotate(0deg)
		}
	}

	/* 动画效果 */
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
	
	@keyframes fadeIn {
		0% { opacity: 0; }
		100% { opacity: 1; }
	}
	
	@keyframes slideUp {
		0% { transform: translateY(20rpx); opacity: 0; }
		100% { transform: translateY(0); opacity: 1; }
	}
	
	@keyframes pulse {
		0% { transform: scale(1); }
		50% { transform: scale(1.05); }
		100% { transform: scale(1); }
	}

	page {
		background-color: #fffaed; /* 淡黄色背景 */
		font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
		height: 100%;
		overflow: hidden;
	}

	.report-container {
		background-color: #fffaed;
		min-height: 100vh;
		animation: fadeIn 0.5s ease-in-out;
		font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
		position: relative;
	}
	
	/* 头部区域样式 */
	.header {
		background: linear-gradient(to right, #ff9933, #ffbb66);
		padding: 30rpx 30rpx 40rpx;
		color: white;
		position: relative;
		z-index: 10;
		border-bottom-left-radius: 30rpx;
		border-bottom-right-radius: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(255, 153, 51, 0.2);
	
		.header-content {
			display: flex;
			align-items: center;
			margin-bottom: 30rpx;
			
			.back-button {
				width: 50rpx;
				height: 50rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				background-color: rgba(255, 255, 255, 0.3);
				border-radius: 50%;
				margin-right: 20rpx;
				
				image {
					width: 20rpx;
					height: auto;
				}
			}
			
			.title {
				font-size: 36rpx;
				font-weight: bold;
			}
		}
		
		.search-box {
			margin-top: 20rpx;
			
			.search-input-wrapper {
				position: relative;
				background-color: rgba(255, 255, 255, 0.8);
				border-radius: 40rpx;
				display: flex;
				align-items: center;
				padding: 15rpx 25rpx;
				
				.search-icon {
					width: 36rpx;
					height: auto;
					margin-right: 15rpx;
				}
				
				.search-input {
					flex: 1;
					font-size: 28rpx;
					color: #333;
					height: 60rpx;
					line-height: 60rpx;
				}
				
				.clear-icon {
					width: 28rpx;
					height: auto;
					padding: 10rpx;
				}
			}
		}
	}
	
	/* 统计信息区域 */
	.stats-area {
		padding: 20rpx 30rpx;
		display: flex;
		justify-content: flex-end;
		
		.counter {
			font-size: 28rpx;
			color: #666;
			
			.counter-highlight {
				color: #ff9933;
				font-weight: bold;
				margin: 0 5rpx;
			}
		}
	}
	
	/* 分类标签区域 */
	.filter-area {
		padding: 10rpx 20rpx 30rpx;
		
		.category-tabs {
			display: flex;
			flex-wrap: wrap;
			gap: 20rpx;
			
			.category-tab {
				padding: 15rpx 30rpx;
				font-size: 28rpx;
				color: #666;
				background: #fff;
				border: 2rpx solid #e5e5e5;
				border-radius: 30rpx;
				transition: all 0.3s ease;
				
				&.active {
					background: #ff9933;
					color: white;
					border-color: #ff9933;
					box-shadow: 0 4rpx 8rpx rgba(255, 153, 51, 0.3);
				}
			}
		}
	}
	
	/* 加载状态 */
	.loading-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(255, 255, 255, 0.7);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 999;
		
		.loading-content {
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 20rpx;
			
			.loading-spinner {
				width: 60rpx;
				height: 60rpx;
				border: 5rpx solid rgba(255, 153, 51, 0.3);
				border-top: 5rpx solid #ff9933;
				border-radius: 50%;
				animation: spin 1s linear infinite;
			}
			
			.loading-text {
				font-size: 28rpx;
				color: #666;
			}
		}
	}
	
	/* 空数据状态 */
	.empty-state {
		padding: 150rpx 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		
		.empty-icon {
			width: 150rpx;
			margin-bottom: 30rpx;
			opacity: 0.5;
		}
		
		.empty-text {
			font-size: 30rpx;
			color: #999;
		}
	}
	
	/* 报告列表区域 */
	.report-list {
		padding: 0 30rpx;
		overflow-y: auto;
		
		.report-list-content {
			display: flex;
			flex-direction: column;
			gap: 30rpx;
			padding-bottom: 100rpx;
			
			.report-item {
				display: flex;
				align-items: center;
				background: white;
				border-radius: 20rpx;
				padding: 30rpx;
				box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
				transition: transform 0.3s ease;
				animation: slideUp 0.5s ease-out;
				
				&:active {
					transform: scale(0.98);
				}
				
				.report-number {
					width: 60rpx;
					height: 60rpx;
					background: #ff9933;
					color: white;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 28rpx;
					font-weight: bold;
					margin-right: 25rpx;
				}
				
				.report-content {
					flex: 1;
					
					.report-title {
						font-size: 32rpx;
						font-weight: bold;
						color: #333;
						margin-bottom: 15rpx;
						line-height: 1.4;
					}
					
					.report-meta {
						display: flex;
						align-items: center;
						gap: 20rpx;
						
						.report-type {
							font-size: 24rpx;
							color: #ff9933;
							background: rgba(255, 153, 51, 0.1);
							padding: 4rpx 15rpx;
							border-radius: 20rpx;
						}
						
						.report-date {
							font-size: 24rpx;
							color: #999;
						}
					}
				}
				
				.arrow-right {
					width: 30rpx;
					height: 30rpx;
					margin-left: 20rpx;
					opacity: 0.3;
					
					image {
						width: 100%;
						height: auto;
					}
				}
			}
		}
	}
</style>