<template>
  <view class="container">
    <view class="title">分享示例页面</view>
    <view class="info">此页面已配置全局分享功能</view>
    <view class="tip">在微信中可以直接点击右上角"..."进行分享</view>
    
    <button type="primary" @click="handleShare">触发分享按钮</button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      pageData: {
        id: 123,
        title: '自定义分享标题'
      }
    }
  },
  
  // 自定义分享内容（覆盖全局分享）
  share(res) {
    return this.$share({
      title: this.pageData.title,
      path: `/pages/examples/share-example?id=${this.pageData.id}`,
      imageUrl: '/static/images/share-image.png'
    })
  },
  
  // 自定义分享到朋友圈（覆盖全局分享）
  shareTimeline() {
    return {
      title: this.pageData.title,
      path: `/pages/examples/share-example?id=${this.pageData.id}`,
      imageUrl: '/static/images/share-image.png'
    }
  },
  
  methods: {
    handleShare() {
      // #ifdef APP-PLUS
      // App分享
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneSession', // WXSceneSession = 聊天，WXSceneTimeline = 朋友圈
        type: 0, // 图文
        title: this.pageData.title,
        summary: '分享内容摘要',
        imageUrl: '/static/images/share-image.png',
        href: 'https://yourwebsite.com/examples/share',
        success: (res) => {
          console.log('分享成功', res)
        },
        fail: (err) => {
          console.log('分享失败', err)
        }
      })
      // #endif
      
      // #ifdef H5
      // H5分享可以使用第三方分享库或自定义弹窗
      uni.showToast({
        title: 'H5环境请实现相应分享功能',
        icon: 'none'
      })
      // #endif
      
      // #ifdef MP-WEIXIN
      uni.showToast({
        title: '小程序请点击右上角分享按钮',
        icon: 'none'
      })
      // #endif
    }
  }
}
</script>

<style>
.container {
  padding: 30rpx;
}
.title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}
.info, .tip {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}
button {
  margin-top: 50rpx;
}
</style> 