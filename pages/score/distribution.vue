<template>
	<view class="score-distribution-page">
		<!-- 顶部背景和标题区域 -->
		<view class="banner-section" :style="{ 'padding-top': (titleTop + 30) + 'px' }">
			<view class="back-btn" :style="{ 'top': titleTop + 'px' }" @click="back">
				<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-back.png'"></image>
			</view>
			<view class="title">一分一段表</view>
		</view>

		<!-- 筛选区域 - 卡片式设计 -->
		<view class="filter-card">
			<view class="filter-row">
				<view class="filter-item" @click="showProvincePicker" hover-class="filter-item-hover">
					<text class="filter-label">省份:</text>
					<text class="filter-value">{{selectedProvince}}</text>
					<text class="arrow-down">▼</text>
				</view>
				<view class="filter-item" @click="showYearPicker" hover-class="filter-item-hover">
					<text class="filter-label">年份:</text>
					<text class="filter-value">{{selectedYear}}</text>
					<text class="arrow-down">▼</text>
				</view>
				<view class="filter-item" @click="showTypePicker" hover-class="filter-item-hover">
					<text class="filter-label">类别:</text>
					<text class="filter-value">{{selectedType}}</text>
					<text class="arrow-down">▼</text>
				</view>
				<view class="filter-item" @click="showLevelPicker" hover-class="filter-item-hover">
					<text class="filter-label">层次:</text>
					<text class="filter-value">{{selectedLevel}}</text>
					<text class="arrow-down">▼</text>
				</view>
			</view>

			<view class="search-box">
				<input type="number" v-model="searchScore" placeholder="请填写分数" class="score-input" @confirm="searchByScore" />
				<text class="unit">分</text>
				<view class="search-btn" @click="searchByScore" hover-class="search-btn-hover">查询</view>
			</view>
		</view>

		<!-- 查询结果卡片 -->
		<view class="result-card" v-if="searchResult">
			<view class="result-header">
				<view class="result-label">分数</view>
				<view class="result-label">同分人数</view>
				<view class="result-label">排名区间</view>
			</view>
			<view class="result-content">
				<view class="result-value">{{searchResult.score}} <text class="unit">分</text></view>
				<view class="result-value">{{searchResult.sameScoreCount}} <text class="unit">人</text></view>
				<view class="result-value">{{searchResult.rankRange}} <text class="unit">名</text></view>
			</view>
		</view>

		<!-- 历年同位次数据 -->
		<view class="history-card" v-if="historyList.length > 0">
			<view class="card-title">历年同位分</view>
			<view class="table-header">
				<view class="th">年份</view>
				<view class="th">位次区间</view>
				<view class="th">同位分</view>
			</view>
			<view class="table-body">
				<view class="table-row" v-for="(item, index) in historyList" :key="index">
					<view class="td">{{item.year}}</view>
					<view class="td">{{item.rankRange}}</view>
					<view class="td">{{item.score}}</view>
				</view>
			</view>
		</view>

		<!-- 全部分段卡片 -->
		<view class="all-scores-card">
			<view class="card-title">全部分段</view>

			<!-- 分数段筛选标签 -->
			<view class="score-range-tabs">
				<view class="tab-item" :class="{active: activeScoreRange === '600-750'}" @click="setScoreRange('600-750')">
					600-750
				</view>
				<view class="tab-item" :class="{active: activeScoreRange === '500-599'}" @click="setScoreRange('500-599')">
					500-599
				</view>
				<view class="tab-item" :class="{active: activeScoreRange === '400-499'}" @click="setScoreRange('400-499')">
					400-499
				</view>
				<view class="tab-item" :class="{active: activeScoreRange === '0-399'}" @click="setScoreRange('0-399')">
					0-399
				</view>
			</view>

			<!-- 分数段表格 -->
			<view class="score-table">
				<view class="table-header">
					<view class="th">分数</view>
					<view class="th">人数</view>
					<view class="th">排名区间</view>
				</view>
				<view class="table-body">
					<view class="table-row" v-for="(item, index) in filteredScoreRanges" :key="index">
						<view class="td">{{item.score}}</view>
						<view class="td">{{item.count}}</view>
						<view class="td">{{item.rankRange}}</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 原生选择器组件 -->
		<view class="picker-popup" v-if="showProvinceSelector">
			<view class="picker-mask" @click="cancelProvinceSelect"></view>
			<view class="picker-content">
				<view class="picker-header">
					<text class="picker-cancel" @click="cancelProvinceSelect">取消</text>
					<text class="picker-title">选择省份</text>
					<text class="picker-confirm" @click="confirmProvinceSelect">确定</text>
				</view>
				<picker-view class="picker-view" :value="[provinceIndex]" @change="onProvincePickerChange" @pickstart="onPickStart" @pickend="onPickEnd">
					<picker-view-column>
						<view class="picker-item" v-for="(item, index) in provinceList" :key="index">{{item}}</view>
					</picker-view-column>
				</picker-view>
			</view>
		</view>

		<view class="picker-popup" v-if="showYearSelector">
			<view class="picker-mask" @click="cancelYearSelect"></view>
			<view class="picker-content">
				<view class="picker-header">
					<text class="picker-cancel" @click="cancelYearSelect">取消</text>
					<text class="picker-title">选择年份</text>
					<text class="picker-confirm" @click="confirmYearSelect">确定</text>
				</view>
				<picker-view class="picker-view" :value="[yearIndex]" @change="onYearPickerChange" @pickstart="onPickStart" @pickend="onPickEnd">
					<picker-view-column>
						<view class="picker-item" v-for="(item, index) in yearList" :key="index">{{item}}</view>
					</picker-view-column>
				</picker-view>
			</view>
		</view>

		<view class="picker-popup" v-if="showTypeSelector">
			<view class="picker-mask" @click="cancelTypeSelect"></view>
			<view class="picker-content">
				<view class="picker-header">
					<text class="picker-cancel" @click="cancelTypeSelect">取消</text>
					<text class="picker-title">选择类型</text>
					<text class="picker-confirm" @click="confirmTypeSelect">确定</text>
				</view>
				<picker-view class="picker-view" :value="[typeIndex]" @change="onTypePickerChange" @pickstart="onPickStart" @pickend="onPickEnd">
					<picker-view-column>
						<view class="picker-item" v-for="(item, index) in getAvailableTypesList()" :key="index">{{item}}</view>
					</picker-view-column>
				</picker-view>
			</view>
		</view>

		<view class="picker-popup" v-if="showLevelSelector">
			<view class="picker-mask" @click="cancelLevelSelect"></view>
			<view class="picker-content">
				<view class="picker-header">
					<text class="picker-cancel" @click="cancelLevelSelect">取消</text>
					<text class="picker-title">选择层次</text>
					<text class="picker-confirm" @click="confirmLevelSelect">确定</text>
				</view>
				<picker-view class="picker-view" :value="[levelIndex]" @change="onLevelPickerChange" @pickstart="onPickStart" @pickend="onPickEnd">
					<picker-view-column>
						<view class="picker-item" v-for="(item, index) in levelList" :key="index">{{item}}</view>
					</picker-view-column>
				</picker-view>
			</view>
		</view>
	</view>
</template>

<script>
	import subjectSelections from '@/data/subjectSelections.json';

	export default {
		data() {
			return {
				imgUrl: this.$base.uploadImgUrl,
				loading: false,
				titleTop: 0,
				searchScore: '',
				selectedYear: '2024',
				selectedProvince: '安徽',
				selectedType: '物理类',
				selectedLevel: '不分层次',
				activeScoreRange: '600-750',

				// 选择器状态
				showProvinceSelector: false,
				showYearSelector: false,
				showTypeSelector: false,
				showLevelSelector: false,
				provinceIndex: 0,
				yearIndex: 0,
				typeIndex: 0,
				levelIndex: 0,

				// 选择器滚动状态
				isPicking: false,  // 是否正在滚动选择
				currentPickerType: '', // 当前正在操作的选择器类型
				tempProvinceIndex: 0,  // 临时保存选择的省份索引
				tempYearIndex: 0,      // 临时保存选择的年份索引
				tempTypeIndex: 0,      // 临时保存选择的类型索引
				tempLevelIndex: 0,     // 临时保存选择的层次索引

				// 选择器数据
				yearList: ['2024','2023','2022'],
				provinceList: [
                    '安徽', '北京', '上海', '广东', '江苏', '浙江', '山东',
                    '四川', '湖北', '河南', '陕西', '天津', '重庆', '河北',
                    '山西', '内蒙古', '辽宁', '吉林', '黑龙江', '福建', '江西',
                    '湖南', '广西', '海南', '贵州', '云南', '西藏', '甘肃',
                    '青海', '宁夏', '新疆'
                ],
				levelList: ['不分层次'],
				// 科目选择数据
				subjectSelectionsData: subjectSelections.Data,

				// 查询结果
				searchResult: null,

				// 历年同位分数据
				historyList: [],

                // 分数段数据
                filteredScoreRanges: [],

                // 所有分数数据 (存储所有从API获取的分数数据)
                allScoreRanges: [],

                // 加载状态
                isLoading: false,

                // 查询参数
                queryParams: {
                    year: '2024',
                    provincename: '安徽',
                    subjectselection: '物理类',
                    minscore: '600',
                    maxscore: '750'
                }
			}
		},
		onLoad() {
			// 获取状态栏高度
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top;

			// 根据省份和年份获取可用的类型
			const availableTypes = this.getAvailableTypesList();
			if (availableTypes.length > 0) {
				this.selectedType = availableTypes[0];
				this.queryParams.subjectselection = this.selectedType;
			}

			this.queryParams.provincename = this.selectedProvince;
			this.queryParams.year = this.selectedYear;

            // 加载所有数据
            this.fetchScoreRangeData();
		},
		methods: {
			// 返回上一页
			back() {
				uni.navigateBack();
			},

            // 设置分数段筛选
            setScoreRange(range) {
                this.activeScoreRange = range;
                // 设置分数范围
                const rangeValues = range.split('-');
                this.queryParams.minscore = rangeValues[0];
                this.queryParams.maxscore = rangeValues[1];
                // 重新获取数据
                this.fetchScoreRangeData();
            },

			// 按分数查询
			searchByScore() {
				if (!this.searchScore) {
					uni.showToast({
						title: '请输入分数',
						icon: 'none'
					});
					return;
				}

				// 转为数字并限制范围
				let score = parseInt(this.searchScore);
				if (score < 200 || score > 750) {
					uni.showToast({
						title: '分数范围为200-750',
						icon: 'none'
					});
					return;
				}

				this.isLoading = true;
				this.searchResult = null;
				this.historyList = [];

				// 显示加载提示
                uni.showLoading({
                    title: '数据查询中...'
                });

                // 设置查询参数
                this.queryParams.minscore = score.toString();
                this.queryParams.maxscore = score.toString();

                // 发起查询
                this.fetchScoreData(score);
			},

			// 获取分数段数据
			fetchScoreRangeData() {
				this.isLoading = true;
				uni.showLoading({
					title: '数据加载中...'
				});

				uni.request({
					url: this.$base.baseUrl + 'admin-api/system/score-segment/by-condition',
					method: 'GET',
					data: this.queryParams,
					header: {
						'content-type': 'application/x-www-form-urlencoded'
					},
					success: (res) => {
						// 移除这里的hideLoading，因为complete回调中已经有了
						this.isLoading = false;

						if (res.data.code === 0 && res.data.data && res.data.data.length > 0) {
							// 处理分数段数据
							this.allScoreRanges = res.data.data.map(item => ({
								score: item.examinationScore,
								count: item.candidateCount,
								rankRange: item.rankingRange
							}));

							// 按分数从高到低排序
							this.allScoreRanges.sort((a, b) => {
								const scoreA = typeof a.score === 'string' && a.score.includes('-') ?
									parseInt(a.score.split('-')[1]) : parseInt(a.score);
								const scoreB = typeof b.score === 'string' && b.score.includes('-') ?
									parseInt(b.score.split('-')[1]) : parseInt(b.score);
								return scoreB - scoreA;
							});

							// 设置筛选后的数据
							this.filteredScoreRanges = this.allScoreRanges;
						} else {
							this.allScoreRanges = [];
							this.filteredScoreRanges = [];

							uni.showToast({
								title: '暂无分数段数据',
								icon: 'none'
							});
						}
					},
					fail: (err) => {
						// 移除这里的hideLoading，因为complete回调中已经有了
						this.isLoading = false;
						console.error('API请求失败', err);
						uni.showToast({
							title: '网络请求失败，请检查网络',
							icon: 'none'
						});
					},
					complete: () => {
						// 确保在任何情况下都关闭loading
						uni.hideLoading();
						this.isLoading = false;
					}
				});
			},

			// 获取指定分数的详细数据
			fetchScoreData(score) {
				uni.request({
					url: this.$base.baseUrl + 'admin-api/system/score-segment/by-condition',
					method: 'GET',
					data: this.queryParams,
					header: {
						'content-type': 'application/x-www-form-urlencoded'
					},
					success: (res) => {
						// 移除这里的hideLoading，因为complete回调中已经有了
						this.isLoading = false;

						if (res.data.code === 0 && res.data.data && res.data.data.length > 0) {
							const scoreData = res.data.data.find(item =>
								item.examinationScore === score.toString() ||
								item.examinationScore.includes(score.toString())
							);

							if (scoreData) {
								// 找到匹配的分数数据
								this.searchResult = {
									score: scoreData.examinationScore,
									sameScoreCount: scoreData.candidateCount,
									rankRange: scoreData.rankingRange
								};

								// 解析历年同位分数据
								if (scoreData.historicalScores) {
									try {
										const historicalData = JSON.parse(scoreData.historicalScores);
										this.historyList = historicalData.map(item => ({
											year: item.AcademicYear,
											rankRange: item.RankingRange,
											score: item.ExaminationScore
										}));
									} catch (e) {
										console.error('解析历年数据失败', e);
									}
								}
							} else {
								// 找不到匹配的分数
								this.handleScoreNotFound();
							}
						} else {
							// 无数据
							this.handleScoreNotFound();
						}
					},
					fail: (err) => {
						// 移除这里的hideLoading，因为complete回调中已经有了
						this.isLoading = false;
						console.error('API请求失败', err);
						uni.showToast({
							title: '网络请求失败，请检查网络',
							icon: 'none'
						});
					},
					complete: () => {
						// 确保在任何情况下都关闭loading
						uni.hideLoading();
						this.isLoading = false;
					}
				});
			},

			// 处理找不到分数的情况
			handleScoreNotFound() {
				// 移除这里的hideLoading，因为complete回调中已经有了
				this.isLoading = false;
				this.searchResult = null;

				uni.showToast({
					title: '未找到该分数的数据',
					icon: 'none'
				});
			},

			// 省份选择器相关方法
			showProvincePicker() {
				this.provinceIndex = this.provinceList.indexOf(this.selectedProvince);
				if (this.provinceIndex === -1) this.provinceIndex = 0;
				this.tempProvinceIndex = this.provinceIndex;  // 初始化临时索引
				this.currentPickerType = 'province';  // 设置当前选择器类型
				this.showProvinceSelector = true;
			},

			cancelProvinceSelect() {
				this.showProvinceSelector = false;
				this.currentPickerType = '';  // 清除当前选择器类型
			},

			onProvincePickerChange(e) {
				this.tempProvinceIndex = e.detail.value[0];  // 更新临时索引
			},

			confirmProvinceSelect() {
				// 使用临时索引更新真实索引
				this.provinceIndex = this.tempProvinceIndex;
				if (this.provinceIndex >= 0 && this.provinceIndex < this.provinceList.length) {
					this.selectedProvince = this.provinceList[this.provinceIndex];
				}
				this.showProvinceSelector = false;
				this.currentPickerType = '';  // 清除当前选择器类型

				// 根据新选择的省份获取可用的类型
				const availableTypes = this.getAvailableTypesList();
				if (availableTypes.length > 0) {
					this.selectedType = availableTypes[0];
				}

				// 更新查询参数
				this.queryParams.provincename = this.selectedProvince;
				this.queryParams.subjectselection = this.selectedType;

				// 重新获取数据
				this.fetchScoreRangeData();
			},

			// 年份选择器相关方法
			showYearPicker() {
				this.yearIndex = this.yearList.indexOf(this.selectedYear);
				if (this.yearIndex === -1) this.yearIndex = 0;
				this.tempYearIndex = this.yearIndex;  // 初始化临时索引
				this.currentPickerType = 'year';  // 设置当前选择器类型
				this.showYearSelector = true;
			},

			cancelYearSelect() {
				this.showYearSelector = false;
				this.currentPickerType = '';  // 清除当前选择器类型
			},

			onYearPickerChange(e) {
				this.tempYearIndex = e.detail.value[0];  // 更新临时索引
			},

			confirmYearSelect() {
				// 使用临时索引更新真实索引
				this.yearIndex = this.tempYearIndex;
				if (this.yearIndex >= 0 && this.yearIndex < this.yearList.length) {
					this.selectedYear = this.yearList[this.yearIndex];
				}
				this.showYearSelector = false;
				this.currentPickerType = '';  // 清除当前选择器类型

				// 更新查询参数
				this.queryParams.year = this.selectedYear;

				// 根据新选择的年份获取可用的类型
				const availableTypes = this.getAvailableTypesList();
				if (availableTypes.length > 0) {
					this.selectedType = availableTypes[0];
					this.queryParams.subjectselection = this.selectedType;
				}

				// 重新获取数据
				this.fetchScoreRangeData();
			},

			// 类型选择器相关方法
			showTypePicker() {
				// 获取当前省份可用的类型列表
				const availableTypes = this.getAvailableTypesList();
				this.typeIndex = availableTypes.indexOf(this.selectedType);
				if (this.typeIndex === -1) this.typeIndex = 0;
				this.tempTypeIndex = this.typeIndex;  // 初始化临时索引
				this.currentPickerType = 'type';  // 设置当前选择器类型
				this.showTypeSelector = true;
			},

			cancelTypeSelect() {
				this.showTypeSelector = false;
				this.currentPickerType = '';  // 清除当前选择器类型
			},

			onTypePickerChange(e) {
				const availableTypes = this.getAvailableTypesList();
				if (e.detail.value[0] >= 0 && e.detail.value[0] < availableTypes.length) {
					this.tempTypeIndex = e.detail.value[0];  // 更新临时索引
				}
			},

			confirmTypeSelect() {
				const availableTypes = this.getAvailableTypesList();
				// 使用临时索引更新真实索引
				this.typeIndex = this.tempTypeIndex;
				if (this.typeIndex >= 0 && this.typeIndex < availableTypes.length) {
					this.selectedType = availableTypes[this.typeIndex];
				}
				this.showTypeSelector = false;
				this.currentPickerType = '';  // 清除当前选择器类型

				// 更新查询参数
				this.queryParams.subjectselection = this.selectedType;

				// 重新获取数据
				this.fetchScoreRangeData();
			},

			// 层次选择器相关方法
			showLevelPicker() {
				// 目前只有"不分层次"一个选项
				if(this.levelList.length <= 1) {
					uni.showToast({
						title: '当前仅支持不分层次查询',
						icon: 'none',
						duration: 1500
					});
					return;
				}

				this.levelIndex = this.levelList.indexOf(this.selectedLevel);
				if (this.levelIndex === -1) this.levelIndex = 0;
				this.tempLevelIndex = this.levelIndex; // 初始化临时索引
				this.currentPickerType = 'level'; // 设置当前选择器类型
				this.showLevelSelector = true;
			},

			cancelLevelSelect() {
				this.showLevelSelector = false;
				this.currentPickerType = ''; // 清除当前选择器类型
			},

			onLevelPickerChange(e) {
				if(e.detail.value[0] >= 0 && e.detail.value[0] < this.levelList.length) {
					this.tempLevelIndex = e.detail.value[0]; // 更新临时索引
				}
			},

			confirmLevelSelect() {
				// 使用临时索引更新真实索引
				this.levelIndex = this.tempLevelIndex;
				if (this.levelIndex >= 0 && this.levelIndex < this.levelList.length) {
					this.selectedLevel = this.levelList[this.levelIndex];
				}
				this.showLevelSelector = false;
				this.currentPickerType = ''; // 清除当前选择器类型

				// 重新获取数据
				this.fetchScoreRangeData();
			},

			// 选择器开始和结束事件处理
			onPickStart() {
				this.isPicking = true;  // 标记正在选择
			},

			onPickEnd() {
				this.isPicking = false;  // 标记选择结束

				// 根据当前选择器类型更新对应的真实索引
				if (this.currentPickerType === 'province') {
					this.provinceIndex = this.tempProvinceIndex;
				} else if (this.currentPickerType === 'year') {
					this.yearIndex = this.tempYearIndex;
				} else if (this.currentPickerType === 'type') {
					this.typeIndex = this.tempTypeIndex;
				} else if (this.currentPickerType === 'level') {
					this.levelIndex = this.tempLevelIndex;
				}
			},

			getAvailableTypesList() {
				// 从JSON数据中获取当前省份和年份的可用类型
				const provinceData = this.subjectSelectionsData[this.selectedProvince];
				if (!provinceData) {
					// 如果没有找到省份数据，返回默认类型
					return ['物理类', '历史类'];
				}

				const yearData = provinceData[this.selectedYear];
				if (!yearData) {
					// 如果没有找到年份数据，尝试获取最近的年份数据
					const availableYears = Object.keys(provinceData).sort().reverse();
					if (availableYears.length > 0) {
						const latestYear = availableYears[0];
						const latestYearData = provinceData[latestYear];
						return latestYearData.map(item => item.name);
					}
					return ['物理类', '历史类'];
				}

				// 返回当前年份的可用类型
				return yearData.map(item => item.name);
			}
		}
	}
</script>

<style lang="scss" scoped>
.score-distribution-page {
	min-height: 100vh;
	background-color: #FFFAED; // 更改为暖色系浅黄色背景
	padding-bottom: 30rpx;

	/* 顶部banner区域 */
	.banner-section {
		background: linear-gradient(135deg, #FF8918 0%, #FFA94D 100%); // 使用橙色渐变
		border-radius: 0 0 30rpx 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(255,137,24,0.15);
		padding: 80rpx 30rpx 30rpx; // 增加顶部padding
		position: relative;

		.back-btn {
			position: fixed;
			left: 30rpx;
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			z-index: 100;

			image {
				width: 36rpx;
				height: 36rpx;
			}
		}

		.title {
			font-size: 40rpx;
			color: #FFFFFF;
			font-weight: bold;
			text-align: center;
			letter-spacing: 2rpx;
			text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
			margin-top: 10rpx;
		}
	}

	/* 筛选卡片 */
	.filter-card {
		margin: 20rpx;
		background: #fff;
		border-radius: 20rpx;
		box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
		padding: 20rpx 15rpx;  /* 减小内边距 */
		animation: slideIn 0.5s ease-out;

		.filter-row {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			margin-bottom: 20rpx;  /* 减小底部间距 */

			.filter-item {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 12rpx 8rpx;  /* 减小内边距 */
				margin-bottom: 10rpx;
				border-radius: 8rpx;  /* 减小圆角 */
				background: #FFF8F2;
				border: 1rpx solid #FFE4CC;
				font-size: 24rpx;  /* 减小字体大小 */
				color: #666;
				width: 48%;  /* 改为两列布局 */
				box-sizing: border-box;
				transition: all 0.2s ease;

				&:active {
					transform: scale(0.98);
				}

				text {
					color: #333;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}

				.filter-label {
					color: #999;
					font-size: 22rpx;
					margin-right: 4rpx;
					flex-shrink: 0;
				}

				.filter-value {
					color: #333;
					flex: 1;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					text-align: center;
				}

				.arrow-down {
					color: #FF8918;
					font-size: 18rpx;
					margin-left: 4rpx;
					flex-shrink: 0;
				}
			}

			.filter-item-hover {
				background: #FFE4CC;
				color: #FF8918;
				transform: scale(0.98);
			}
		}

		.search-box {
			display: flex;
			align-items: center;

			.score-input {
				flex: 1;
				height: 70rpx; /* 减小高度 */
				background: #FFF8F2;
				border-radius: 8rpx; /* 减小圆角 */
				border: 1rpx solid #FFE4CC;
				padding: 0 15rpx; /* 减小内边距 */
				font-size: 28rpx; /* 减小字体大小 */
				color: #333;
				text-align: right;
			}

			.unit {
				margin: 0 15rpx; /* 减小间距 */
				font-size: 26rpx;
				color: #666;
			}

			.search-btn {
				background: linear-gradient(135deg, #FF8918 0%, #FB6E3F 100%);
				color: #fff;
				border-radius: 8rpx; /* 减小圆角 */
				font-size: 26rpx; /* 减小字体大小 */
				font-weight: bold;
				box-shadow: 0 4rpx 8rpx rgba(255,137,24,0.2);
				width: 100rpx; /* 减小宽度 */
				height: 70rpx; /* 减小高度 */
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.2s ease;

				&:active {
					transform: scale(0.98);
					box-shadow: 0 2rpx 6rpx rgba(255,137,24,0.2);
				}
			}

			.search-btn-hover {
				transform: scale(0.95);
				box-shadow: 0 2rpx 4rpx rgba(255,137,24,0.15);
				opacity: 0.9;
			}
		}
	}

	/* 查询结果卡片 */
	.result-card {
		margin: 20rpx;
		background: #fff;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
		animation: slideIn 0.5s ease-out;

		.card-title {
			padding: 24rpx 32rpx 12rpx 32rpx;
			font-size: 32rpx;
			font-weight: bold;
			color: #FF8918;
			border-bottom: 2rpx solid #FFF3E5;
			background: #fff;
		}

		.result-header {
			display: flex;
			justify-content: space-between;
			padding: 20rpx 32rpx;
			background: #FFF8F2;

			.result-label {
				flex: 1;
				font-size: 28rpx;
				color: #666;
				text-align: center;
				font-weight: 500;
			}
		}

		.result-content {
			display: flex;
			justify-content: space-between;
			padding: 30rpx;

			.result-value {
				flex: 1;
				font-size: 36rpx;
				color: #333;
				font-weight: bold;
				text-align: center;

				.unit {
					font-size: 24rpx;
					color: #999;
					font-weight: normal;
					margin-left: 4rpx;
				}
			}
		}
	}

	/* 全部分段卡片 */
	.all-scores-card {
		margin: 20rpx;
		background: #fff;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
		animation: slideIn 0.5s ease-out;

		.card-title {
			padding: 24rpx 32rpx;
			font-size: 32rpx;
			font-weight: bold;
			color: #FF8918;
			border-bottom: 2rpx solid #FFE4CC;
		}

		.score-range-tabs {
			display: flex;
			border-bottom: 2rpx solid #FFE4CC;
			background: #FFF8F2;

			.tab-item {
				flex: 1;
				text-align: center;
				padding: 20rpx 0;
				font-size: 28rpx;
				color: #666;
				position: relative;
				transition: all 0.3s ease;

				&.active {
					color: #FF8918;
					font-weight: bold;

					&::after {
						content: '';
						position: absolute;
						width: 40%;
						height: 4rpx;
						background: #FF8918;
						bottom: 0;
						left: 50%;
						transform: translateX(-50%);
						border-radius: 4rpx;
					}
				}
			}
		}

		.score-table {
			.table-header {
				display: flex;
				background: #FFF8F2;
				border-bottom: 2rpx solid #FFE4CC;

				.th {
					flex: 1;
					padding: 20rpx 0;
					font-size: 28rpx;
					color: #666;
					text-align: center;
					font-weight: 500;
				}
			}

			.table-body {
				.table-row {
					display: flex;
					border-bottom: 1rpx solid #F5F5F5;

					&:nth-child(odd) {
						background: #FFFCF7;
					}

					.td {
						flex: 1;
						padding: 24rpx 0;
						font-size: 28rpx;
						color: #333;
						text-align: center;
					}
				}
			}
		}
	}

	/* 历年同位分卡片 */
	.history-card {
		margin: 20rpx;
		background: #fff;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
		margin-bottom: 50rpx;
		animation: slideIn 0.5s ease-out;

		.card-title {
			padding: 24rpx 32rpx;
			font-size: 32rpx;
			font-weight: bold;
			color: #FF8918;
			border-bottom: 2rpx solid #FFE4CC;
		}

		.table-header {
			display: flex;
			background: #FFF8F2;
			border-bottom: 2rpx solid #FFE4CC;

			.th {
				flex: 1;
				padding: 20rpx 0;
				font-size: 28rpx;
				color: #666;
				text-align: center;
				font-weight: 500;
			}
		}

		.table-body {
			.table-row {
				display: flex;
				border-bottom: 1rpx solid #F5F5F5;

				&:nth-child(odd) {
					background: #FFFCF7;
				}

				&:last-child {
					border-bottom: none;
				}

				.td {
					flex: 1;
					padding: 24rpx 0;
					font-size: 28rpx;
					color: #333;
					text-align: center;
				}
			}
		}
	}
}

@keyframes slideIn {
	0% {
		opacity: 0;
		transform: translateY(20rpx);
	}
	100% {
		opacity: 1;
		transform: translateY(0);
	}
}

/* 选择器弹窗样式 */
.picker-popup {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1000;

	.picker-mask {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.4);
	}

	.picker-content {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #fff;
		transform: translateY(0);
		transition: transform 0.3s;
		border-radius: 24rpx 24rpx 0 0;
		overflow: hidden;
		box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);

		.picker-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 100rpx;
			padding: 0 30rpx;
			background-color: #FFF8F2;
			border-bottom: 1rpx solid #FFE4CC;

			.picker-cancel {
				color: #999;
				font-size: 30rpx;
				padding: 20rpx;
				margin: -20rpx;
			}

			.picker-title {
				color: #333;
				font-size: 32rpx;
				font-weight: bold;
			}

			.picker-confirm {
				color: #FF8918;
				font-size: 30rpx;
				font-weight: bold;
				padding: 20rpx;
				margin: -20rpx;
			}
		}

		.picker-view {
			width: 100%;
			height: 480rpx;
			position: relative;

			&::before, &::after {
				content: '';
				position: absolute;
				left: 0;
				right: 0;
				height: 192rpx;
				z-index: 1;
				pointer-events: none;
			}

			&::before {
				top: 0;
				background: linear-gradient(to bottom, rgba(255,255,255,0.9), rgba(255,255,255,0.3));
			}

			&::after {
				bottom: 0;
				background: linear-gradient(to top, rgba(255,255,255,0.9), rgba(255,255,255,0.3));
			}

			.picker-item {
				line-height: 80rpx;
				text-align: center;
				color: #333;
				font-size: 32rpx;
				padding: 0 30rpx;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
	}
}
</style>
