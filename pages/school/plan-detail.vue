<template>
	<view class="plan-detail-container">
		<!-- 头部 -->
		<view class="header" :style="{ 'padding-top': titleTop + 'px' }">
			<view class="back-btn" @tap="back">
				<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-back.png'" mode="widthFix"></image>
			</view>
			<view class="title">{{ detailType === 'school' ? '学校招生计划' : '专业招生计划' }}</view>
			<view class="right-placeholder"></view>
		</view>

		<!-- 学校或专业信息 -->
		<view class="info-card">
			<block v-if="detailType === 'school'">
				<view class="school-header">
					<view class="school-logo">
						<image :src="planInfo.logo || imgUrl + '/qdkbm/newimage/fhui/default-school.png'"></image>
					</view>
					<view class="school-info">
						<view class="school-name">{{ planInfo.name }}</view>
						<view class="school-meta">{{ planInfo.location }} | {{ planInfo.type }}</view>
						<view class="school-plan">
							<view class="plan-item">
								<text class="label">24年计划</text>
								<text class="value highlight">{{ planInfo.planCount || 0 }}人</text>
							</view>
							<view class="plan-item">
								<text class="label">招生专业</text>
								<text class="value">{{ planInfo.majorCount || 0 }}个</text>
							</view>
						</view>
					</view>
				</view>
			</block>
			<block v-else>
				<view class="major-header">
					<view class="major-name">{{ planInfo.name }}</view>
					<view class="major-meta">{{ planInfo.category }}</view>
					<view class="major-plan">
						<view class="plan-item">
							<text class="label">24年计划</text>
							<text class="value highlight">{{ planInfo.planCount || 0 }}人</text>
						</view>
						<view class="plan-item">
							<text class="label">招生院校</text>
							<text class="value">{{ planInfo.schoolCount || 0 }}所</text>
						</view>
					</view>
				</view>
			</block>
		</view>

		<!-- 筛选条件 -->
		<view class="filter-section">
			<view class="filter-item" @tap="showYearPicker">
				<text>{{ selectedYear }}</text>
				<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-arrow-down.png'" class="arrow-icon"></image>
			</view>
			<view class="filter-separator"></view>
			<view class="filter-item" @tap="showSubjectPicker">
				<text>{{ selectedSubject || '选科组合' }}</text>
				<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-arrow-down.png'" class="arrow-icon"></image>
			</view>
			<view class="filter-separator"></view>
			<view class="filter-item" @tap="showProvincePicker" v-if="detailType === 'school'">
				<text>{{ selectedProvince || '招生省份' }}</text>
				<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-arrow-down.png'" class="arrow-icon"></image>
			</view>
			<view class="filter-item" @tap="showBatchPicker" v-else>
				<text>{{ selectedBatch || '招生批次' }}</text>
				<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-arrow-down.png'" class="arrow-icon"></image>
			</view>
		</view>

		<!-- 列表标题 -->
		<view class="list-header">
			<block v-if="detailType === 'school'">
				<view class="column major">专业名称</view>
				<view class="column">计划数</view>
				<view class="column">选科要求</view>
				<view class="column">批次</view>
			</block>
			<block v-else>
				<view class="column school">院校名称</view>
				<view class="column">计划数</view>
				<view class="column">选科要求</view>
				<view class="column">批次</view>
			</block>
		</view>

		<!-- 列表内容 -->
		<scroll-view scroll-y class="list-content">
			<view v-if="listData.length > 0">
				<view class="list-item" v-for="(item, index) in listData" :key="index">
					<block v-if="detailType === 'school'">
						<view class="column major">{{ item.name }}</view>
						<view class="column">{{ item.count }}</view>
						<view class="column">{{ item.subject }}</view>
						<view class="column">{{ item.batch }}</view>
					</block>
					<block v-else>
						<view class="column school">{{ item.name }}</view>
						<view class="column">{{ item.count }}</view>
						<view class="column">{{ item.subject }}</view>
						<view class="column">{{ item.batch }}</view>
					</block>
				</view>
			</view>
			<view class="empty-state" v-else>
				<image :src="imgUrl + '/qdkbm/newimage/fhui/empty-search.png'" mode="widthFix"></image>
				<text>没有找到符合条件的招生计划</text>
			</view>
		</scroll-view>

		<!-- 年份选择器 -->
		<u-popup :show="showYearPicker" mode="bottom" @close="closeYearPicker">
			<view class="picker-container">
				<view class="picker-header">
					<text class="picker-title">选择年份</text>
					<view class="picker-close" @tap="closeYearPicker">
						<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-close.png'"></image>
					</view>
				</view>
				<scroll-view scroll-y class="picker-content">
					<view
						class="picker-item"
						v-for="(year, index) in yearOptions"
						:key="index"
						:class="{ active: selectedYear === year }"
						@tap="selectYear(year)"
					>
						{{ year }}
					</view>
				</scroll-view>
			</view>
		</u-popup>

		<!-- 选科组合选择器 -->
		<u-popup :show="showSubjectPicker" mode="bottom" @close="closeSubjectPicker">
			<view class="picker-container">
				<view class="picker-header">
					<text class="picker-title">选择选科组合</text>
					<view class="picker-close" @tap="closeSubjectPicker">
						<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-close.png'"></image>
					</view>
				</view>
				<scroll-view scroll-y class="picker-content">
					<view
						class="picker-item"
						@tap="selectSubject('')"
						:class="{ active: selectedSubject === '' }"
					>
						全部
					</view>
					<view
						class="picker-item"
						v-for="(subject, index) in subjectOptions"
						:key="index"
						:class="{ active: selectedSubject === subject }"
						@tap="selectSubject(subject)"
					>
						{{ subject }}
					</view>
				</scroll-view>
			</view>
		</u-popup>

		<!-- 省份选择器 -->
		<u-popup :show="showProvincePicker" mode="bottom" @close="closeProvincePicker">
			<view class="picker-container">
				<view class="picker-header">
					<text class="picker-title">选择省份</text>
					<view class="picker-close" @tap="closeProvincePicker">
						<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-close.png'"></image>
					</view>
				</view>
				<scroll-view scroll-y class="picker-content">
					<view
						class="picker-item"
						@tap="selectProvince('')"
						:class="{ active: selectedProvince === '' }"
					>
						全部
					</view>
					<view
						class="picker-item"
						v-for="(province, index) in provinceOptions"
						:key="index"
						:class="{ active: selectedProvince === province }"
						@tap="selectProvince(province)"
					>
						{{ province }}
					</view>
				</scroll-view>
			</view>
		</u-popup>

		<!-- 批次选择器 -->
		<u-popup :show="showBatchPicker" mode="bottom" @close="closeBatchPicker">
			<view class="picker-container">
				<view class="picker-header">
					<text class="picker-title">选择批次</text>
					<view class="picker-close" @tap="closeBatchPicker">
						<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-close.png'"></image>
					</view>
				</view>
				<scroll-view scroll-y class="picker-content">
					<view
						class="picker-item"
						@tap="selectBatch('')"
						:class="{ active: selectedBatch === '' }"
					>
						全部
					</view>
					<view
						class="picker-item"
						v-for="(batch, index) in batchOptions"
						:key="index"
						:class="{ active: selectedBatch === batch }"
						@tap="selectBatch(batch)"
					>
						{{ batch }}
					</view>
				</scroll-view>
			</view>
		</u-popup>
	</view>
</template>

<script>
import apiConfig from '@/config/api-config.js'
import regionConfig from '@/config/region-config.js'

export default {
	data() {
		return {
			titleTop: 0,
			imgUrl: this.$base.uploadImgUrl,
			id: null,
			name: '',
			detailType: 'school', // 'school' or 'major'

			planInfo: {},
			listData: [],

			selectedYear: '2024年',
			selectedSubject: '',
			selectedProvince: '',
			selectedBatch: '',

			showYearPicker: false,
			showSubjectPicker: false,
			showProvincePicker: false,
			showBatchPicker: false,

			yearOptions: ['2024年', '2023年', '2022年', '2021年'],
			subjectOptions: ['物理/化学/生物', '物理/化学/地理', '物理/生物/地理', '历史/政治/地理', '历史/政治/化学', '历史/地理/生物'],
			provinceOptions: regionConfig.provinces,
			batchOptions: ['本科批', '特殊类型招生批', '提前批', '国家专项计划批', '地方专项计划批', '高校专项计划批'],

			// API相关
			loading: false,
			page: 1,
			pageSize: 50,
			hasMore: true,
			guguApiUrl: apiConfig.guguData.baseUrl + apiConfig.guguData.endpoints.enrollmentPlan,
			appkey: apiConfig.guguData.appkey
		}
	},
	onLoad(options) {
		// 获取胶囊按钮位置信息，用于设置顶部安全距离
		let res = uni.getMenuButtonBoundingClientRect();
		this.titleTop = res.top;

		// 获取传递的参数
		this.id = options.id;
		this.name = decodeURIComponent(options.name || '');
		this.detailType = options.type || 'school';

		// 加载数据
		this.loadDetailData();
		this.loadListData();
	},
	methods: {
		// 加载详情数据
		loadDetailData() {
			// 初始化基本信息
			if (this.detailType === 'school') {
				this.planInfo = {
					id: this.id,
					name: this.name,
					logo: '',
					location: '',
					type: '',
					planCount: 0,
					majorCount: 0
				};
			} else {
				this.planInfo = {
					id: this.id,
					name: this.name,
					category: '',
					planCount: 0,
					schoolCount: 0
				};
			}

			// 从API获取统计数据
			this.loadSummaryData();
		},

		// 加载列表数据
		loadListData() {
			if (this.loading) return;

			this.loading = true;
			const params = this.buildApiParams();

			uni.showLoading({
				title: '加载中...'
			});

			uni.request({
				url: this.guguApiUrl,
				data: params,
				method: 'GET',
				success: (res) => {
					uni.hideLoading();

					if (res.statusCode === 200 && res.data.DataStatus && res.data.DataStatus.StatusCode === 100) {
						const apiData = res.data.Data || [];

						// 处理列表数据
						this.listData = apiData.map(item => ({
							name: this.detailType === 'school' ? item.CollegeMajorName : item.SchoolName,
							count: item.EnrollmentNumbers || '0',
							subject: item.CourseSelectionRequirements || '不限',
							batch: item.BatchName || '未知',
							province: item.ProvinceName || '',
							year: item.Year || '',
							classOne: item.ClassOne || '',
							classTwo: item.ClassTwo || '',
							type: item.Type || ''
						}));

						console.log('详情页列表数据:', this.listData);
					} else {
						uni.showToast({
							title: '获取数据失败',
							icon: 'none'
						});
					}

					this.loading = false;
				},
				fail: (err) => {
					console.error('API请求失败:', err);
					uni.hideLoading();
					uni.showToast({
						title: '网络请求失败',
						icon: 'none'
					});
					this.loading = false;
				}
			});
		},

		// 构建API参数
		buildApiParams() {
			const params = {
				appkey: this.appkey,
				pageIndex: this.page,
				pageSize: this.pageSize,
				year: this.selectedYear ? parseInt(this.selectedYear.replace('年', '')) : 2024
			};

			// 根据详情类型设置参数
			if (this.detailType === 'school') {
				params.schoolname = this.name;
			} else {
				params.collegemajorname = this.name;
			}

			// 添加筛选条件
			if (this.selectedProvince) {
				params.provincename = this.selectedProvince;
			}
			if (this.selectedBatch) {
				params.batchname = this.selectedBatch;
			}
			if (this.selectedSubject) {
				params.type = this.selectedSubject;
			}

			return params;
		},

		// 加载统计数据
		loadSummaryData() {
			const params = {
				appkey: this.appkey,
				pageIndex: 1,
				pageSize: 1000, // 获取更多数据用于统计
				year: 2024
			};

			// 根据详情类型设置参数
			if (this.detailType === 'school') {
				params.schoolname = this.name;
			} else {
				params.collegemajorname = this.name;
			}

			uni.request({
				url: this.guguApiUrl,
				data: params,
				method: 'GET',
				success: (res) => {
					if (res.statusCode === 200 && res.data.DataStatus && res.data.DataStatus.StatusCode === 100) {
						const apiData = res.data.Data || [];

						if (this.detailType === 'school') {
							// 统计学校的专业数量和招生人数
							const majors = new Set();
							let totalPlan = 0;
							let provinces = new Set();

							apiData.forEach(item => {
								majors.add(item.CollegeMajorName);
								totalPlan += parseInt(item.EnrollmentNumbers || 0);
								provinces.add(item.ProvinceName);
							});

							this.planInfo.majorCount = majors.size;
							this.planInfo.planCount = totalPlan;
							this.planInfo.location = Array.from(provinces).slice(0, 3).join('、');
							this.planInfo.type = apiData.length > 0 ? (apiData[0].Type || '综合') : '综合';
						} else {
							// 统计专业的学校数量和招生人数
							const schools = new Set();
							let totalPlan = 0;
							let categories = new Set();

							apiData.forEach(item => {
								schools.add(item.SchoolName);
								totalPlan += parseInt(item.EnrollmentNumbers || 0);
								if (item.ClassOne) categories.add(item.ClassOne);
								if (item.ClassTwo) categories.add(item.ClassTwo);
							});

							this.planInfo.schoolCount = schools.size;
							this.planInfo.planCount = totalPlan;
							this.planInfo.category = Array.from(categories).join('-');
						}
					}
				},
				fail: (err) => {
					console.error('统计数据获取失败:', err);
				}
			});
		},

		// 显示年份选择器
		showYearPicker() {
			this.showYearPicker = true;
		},

		// 关闭年份选择器
		closeYearPicker() {
			this.showYearPicker = false;
		},

		// 选择年份
		selectYear(year) {
			this.selectedYear = year;
			this.closeYearPicker();
			this.loadListData();
		},

		// 显示选科组合选择器
		showSubjectPicker() {
			this.showSubjectPicker = true;
		},

		// 关闭选科组合选择器
		closeSubjectPicker() {
			this.showSubjectPicker = false;
		},

		// 选择选科组合
		selectSubject(subject) {
			this.selectedSubject = subject;
			this.closeSubjectPicker();
			this.loadListData();
		},

		// 显示省份选择器
		showProvincePicker() {
			this.showProvincePicker = true;
		},

		// 关闭省份选择器
		closeProvincePicker() {
			this.showProvincePicker = false;
		},

		// 选择省份
		selectProvince(province) {
			this.selectedProvince = province;
			this.closeProvincePicker();
			this.loadListData();
		},

		// 显示批次选择器
		showBatchPicker() {
			this.showBatchPicker = true;
		},

		// 关闭批次选择器
		closeBatchPicker() {
			this.showBatchPicker = false;
		},

		// 选择批次
		selectBatch(batch) {
			this.selectedBatch = batch;
			this.closeBatchPicker();
			this.loadListData();
		},

		// 返回上一页
		back() {
			uni.navigateBack({
				delta: 1
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.plan-detail-container {
	background-color: #f5f7fa;
	min-height: 100vh;
}

.header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 40rpx;
	background-color: #fff;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;

		image {
			width: 40rpx;
			height: 40rpx;
		}
	}

	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}

	.right-placeholder {
		width: 60rpx;
	}
}

.info-card {
	background-color: #fff;
	margin-top: 2rpx;
	padding: 30rpx;

	.school-header {
		display: flex;

		.school-logo {
			width: 120rpx;
			height: 120rpx;
			margin-right: 30rpx;

			image {
				width: 100%;
				height: 100%;
				border-radius: 10rpx;
			}
		}

		.school-info {
			flex: 1;
		}

		.school-name {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 10rpx;
		}

		.school-meta {
			font-size: 26rpx;
			color: #999;
			margin-bottom: 20rpx;
		}

		.school-plan {
			display: flex;

			.plan-item {
				margin-right: 40rpx;

				.label {
					font-size: 26rpx;
					color: #666;
				}

				.value {
					font-size: 28rpx;
					color: #333;
					margin-left: 10rpx;

					&.highlight {
						color: #ff8918;
						font-weight: bold;
					}
				}
			}
		}
	}

	.major-header {
		.major-name {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 10rpx;
		}

		.major-meta {
			font-size: 26rpx;
			color: #999;
			margin-bottom: 20rpx;
		}

		.major-plan {
			display: flex;

			.plan-item {
				margin-right: 40rpx;

				.label {
					font-size: 26rpx;
					color: #666;
				}

				.value {
					font-size: 28rpx;
					color: #333;
					margin-left: 10rpx;

					&.highlight {
						color: #ff8918;
						font-weight: bold;
					}
				}
			}
		}
	}
}

.filter-section {
	display: flex;
	align-items: center;
	height: 80rpx;
	background-color: #fff;
	margin-top: 20rpx;
	padding: 0 20rpx;

	.filter-item {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 26rpx;
		color: #666;

		text {
			max-width: 160rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.arrow-icon {
			width: 24rpx;
			height: 24rpx;
			margin-left: 8rpx;
		}
	}

	.filter-separator {
		width: 2rpx;
		height: 30rpx;
		background-color: #eee;
	}
}

.list-header {
	display: flex;
	height: 80rpx;
	background-color: #f8f8f8;
	border-bottom: 1rpx solid #eee;
	margin-top: 2rpx;

	.column {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #666;
		font-weight: bold;

		&.major, &.school {
			flex: 2;
			justify-content: flex-start;
			padding-left: 30rpx;
		}
	}
}

.list-content {
	height: calc(100vh - 420rpx);

	.list-item {
		display: flex;
		height: 100rpx;
		background-color: #fff;
		border-bottom: 1rpx solid #f5f5f5;

		.column {
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 26rpx;
			color: #333;

			&.major, &.school {
				flex: 2;
				justify-content: flex-start;
				padding-left: 30rpx;
				font-weight: bold;
			}
		}
	}
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;

	image {
		width: 200rpx;
		margin-bottom: 30rpx;
	}

	text {
		font-size: 28rpx;
		color: #999;
	}
}

.picker-container {
	background-color: #fff;
	border-radius: 20rpx 20rpx 0 0;
	overflow: hidden;

	.picker-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx;
		border-bottom: 1rpx solid #eee;

		.picker-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}

		.picker-close {
			width: 40rpx;
			height: 40rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}
	}

	.picker-content {
		height: 60vh;
		padding: 20rpx 30rpx;

		.picker-item {
			height: 80rpx;
			line-height: 80rpx;
			font-size: 28rpx;
			color: #333;
			border-bottom: 1rpx solid #f5f5f5;

			&.active {
				color: #ff8918;
				font-weight: bold;
			}
		}
	}
}
</style>