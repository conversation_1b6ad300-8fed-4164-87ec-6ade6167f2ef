<template>
  <view class="school-detail-container">
    <!-- 加载动画 -->
    <view class="loading-container" v-if="loading">
      <view class="loading-spinner">
        <view class="loading-circle"></view>
        <view class="loading-text">加载中</view>
      </view>
    </view>

    <!-- 头部区域 -->
    <view class="header" :style="{ 'padding-top': titleTop + 'px' }">
      <view class="back-btn" @tap="goBack" :style="{ 'top': titleTop + 'px' }">
        <image :src="imgUrl + '/qdkbm/newimage/fhui/icon-back.png'"></image>
      </view>
      <view class="nav-bar">
        <view class="page-title">学校详情</view>
      </view>
      
      <view class="school-basic-info">
        <view class="school-logo">
          <template v-if="schoolInfo.coverImage">
            <image :src="schoolInfo.coverImage" class="school-cover-image"></image>
          </template>
          <template v-else>
            <view class="logo-placeholder">
              <text>{{ getSchoolInitial(schoolInfo.name) }}</text>
            </view>
          </template>
        </view>
        <view class="school-name-container">
          <view class="school-name">
            {{ schoolInfo.name }}
            <view class="school-tags">
              <view class="tag tag-985" v-if="schoolInfo.is985">985</view>
              <view class="tag tag-211" v-if="schoolInfo.is211">211</view>
              <view class="tag tag-double" v-if="schoolInfo.isDoubleFirst">双一流</view>
              <view class="tag tag-edu" v-if="schoolInfo.educationLevel">{{ schoolInfo.educationLevel }}</view>
              <view class="tag tag-property" v-if="schoolInfo.collegeProperty">{{ schoolInfo.collegeProperty }}</view>
            </view>
          </view>
          <view class="school-location">
            <image :src="imgUrl + '/qdkbm/newimage/fhui/icon-location-white.png'" class="location-icon"></image>
            {{ schoolInfo.province }} {{ schoolInfo.city }}
          </view>
        </view>
      </view>
    </view>

    <!-- 主要内容区 -->
    <view class="main-content">
      <!-- 学校概览 -->
      <view class="info-card">
        <view class="card-title">学校概览</view>
        <view class="info-grid">
          <view class="info-item">
            <view class="item-label">学校类型</view>
            <view class="item-value">{{ schoolInfo.type || '暂无' }}</view>
          </view>
          <view class="info-item">
            <view class="item-label">办学层次</view>
            <view class="item-value">{{ schoolInfo.educationLevel || '暂无' }}</view>
          </view>
          <view class="info-item">
            <view class="item-label">全国排名</view>
            <view class="item-value">{{ schoolInfo.rank || '暂无' }}</view>
          </view>
          <view class="info-item">
            <view class="item-label">学校性质</view>
            <view class="item-value">{{ schoolInfo.collegeProperty || '暂无' }}</view>
          </view>
        </view>
      </view>
      
      <!-- 学校简介 -->
      <view class="info-card">
        <view class="card-title">学校简介</view>
        <view class="school-intro">
          <text>{{ schoolInfo.intro ? (showFullIntro ? schoolInfo.intro : schoolInfo.intro.substring(0, 100) + '...') : '暂无简介' }}</text>
        </view>
        <view class="more-btn" v-if="schoolInfo.intro && schoolInfo.intro.length > 100 && !showFullIntro" @tap="openIntroDrawer">
          <text>查看更多</text>
          <image :src="imgUrl + '/qdkbm/newimage/fhui/icon-arrow-down.png'" class="more-icon"></image>
        </view>
      </view>
      
      <!-- 招生专业 -->
      <view class="info-card">
        <view class="card-title">招生专业</view>
        <view class="major-list" v-if="majorList.length > 0">
          <view class="major-item" v-for="(item, index) in displayedMajorList" :key="index">
            <view class="major-name">{{ item.name }}</view>
            <view class="major-attrs">
              <view class="major-attr">
                <text class="attr-label">学制：</text>
                <text class="attr-value">{{ item.duration || '暂无' }}</text>
              </view>
              <view class="major-attr">
                <text class="attr-label">类型：</text>
                <text class="attr-value">{{ item.type || '暂无' }}</text>
              </view>
            </view>
          </view>
        </view>
        <view class="empty-tip" v-else>暂无专业信息</view>
        <view class="more-btn" v-if="majorList.length > 3 && !showAllMajors" @tap="openMajorDrawer">
          <text>查看更多</text>
          <image :src="imgUrl + '/qdkbm/newimage/fhui/icon-arrow-down.png'" class="more-icon"></image>
        </view>
      </view>
      
      <!-- 联系方式 -->
      <view class="info-card">
        <view class="card-title">联系方式</view>
        <view class="contact-list">
          <view class="contact-item">
            <view class="contact-icon">
              <image :src="imgUrl + '/qdkbm/newimage/fhui/icon-website.png'"></image>
            </view>
            <view class="contact-info">
              <view class="contact-label">官方网站</view>
              <view class="contact-value">{{ schoolInfo.website || '暂无' }}</view>
            </view>
          </view>
          <view class="contact-item">
            <view class="contact-icon">
              <image :src="imgUrl + '/qdkbm/newimage/fhui/icon-phone.png'"></image>
            </view>
            <view class="contact-info">
              <view class="contact-label">招生电话</view>
              <view class="contact-value">{{ schoolInfo.phone || '暂无' }}</view>
            </view>
          </view>
          <view class="contact-item">
            <view class="contact-icon">
              <image :src="imgUrl + '/qdkbm/newimage/fhui/icon-address.png'"></image>
            </view>
            <view class="contact-info">
              <view class="contact-label">学校地址</view>
              <view class="contact-value">{{ schoolInfo.address || '暂无' }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 学校简介抽屉 -->
    <view class="drawer-mask" v-if="introDrawerVisible" @tap="closeIntroDrawer"></view>
    <view class="drawer" :class="{ 'drawer-show': introDrawerVisible }">
      <view class="drawer-header">
        <view class="drawer-title">学校简介</view>
        <view class="drawer-close" @tap="closeIntroDrawer">
          <image :src="imgUrl + '/qdkbm/newimage/fhui/icon-close.png'" class="close-icon"></image>
        </view>
      </view>
      <scroll-view class="drawer-content" scroll-y>
        <view class="drawer-intro">
          <view class="intro-content">
            <text>{{ schoolInfo.intro || '暂无简介' }}</text>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 招生专业抽屉 -->
    <view class="drawer-mask" v-if="majorDrawerVisible" @tap="closeMajorDrawer"></view>
    <view class="drawer" :class="{ 'drawer-show': majorDrawerVisible }">
      <view class="drawer-header">
        <view class="drawer-title">招生专业</view>
        <view class="drawer-close" @tap="closeMajorDrawer">
          <image :src="imgUrl + '/qdkbm/newimage/fhui/icon-close.png'" class="close-icon"></image>
        </view>
      </view>
      <scroll-view class="drawer-content" scroll-y>
        <view class="drawer-major-list">
          <view class="drawer-major-item" v-for="(item, index) in majorList" :key="index">
            <view class="drawer-major-name">{{ item.name }}</view>
            <view class="drawer-major-attrs">
              <view class="drawer-major-attr">
                <text class="drawer-attr-label">学制：</text>
                <text class="drawer-attr-value">{{ item.duration || '暂无' }}</text>
              </view>
              <view class="drawer-major-attr">
                <text class="drawer-attr-label">类型：</text>
                <text class="drawer-attr-value">{{ item.type || '暂无' }}</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loading: true,
      imgUrl: this.$base.uploadImgUrl,
      titleTop: 0,
      schoolId: '',
      schoolName: '', // 添加学校名称字段
      schoolInfo: {},
      majorList: [],
      scoreList: [],
      scoreYears: ['2023', '2022', '2021', '2020'],
      currentYear: '2023',
      // 新增抽屉相关数据
      showFullIntro: false,
      showAllMajors: false,
      introDrawerVisible: false,
      majorDrawerVisible: false
    };
  },
  onLoad(options) {
    // 获取状态栏高度
    let res = uni.getMenuButtonBoundingClientRect();
    this.titleTop = res.top;
    
    if (options.id && options.name) {
      this.schoolId = options.id;
      // 修复多次转码问题
      this.schoolName = decodeURIComponent(options.name);
      this.getSchoolDetail();
    } else {
      uni.showToast({
        title: '学校信息不完整',
        icon: 'none'
      });
      setTimeout(() => {
        this.goBack();
      }, 1500);
    }
  },
  onReady() {
    let res = uni.getMenuButtonBoundingClientRect();
    this.titleTop = res.top;
  },
  computed: {
    // 计算显示的专业列表（只显示前3个）
    displayedMajorList() {
      if (this.showAllMajors) {
        return this.majorList;
      } else {
        return this.majorList.slice(0, 3);
      }
    }
  },
  methods: {
    // 获取学校详情
    getSchoolDetail() {
      this.loading = true;
      
      // 构建请求参数
      const params = {
        appkey: '257KUWTX6ZUES8994QLLN5Z5PPLDTHV5', // 替换为实际的 APPKEY
        keywords: this.schoolName, // 使用学校名称而不是ID
        keywordstrict: true
      };
      
      // 发起请求
      uni.request({
        url: 'https://api.gugudata.com/location/college',
        method: 'GET',
        data: params,
        success: (res) => {
          this.loading = false;
          
          if (res.statusCode === 200 && res.data && res.data.DataStatus && res.data.DataStatus.StatusCode === 100) {
            // 处理返回的数据
            const collegeData = res.data.Data && res.data.Data.length > 0 ? res.data.Data[0] : null;
            
            if (collegeData) {
              // 设置学校基本信息
              this.schoolInfo = {
                id: collegeData.DataId,
                name: collegeData.CollegeName,
                province: collegeData.Province,
                city: collegeData.City,
                is985: collegeData.Is985,
                is211: collegeData.Is211,
                isDoubleFirst: collegeData.IsDualClass,
                educationLevel: collegeData.EduLevel,
                collegeProperty: collegeData.CollegeProperty,
                type: collegeData.CollegeCategory,
                rank: collegeData.Ranking,
                belong: collegeData.CollegeProperty,
                intro: collegeData.Intro,
                website: collegeData.WebSite,
                phone: collegeData.CallNumber,
                address: collegeData.Address,
                coverImage: collegeData.CoverImage
              };
              
              // 处理专业列表
              if (collegeData.MajorList && collegeData.MajorList.length > 0) {
                this.majorList = [];
                
                // 将所有专业分类的专业扁平化处理
                collegeData.MajorList.forEach(majorCategory => {
                  if (majorCategory.Majors && majorCategory.Majors.length > 0) {
                    majorCategory.Majors.forEach(majorName => {
                      this.majorList.push({
                        name: majorName,
                        type: majorCategory.MajorTitle,
                        duration: '四年' // 假设所有专业学制为四年
                      });
                    });
                  }
                });
              }
            } else {
              uni.showToast({
                title: '未找到学校详情',
                icon: 'none'
              });
            }
          } else {
            uni.showToast({
              title: res.data?.DataStatus?.StatusDescription || '获取学校详情失败',
              icon: 'none'
            });
          }
        },
        fail: (err) => {
          this.loading = false;
          uni.showToast({
            title: '网络异常，请稍后重试',
            icon: 'none'
          });
          console.error('请求失败:', err);
        }
      });
    },
    
    // 设置分数线年份
    setScoreYear(year) {
      this.currentYear = year;
      // 在实际应用中，这里应该重新请求对应年份的分数线数据
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 获取学校名称首字母
    getSchoolInitial(name) {
      if (!name) return '';
      const firstChar = name.charAt(0);
      const pinyinRegex = /[a-zA-Z]/;
      
      if (pinyinRegex.test(firstChar)) {
        return firstChar.toUpperCase();
      } else {
        // 简单处理，实际项目中可能需要更复杂的汉字转拼音
        return firstChar;
      }
    },
    
    // 打开学校简介抽屉
    openIntroDrawer() {
      this.introDrawerVisible = true;
      this.showFullIntro = true;
    },
    
    // 关闭学校简介抽屉
    closeIntroDrawer() {
      this.introDrawerVisible = false;
      this.showFullIntro = false;
    },
    
    // 打开招生专业抽屉
    openMajorDrawer() {
      this.majorDrawerVisible = true;
      this.showAllMajors = true;
    },
    
    // 关闭招生专业抽屉
    closeMajorDrawer() {
      this.majorDrawerVisible = false;
      this.showAllMajors = false;
    }
  }
};
</script>

<style lang="scss">
/* 全局样式 */
page {
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

/* 加载动画 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-circle {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid rgba(255, 137, 24, 0.2);
  border-top-color: #FF8918;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 头部区域 */
.header {
  background: linear-gradient(135deg, #FF8918, #FFC107);
  padding: 20rpx 40rpx 40rpx;
  color: #fff;
  position: relative;
}

.back-btn {
  position: fixed;
  left: 30rpx;
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  box-shadow: none;
  backdrop-filter: blur(0px);
  
  image {
    width: 30rpx;
    height: 30rpx;
  }
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  margin-top: 30rpx;
}

.page-title {
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
}

.school-basic-info {
  display: flex;
  align-items: center;
  margin-top: 30rpx;
}

.school-logo {
  width: 120rpx;
  height: 120rpx;
  margin-right: 30rpx;
}

.logo-placeholder {
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 48rpx;
  font-weight: bold;
}

.school-name-container {
  flex: 1;
}

.school-name {
  font-size: 34rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
}

.school-tags {
  display: flex;
  margin-left: 20rpx;
}

.tag {
  font-size: 20rpx;
  padding: 4rpx 10rpx;
  border-radius: 6rpx;
  margin-right: 10rpx;
  font-weight: normal;
}

.tag-985 {
  background-color: rgba(230, 67, 64, 0.8);
  color: #fff;
}

.tag-211 {
  background-color: rgba(26, 173, 25, 0.8);
  color: #fff;
}

.tag-double {
  background-color: rgba(87, 107, 149, 0.8);
  color: #fff;
}

.tag-edu {
  background-color: rgba(114, 46, 209, 0.8);
  color: #fff;
}

.tag-property {
  background-color: rgba(235, 47, 150, 0.8);
  color: #fff;
}

.school-location {
  font-size: 28rpx;
  opacity: 0.9;
  display: flex;
  align-items: center;
}

.location-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
}

/* 主内容区 */
.main-content {
  padding: 30rpx 40rpx;
}

/* 信息卡片 */
.info-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: #FF8918;
  border-radius: 4rpx;
}

/* 学校概览 */
.info-grid {
  display: flex;
  flex-wrap: wrap;
}

.info-item {
  width: 50%;
  margin-bottom: 20rpx;
}

.item-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 6rpx;
}

.item-value {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

/* 学校简介 */
.school-intro {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 查看更多按钮 */
.more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20rpx;
  padding: 10rpx 0;
  color: #FF8918;
  font-size: 28rpx;
}

.more-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: 8rpx;
}

/* 招生专业 */
.major-list {
  margin-top: 20rpx;
}

.major-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.major-item:last-child {
  border-bottom: none;
}

.major-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.major-attrs {
  display: flex;
}

.major-attr {
  font-size: 26rpx;
  color: #666;
  margin-right: 30rpx;
  display: flex;
}

.attr-label {
  color: #999;
}

/* 录取分数线 */
.score-filter {
  display: flex;
  margin-bottom: 20rpx;
}

.filter-item {
  padding: 10rpx 20rpx;
  font-size: 26rpx;
  color: #666;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  margin-right: 20rpx;
}

.filter-item.active {
  background-color: #FF8918;
  color: #fff;
}

.score-table {
  border: 1rpx solid #eee;
  border-radius: 10rpx;
  overflow: hidden;
}

.table-header {
  display: flex;
  background-color: #f8f8f8;
}

.th {
  flex: 1;
  padding: 20rpx 10rpx;
  font-size: 26rpx;
  color: #333;
  text-align: center;
  font-weight: bold;
}

.table-row {
  display: flex;
  border-top: 1rpx solid #eee;
}

.td {
  flex: 1;
  padding: 20rpx 10rpx;
  font-size: 26rpx;
  color: #666;
  text-align: center;
}

.td.score {
  color: #FF8918;
  font-weight: bold;
}

/* 联系方式 */
.contact-list {
  margin-top: 20rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-icon image {
  width: 40rpx;
  height: 40rpx;
}

.contact-info {
  flex: 1;
}

.contact-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 6rpx;
}

.contact-value {
  font-size: 28rpx;
  color: #333;
}

/* 空状态提示 */
.empty-tip {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}
.school-cover-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  object-fit: cover;
  border: 4rpx solid #FF8918;
  background: #fff;
  box-shadow: 0 2rpx 12rpx rgba(255,137,24,0.08);
  display: block;
  margin: 0 auto;
}

/* 抽屉样式 */
.drawer-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 998;
}

.drawer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 70vh;
  background-color: #fff;
  border-radius: 30rpx 30rpx 0 0;
  z-index: 999;
  transform: translateY(100%);
  transition: transform 0.3s ease-out;
}

.drawer-show {
  transform: translateY(0);
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: linear-gradient(to right, #FF8918, #FFC107);
  color: #fff;
}

.drawer-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.drawer-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  width: 32rpx;
  height: 32rpx;
  filter: brightness(10);
}

.drawer-content {
  height: calc(70vh - 90rpx);
  padding: 0;
  background-color: #f8f8f8;
}

.drawer-intro {
  padding: 30rpx 0;
}

.intro-content {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx 40rpx;
  margin: 0 30rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.8;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.drawer-major-list {
  padding: 30rpx 30rpx 50rpx;
}

.drawer-major-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.drawer-major-item:last-child {
  margin-bottom: 0;
}

.drawer-major-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
  border-left: 6rpx solid #FF8918;
  padding-left: 16rpx;
}

.drawer-major-attrs {
  display: flex;
  flex-wrap: wrap;
}

.drawer-major-attr {
  font-size: 26rpx;
  color: #666;
  margin-right: 30rpx;
  margin-bottom: 8rpx;
  display: flex;
}

.drawer-attr-label {
  color: #999;
}

.drawer-attr-value {
  color: #333;
}
</style>
