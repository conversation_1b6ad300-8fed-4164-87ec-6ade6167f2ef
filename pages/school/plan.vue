<template>
	<view class="plan-container">
		<!-- 头部 -->
		<view class="header" :style="{ 'padding-top': titleTop + 'px' }">
			<view class="back-btn" @tap="back">
				<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-back.png'" mode="widthFix"></image>
			</view>
			<view class="title">招生计划</view>
			<view class="right-placeholder"></view>
		</view>

		<!-- 选项卡 -->
		<view class="tabs">
			<view class="tab" :class="{ active: activeTab === 'school' }" @tap="switchTab('school')">
				按学校查询
			</view>
			<view class="tab" :class="{ active: activeTab === 'major' }" @tap="switchTab('major')">
				按专业查询
			</view>
		</view>

		<!-- 搜索框 -->
		<view class="search-box">
			<view class="search-input">
				<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-search.png'" class="search-icon"></image>
				<input
					type="text"
					v-model="searchKeyword"
					:placeholder="activeTab === 'school' ? '搜索学校招生计划' : '搜索专业招生计划'"
					confirm-type="search"
					@confirm="handleSearch"
				/>
				<view class="clear-btn" v-if="searchKeyword" @tap="clearSearch">
					<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-clear.png'"></image>
				</view>
			</view>
			<button class="search-button" @tap="handleSearch">搜索</button>
		</view>

		<!-- 筛选条件 -->
		<view class="filter-section">
			<view class="filter-item" @tap="openYearPicker">
				<text>{{ selectedYear }}</text>
				<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-arrow-down.png'" class="arrow-icon"></image>
			</view>
			<view class="filter-separator"></view>
			<view class="filter-item" @tap="openLocationPicker">
				<text>{{ selectedLocation || '院校位置' }}</text>
				<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-arrow-down.png'" class="arrow-icon"></image>
			</view>
			<view class="filter-separator"></view>
			<view class="filter-item" @tap="openLevelPicker">
				<text>{{ selectedLevel || '院校层次' }}</text>
				<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-arrow-down.png'" class="arrow-icon"></image>
			</view>
			<view class="filter-separator"></view>
			<view class="filter-item" @tap="openMoreFilters">
				<text>筛选</text>
				<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-filter.png'" class="filter-icon"></image>
			</view>
		</view>

		<!-- 按学校查询内容 -->
		<scroll-view
			v-if="activeTab === 'school'"
			scroll-y
			class="plan-list"
			refresher-enabled
			:refresher-triggered="refreshing"
			@refresherrefresh="onRefresh"
			@scrolltolower="onLoadMore"
		>
			<view v-if="schoolList.length > 0">
				<view class="school-item" v-for="(item, index) in schoolList" :key="index" @tap="viewSchoolDetail(item)">
					<view class="school-logo">
						<image :src="item.logo || imgUrl + '/qdkbm/newimage/fhui/default-school.png'"></image>
					</view>
					<view class="school-info">
						<view class="school-name">{{ item.name }}</view>
						<view class="school-meta">{{ item.location }} | {{ item.type }}</view>
						<view class="school-plan">
							<text class="plan-label">24年计划</text>
							<text class="plan-count">{{ item.planCount }}人</text>
							<text class="major-count">招生专业 {{ item.majorCount }}个</text>
						</view>
					</view>
					<view class="arrow-right">
						<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-right.png'"></image>
					</view>
				</view>
				<view class="load-more" v-if="hasMore && !loading">
					<text>上拉加载更多</text>
				</view>
				<view class="load-more" v-if="loading">
					<text>加载中...</text>
				</view>
				<view class="no-more" v-if="!hasMore && schoolList.length > 0">
					<text>没有更多数据了</text>
				</view>
			</view>
			<view class="empty-state" v-else-if="!loading">
				<image :src="imgUrl + '/qdkbm/newimage/fhui/empty-search.png'" mode="widthFix"></image>
				<text>没有找到符合条件的招生计划</text>
			</view>
		</scroll-view>

		<!-- 按专业查询内容 -->
		<scroll-view
			v-if="activeTab === 'major'"
			scroll-y
			class="plan-list"
			refresher-enabled
			:refresher-triggered="refreshing"
			@refresherrefresh="onRefresh"
			@scrolltolower="onLoadMore"
		>
			<view v-if="majorList.length > 0">
				<view class="major-item" v-for="(item, index) in majorList" :key="index" @tap="viewMajorDetail(item)">
					<view class="major-name">{{ item.name }}</view>
					<view class="major-info">
						<view class="major-meta">专业类别：{{ item.category }}</view>
						<view class="major-plan">
							<text class="plan-label">24年计划</text>
							<text class="plan-count">{{ item.planCount }}人</text>
							<text class="school-count">招生院校 {{ item.schoolCount }}所</text>
						</view>
					</view>
					<view class="arrow-right">
						<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-right.png'"></image>
					</view>
				</view>
				<view class="load-more" v-if="hasMore && !loading">
					<text>上拉加载更多</text>
				</view>
				<view class="load-more" v-if="loading">
					<text>加载中...</text>
				</view>
				<view class="no-more" v-if="!hasMore && majorList.length > 0">
					<text>没有更多数据了</text>
				</view>
			</view>
			<view class="empty-state" v-else-if="!loading">
				<image :src="imgUrl + '/qdkbm/newimage/fhui/empty-search.png'" mode="widthFix"></image>
				<text>没有找到符合条件的招生计划</text>
			</view>
		</scroll-view>

		<!-- 年份选择器 -->
		<u-popup :show="showYearPicker" mode="bottom" @close="closeYearPicker">
			<view class="picker-container">
				<view class="picker-header">
					<text class="picker-title">选择年份</text>
					<view class="picker-close" @tap="closeYearPicker">
						<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-close.png'"></image>
					</view>
				</view>
				<scroll-view scroll-y class="picker-content">
					<view
						class="picker-item"
						v-for="(year, index) in yearOptions"
						:key="index"
						:class="{ active: selectedYear === year }"
						@tap="selectYear(year)"
					>
						{{ year }}
					</view>
				</scroll-view>
			</view>
		</u-popup>

		<!-- 位置选择器 -->
		<u-popup
			:show="showLocationPicker"
			mode="bottom"
			@close="closeLocationPicker"
			:closeOnClickOverlay="true"
			:safeAreaInsetBottom="true"
		>
			<view class="picker-container">
				<view class="picker-header">
					<text class="picker-title">选择位置</text>
					<view class="picker-close" @tap="closeLocationPicker">
						<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-close.png'"></image>
					</view>
				</view>
				<scroll-view scroll-y class="picker-content">
					<view
						class="picker-item"
						@tap="selectLocation('')"
						:class="{ active: selectedLocation === '' }"
					>
						全部
					</view>
					<view
						class="picker-item"
						v-for="(location, index) in locationOptions"
						:key="index"
						:class="{ active: selectedLocation === location }"
						@tap="selectLocation(location)"
					>
						{{ location }}
					</view>
				</scroll-view>
			</view>
		</u-popup>

		<!-- 层次选择器 -->
		<u-popup :show="showLevelPicker" mode="bottom" @close="closeLevelPicker">
			<view class="picker-container">
				<view class="picker-header">
					<text class="picker-title">选择层次</text>
					<view class="picker-close" @tap="closeLevelPicker">
						<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-close.png'"></image>
					</view>
				</view>
				<scroll-view scroll-y class="picker-content">
					<view
						class="picker-item"
						@tap="selectLevel('')"
						:class="{ active: selectedLevel === '' }"
					>
						全部
					</view>
					<view
						class="picker-item"
						v-for="(level, index) in levelOptions"
						:key="index"
						:class="{ active: selectedLevel === level }"
						@tap="selectLevel(level)"
					>
						{{ level }}
					</view>
				</scroll-view>
			</view>
		</u-popup>

		<!-- 更多筛选 -->
		<u-popup :show="showMoreFilters" mode="bottom" @close="closeMoreFilters">
			<view class="more-filters-container">
				<view class="picker-header">
					<text class="picker-title">更多筛选</text>
					<view class="picker-close" @tap="closeMoreFilters">
						<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-close.png'"></image>
					</view>
				</view>
				<scroll-view scroll-y class="filters-scroll">
					<view class="filter-group">
						<view class="filter-group-title">院校类型</view>
						<view class="filter-group-content">
							<view
								class="filter-tag"
								v-for="(type, index) in schoolTypes"
								:key="index"
								:class="{ active: selectedType === type }"
								@tap="selectType(type)"
							>
								{{ type }}
							</view>
						</view>
					</view>
					<view class="filter-group" v-if="activeTab === 'major'">
						<view class="filter-group-title">专业类别</view>
						<view class="filter-group-content">
							<view
								class="filter-tag"
								v-for="(category, index) in majorCategories"
								:key="index"
								:class="{ active: selectedCategory === category }"
								@tap="selectCategory(category)"
							>
								{{ category }}
							</view>
						</view>
					</view>
				</scroll-view>
				<view class="filter-actions">
					<button class="reset-btn" @tap="resetFilters">重置</button>
					<button class="confirm-btn" @tap="applyFilters">确认</button>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
import apiConfig from '@/config/api-config.js'
import regionConfig from '@/config/region-config.js'

export default {
	data() {
		return {
			titleTop: 0,
			imgUrl: this.$base.uploadImgUrl,
			activeTab: 'school',
			searchKeyword: '',
			selectedYear: '2024年',
			selectedLocation: '',
			selectedLevel: '',
			selectedType: '',
			selectedCategory: '',

			// 筛选器显示控制
			showYearPicker: false,
			showLocationPicker: false,
			showLevelPicker: false,
			showMoreFilters: false,

			yearOptions: ['2024年', '2023年', '2022年', '2021年'],
			locationOptions: regionConfig.provinces,
			levelOptions: ['本科', '专科'],
			schoolTypes: ['综合', '理工', '师范', '农林', '医药', '财经', '政法', '民族', '语言', '艺术', '体育'],
			majorCategories: ['文学', '理学', '工学', '经济学', '管理学', '医学', '法学', '教育学', '历史学', '农学', '哲学', '艺术学'],

			schoolList: [],
			majorList: [],

			loading: false,
			refreshing: false,
			page: 1,
			pageSize: 10,
			hasMore: true,

			// GuguData API 信息
			guguApiUrl: apiConfig.guguData.baseUrl + apiConfig.guguData.endpoints.enrollmentPlan,
			appkey: apiConfig.guguData.appkey
		}
	},
	onLoad() {
		// 获取胶囊按钮位置信息，用于设置顶部安全距离
		let res = uni.getMenuButtonBoundingClientRect();
		this.titleTop = res.top;

		// 设置默认查询安徽的数据
		this.selectedLocation = '安徽';

		// 初始加载数据
		this.loadData();
	},
	methods: {
		// 切换标签页
		switchTab(tab) {
			this.activeTab = tab;
			this.page = 1;
			this.hasMore = true;
			this.schoolList = [];
			this.majorList = [];
			this.loadData();
		},

		// 搜索处理
		handleSearch() {
			this.page = 1;
			this.hasMore = true;
			this.schoolList = [];
			this.majorList = [];
			this.loadData();
		},

		// 清除搜索关键词
		clearSearch() {
			this.searchKeyword = '';
			this.page = 1;
			this.hasMore = true;
			this.schoolList = [];
			this.majorList = [];
			this.loadData();
		},

		// 下拉刷新
		onRefresh() {
			this.refreshing = true;
			this.page = 1;
			this.hasMore = true;
			this.schoolList = [];
			this.majorList = [];
			this.loadData().finally(() => {
				this.refreshing = false;
			});
		},

		// 上拉加载更多
		onLoadMore() {
			if (!this.loading && this.hasMore) {
				this.loadData();
			}
		},

		// 构建API请求参数
		buildApiParams() {
			const params = {
				appkey: this.appkey,
				pageindex: this.page,
				pagesize: this.pageSize
			};

			// 提取年份数字
			if (this.selectedYear !== '全部') {
				const yearMatch = this.selectedYear.match(/\d+/);
				if (yearMatch) {
					params.year = parseInt(yearMatch[0]);
				}
			}

			// 按学校查询
			if (this.activeTab === 'school') {
				if (this.searchKeyword) {
					params.schoolname = this.searchKeyword;
				}

				// 筛选院校位置
				if (this.selectedLocation) {
					params.provincename = this.selectedLocation;
				}

				// 筛选院校类型
				if (this.selectedType) {
					// 学校类型和API参数的映射可能需要调整
					params.type = this.mapSchoolTypeToApiParam(this.selectedType);
				}

				// 筛选院校层次 (本科/专科)
				if (this.selectedLevel) {
					params.batchname = this.selectedLevel === '本科' ?
						'本科批' : this.selectedLevel === '专科' ? '专科批' : '';
				}
			}
			// 按专业查询
			else {
				if (this.searchKeyword) {
					params.collegemajorname = this.searchKeyword;
				}

				// 筛选专业类别
				if (this.selectedCategory) {
					params.classone = this.selectedCategory;
				}

				// 筛选院校层次 (本科/专科)
				if (this.selectedLevel) {
					params.batchname = this.selectedLevel === '本科' ?
						'本科批' : this.selectedLevel === '专科' ? '专科批' : '';
				}
			}

			return params;
		},

		// 学校类型映射到API参数
		mapSchoolTypeToApiParam(schoolType) {
			// 根据实际情况映射学校类型到API参数
			const typeMap = {
				'综合': '综合',
				'理工': '理科',
				'文学': '文科',
				'艺术': '艺术类',
				'体育': '体育类'
			};

			return typeMap[schoolType] || '';
		},

		// 加载数据
		loadData() {
			if (this.loading || !this.hasMore) return Promise.resolve();

			this.loading = true;

			const params = this.buildApiParams();

			if (!this.refreshing) {
				uni.showLoading({
					title: '加载中...'
				});
			}

			return new Promise((resolve, reject) => {
				// 调用 GuguData API 获取数据
				uni.request({
					url: this.guguApiUrl,
					data: params,
					method: 'GET',
					success: (res) => {
						if (!this.refreshing) {
							uni.hideLoading();
						}

						if (res.statusCode === 200 && res.data.DataStatus && res.data.DataStatus.StatusCode === 100) {
							const apiData = res.data.Data || [];
							const totalCount = res.data.DataStatus.DataTotalCount || 0;

							console.log('API数据:', apiData);
							console.log('总数据量:', totalCount);
							console.log('当前标签页:', this.activeTab);

							// 处理数据
							if (this.activeTab === 'school') {
								this.processSchoolData(apiData);
								console.log('处理后的学校列表:', this.schoolList);
							} else {
								this.processMajorData(apiData);
								console.log('处理后的专业列表:', this.majorList);
							}

							// 判断是否有更多数据
							this.hasMore = this.page * this.pageSize < totalCount;

							// 增加页码
							if (this.hasMore) {
								this.page++;
							}

							resolve();
						} else {
							console.error('API响应错误:', res);
							uni.showToast({
								title: '获取数据失败',
								icon: 'none'
							});
							reject(new Error('获取数据失败'));
						}

						this.loading = false;
					},
					fail: (err) => {
						console.error('API请求失败:', err);
						if (!this.refreshing) {
							uni.hideLoading();
						}
						uni.showToast({
							title: '网络请求失败',
							icon: 'none'
						});
						this.loading = false;
						reject(err);
					}
				});
			});
		},

		// 处理学校数据
		processSchoolData(apiData) {
			// 按学校名称分组数据
			const schoolGroups = {};

			apiData.forEach(item => {
				const schoolName = item.SchoolName;

				if (!schoolGroups[schoolName]) {
					schoolGroups[schoolName] = {
						id: item.SchoolUUID || this.generateUUID(),
						name: schoolName,
						logo: '', // API 没有提供 logo
						location: item.ProvinceName ? `${item.ProvinceName}` : '未知',
						type: item.Type || '未知',
						planCount: 0,
						majorCount: 0,
						majors: new Set()
					};
				}

				// 统计学校招生人数和专业数量
				schoolGroups[schoolName].planCount += parseInt(item.EnrollmentNumbers || 0);
				schoolGroups[schoolName].majors.add(item.CollegeMajorName);
			});

			// 转换为数组并计算专业数量
			const schools = Object.values(schoolGroups).map(school => {
				school.majorCount = school.majors.size;
				delete school.majors; // 不需要在列表中显示专业详情
				return school;
			});

			// 追加到当前列表
			this.schoolList = [...this.schoolList, ...schools];
		},

		// 处理专业数据
		processMajorData(apiData) {
			// 按专业名称分组数据
			const majorGroups = {};

			apiData.forEach(item => {
				const majorName = item.CollegeMajorName;

				if (!majorGroups[majorName]) {
					majorGroups[majorName] = {
						id: item.CollegeMajorCode || this.generateUUID(),
						name: majorName,
						category: `${item.ClassOne || '未知'}-${item.ClassTwo || '未知'}`,
						planCount: 0,
						schoolCount: 0,
						schools: new Set()
					};
				}

				// 统计专业招生人数和学校数量
				majorGroups[majorName].planCount += parseInt(item.EnrollmentNumbers || 0);
				majorGroups[majorName].schools.add(item.SchoolName);
			});

			// 转换为数组并计算学校数量
			const majors = Object.values(majorGroups).map(major => {
				major.schoolCount = major.schools.size;
				delete major.schools; // 不需要在列表中显示学校详情
				return major;
			});

			// 追加到当前列表
			this.majorList = [...this.majorList, ...majors];
		},

		// 生成唯一ID
		generateUUID() {
			return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
				var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
				return v.toString(16);
			});
		},

		// 查看学校详情
		viewSchoolDetail(school) {
			uni.navigateTo({
				url: `/pages/school/plan-detail?id=${school.id}&type=school&name=${encodeURIComponent(school.name)}`
			});
		},

		// 查看专业详情
		viewMajorDetail(major) {
			uni.navigateTo({
				url: `/pages/school/plan-detail?id=${major.id}&type=major&name=${encodeURIComponent(major.name)}`
			});
		},

		// 显示年份选择器
		openYearPicker() {
			this.showYearPicker = true;
		},

		// 关闭年份选择器
		closeYearPicker() {
			this.showYearPicker = false;
		},

		// 选择年份
		selectYear(year) {
			this.selectedYear = year;
			this.closeYearPicker();
			this.page = 1;
			this.hasMore = true;
			this.schoolList = [];
			this.majorList = [];
			this.loadData();
		},

		// 显示位置选择器
		openLocationPicker() {
			this.showLocationPicker = true;
		},

		// 关闭位置选择器
		closeLocationPicker() {
			this.showLocationPicker = false;
		},

		// 选择位置
		selectLocation(location) {
			this.selectedLocation = location;
			this.closeLocationPicker();
			this.page = 1;
			this.hasMore = true;
			this.schoolList = [];
			this.majorList = [];
			this.loadData();
		},

		// 显示层次选择器
		openLevelPicker() {
			this.showLevelPicker = true;
		},

		// 关闭层次选择器
		closeLevelPicker() {
			this.showLevelPicker = false;
		},

		// 选择层次
		selectLevel(level) {
			this.selectedLevel = level;
			this.closeLevelPicker();
			this.page = 1;
			this.hasMore = true;
			this.schoolList = [];
			this.majorList = [];
			this.loadData();
		},

		// 显示更多筛选
		openMoreFilters() {
			this.showMoreFilters = true;
		},

		// 关闭更多筛选
		closeMoreFilters() {
			this.showMoreFilters = false;
		},

		// 选择院校类型
		selectType(type) {
			this.selectedType = type === this.selectedType ? '' : type;
		},

		// 选择专业类别
		selectCategory(category) {
			this.selectedCategory = category === this.selectedCategory ? '' : category;
		},

		// 重置筛选
		resetFilters() {
			this.selectedType = '';
			this.selectedCategory = '';
		},

		// 应用筛选
		applyFilters() {
			this.closeMoreFilters();
			this.page = 1;
			this.hasMore = true;
			this.schoolList = [];
			this.majorList = [];
			this.loadData();
		},

		// 返回上一页
		back() {
			uni.navigateBack({
				delta: 1
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.plan-container {
	background-color: #f5f7fa;
	min-height: 100vh;
	padding-bottom: 20rpx;
}

.header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 40rpx;
	background-color: #fff;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;

		image {
			width: 40rpx;
			height: 40rpx;
		}
	}

	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}

	.right-placeholder {
		width: 60rpx;
	}
}

.tabs {
	display: flex;
	height: 80rpx;
	background-color: #fff;
	margin-top: 2rpx;

	.tab {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 30rpx;
		color: #666;
		position: relative;

		&.active {
			color: #ff8918;
			font-weight: bold;

			&:after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 60rpx;
				height: 6rpx;
				background-color: #ff8918;
				border-radius: 6rpx;
			}
		}
	}
}

.search-box {
	display: flex;
	padding: 20rpx;
	background-color: #fff;
	border-bottom: 1rpx solid #eee;

	.search-input {
		flex: 1;
		height: 70rpx;
		background-color: #f5f7fa;
		border-radius: 70rpx;
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		margin-right: 20rpx;

		.search-icon {
			width: 32rpx;
			height: 32rpx;
			margin-right: 10rpx;
		}

		input {
			flex: 1;
			height: 100%;
			font-size: 28rpx;
		}

		.clear-btn {
			width: 40rpx;
			height: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			image {
				width: 28rpx;
				height: 28rpx;
			}
		}
	}

	.search-button {
		width: 120rpx;
		height: 70rpx;
		line-height: 70rpx;
		background-color: #ff8918;
		color: #fff;
		font-size: 28rpx;
		border-radius: 70rpx;
		padding: 0;
	}
}

.filter-section {
	display: flex;
	align-items: center;
	height: 80rpx;
	background-color: #fff;
	padding: 0 20rpx;
	border-bottom: 1rpx solid #eee;

	.filter-item {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 26rpx;
		color: #666;

		text {
			max-width: 160rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.arrow-icon {
			width: 24rpx;
			height: 24rpx;
			margin-left: 8rpx;
		}

		.filter-icon {
			width: 28rpx;
			height: 28rpx;
			margin-left: 8rpx;
		}
	}

	.filter-separator {
		width: 2rpx;
		height: 30rpx;
		background-color: #eee;
	}
}

.plan-list {
	height: calc(100vh - 300rpx);

	.school-item, .major-item {
		display: flex;
		padding: 30rpx 40rpx;
		background-color: #fff;
		margin-bottom: 2rpx;

		.school-logo {
			width: 80rpx;
			height: 80rpx;
			margin-right: 20rpx;

			image {
				width: 100%;
				height: 100%;
				border-radius: 10rpx;
			}
		}

		.school-info, .major-info {
			flex: 1;
		}

		.school-name, .major-name {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 10rpx;
		}

		.school-meta, .major-meta {
			font-size: 24rpx;
			color: #999;
			margin-bottom: 10rpx;
		}

		.school-plan, .major-plan {
			display: flex;
			align-items: center;
			font-size: 24rpx;

			.plan-label {
				color: #666;
			}

			.plan-count {
				color: #ff8918;
				font-weight: bold;
				margin: 0 20rpx;
			}

			.major-count, .school-count {
				color: #666;
			}
		}

		.arrow-right {
			display: flex;
			align-items: center;

			image {
				width: 32rpx;
				height: 32rpx;
			}
		}
	}

	.major-item {
		flex-direction: column;

		.major-name {
			margin-bottom: 20rpx;
		}

		.major-info {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.arrow-right {
			position: absolute;
			right: 40rpx;
			top: 50%;
			transform: translateY(-50%);
		}
	}
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;

	image {
		width: 200rpx;
		margin-bottom: 30rpx;
	}

	text {
		font-size: 28rpx;
		color: #999;
	}
}

.load-more, .no-more {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 80rpx;
	font-size: 26rpx;
	color: #999;
}

.picker-container {
	background-color: #fff;
	border-radius: 20rpx 20rpx 0 0;
	overflow: hidden;

	.picker-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx;
		border-bottom: 1rpx solid #eee;

		.picker-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}

		.picker-close {
			width: 40rpx;
			height: 40rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}
	}

	.picker-content {
		height: 60vh;
		padding: 20rpx 30rpx;

		.picker-item {
			height: 80rpx;
			line-height: 80rpx;
			font-size: 28rpx;
			color: #333;
			border-bottom: 1rpx solid #f5f5f5;

			&.active {
				color: #ff8918;
				font-weight: bold;
			}
		}
	}
}

.more-filters-container {
	background-color: #fff;
	border-radius: 20rpx 20rpx 0 0;
	overflow: hidden;

	.filters-scroll {
		max-height: 60vh;
		padding: 0 30rpx;
	}

	.filter-group {
		padding: 30rpx 0;
		border-bottom: 1rpx solid #f5f5f5;

		.filter-group-title {
			font-size: 30rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 30rpx;
		}

		.filter-group-content {
			display: flex;
			flex-wrap: wrap;

			.filter-tag {
				height: 60rpx;
				line-height: 60rpx;
				padding: 0 30rpx;
				background-color: #f5f7fa;
				border-radius: 60rpx;
				font-size: 26rpx;
				color: #666;
				margin-right: 20rpx;
				margin-bottom: 20rpx;

				&.active {
					background-color: #fff3e7;
					color: #ff8918;
					border: 1rpx solid #ff8918;
				}
			}
		}
	}

	.filter-actions {
		display: flex;
		padding: 30rpx;

		button {
			flex: 1;
			height: 80rpx;
			line-height: 80rpx;
			font-size: 30rpx;
			border-radius: 80rpx;

			&.reset-btn {
				background-color: #f5f7fa;
				color: #666;
				margin-right: 20rpx;
			}

			&.confirm-btn {
				background-color: #ff8918;
				color: #fff;
			}
		}
	}
}
</style>