<template>
  <view class="school-container" :class="{'no-scroll': showFilterPanel}">
    <!-- 加载动画 -->
    <view class="loading-container" v-if="loading">
      <view class="loading-spinner">
        <view class="loading-circle"></view>
        <view class="loading-text">加载中</view>
      </view>
    </view>

    <!-- 固定头部和搜索区 -->
    <view class="fixed-header-area">
      <!-- 现代化头部设计 -->
      <view class="header" :style="{ 'padding-top': titleTop + 'px' }">
        <view class="header-content">
          <view class="title-container">
            <text class="title">查大学</text>
            <text class="subtitle">高校信息一手掌握</text>
          </view>
          <view class="header-icon">
            <image
              :src="imgUrl + '/qdkbm/newimage/fhui/school-icon.png'"
              mode="widthFix"
            ></image>
          </view>
        </view>
      </view>
      
      <!-- 搜索和筛选区域 -->
      <view class="search-section">
        <view class="search-box">
          <view class="search-input">
            <image :src="imgUrl + '/qdkbm/newimage/fhui/icon-search.png'" class="search-icon"></image>
            <input 
              type="text" 
              v-model="searchKeyword" 
              placeholder="输入学校名称、省份或专业" 
              confirm-type="search"
              @confirm="searchSchools"
            />
            <view class="clear-btn" v-if="searchKeyword" @tap="clearSearch">
              <image :src="imgUrl + '/qdkbm/newimage/fhui/icon-clear.png'"></image>
            </view>
          </view>
          <button class="search-button" @tap="searchSchools">搜索</button>
        </view>
        
        
      </view>
    </view>

    <!-- 主要内容区 -->
    <view class="main-content">
      <!-- 筛选条件区域 -->
      <view class="filter-section">
        <!-- 位置筛选 -->
        <view class="filter-group" @tap="toggleFilterPanel('location')">
          <text class="filter-label">位置</text>
          <text class="filter-value">{{ selectedProvince ? selectedProvince.name : '全部' }}</text>
          <image :src="imgUrl + '/qdkbm/newimage/fhui/icon-arrow-down.png'" class="filter-arrow"></image>
        </view>
        
        <!-- 类型筛选 -->
        <view class="filter-group" @tap="toggleFilterPanel('type')">
          <text class="filter-label">类型</text>
          <text class="filter-value">{{ getFilterText() }}</text>
          <image :src="imgUrl + '/qdkbm/newimage/fhui/icon-arrow-down.png'" class="filter-arrow"></image>
        </view>
        
        <!-- 排序筛选 -->
        <view class="filter-group" @tap="toggleFilterPanel('sort')">
          <text class="filter-label">排序</text>
          <text class="filter-value">{{ sortBy === 'rank' ? '排名' : '校名' }}</text>
          <image :src="imgUrl + '/qdkbm/newimage/fhui/icon-arrow-down.png'" class="filter-arrow"></image>
        </view>
      </view>
      
      <!-- 筛选面板遮罩层 -->
      <view class="filter-mask" v-if="showFilterPanel" @tap="closeFilterPanel"></view>
      
      <!-- 筛选面板抽屉 -->
      <view class="filter-panel" :class="{ 'show': showFilterPanel }">
        <!-- 关闭按钮 -->
        <view class="panel-close" @tap="closeFilterPanel">
          <image :src="imgUrl + '/qdkbm/newimage/fhui/icon-close.png'" mode="widthFix"></image>
        </view>
        
        <!-- 位置筛选面板 -->
        <view class="location-panel" v-if="currentFilterPanel === 'location'">
          <view class="panel-header">
            <text>选择省份</text>
          </view>
          <view class="province-grid">
            <view 
              class="province-item" 
              :class="{ active: !selectedProvince }"
              @tap="selectProvince(null)"
            >
              <text>全部</text>
            </view>
            <view 
              class="province-item" 
              v-for="(item, index) in provinces" 
              :key="index"
              :class="{ active: selectedProvince && selectedProvince.code === item.code }"
              @tap="selectProvince(item)"
            >
              <text>{{ item.name }}</text>
            </view>
          </view>
          <!-- 调试信息 -->
          <view class="debug-info" v-if="provinces.length === 0">
            <text>没有省份数据</text>
          </view>
        </view>
        
        <!-- 类型筛选面板 -->
        <view class="type-panel" v-if="currentFilterPanel === 'type'">
          <view class="panel-header">
            <text>选择类型</text>
          </view>
          <view class="type-grid">
            <view 
              class="type-item" 
              :class="{ active: currentFilter === 'all' }"
              @tap="setFilter('all')"
            >
              <text>全部</text>
            </view>
            <view 
              class="type-item" 
              :class="{ active: currentFilter === '985' }"
              @tap="setFilter('985')"
            >
              <text>985高校</text>
            </view>
            <view 
              class="type-item" 
              :class="{ active: currentFilter === '211' }"
              @tap="setFilter('211')"
            >
              <text>211高校</text>
            </view>
            <view 
              class="type-item" 
              :class="{ active: currentFilter === 'double' }"
              @tap="setFilter('double')"
            >
              <text>双一流</text>
            </view>
            <view 
              class="type-item" 
              :class="{ active: currentFilter === 'normal' }"
              @tap="setFilter('normal')"
            >
              <text>师范类</text>
            </view>
            <view 
              class="type-item" 
              :class="{ active: currentFilter === 'art' }"
              @tap="setFilter('art')"
            >
              <text>艺术类</text>
            </view>
          </view>
        </view>
        
        <!-- 排序筛选面板 -->
        <view class="sort-panel" v-if="currentFilterPanel === 'sort'">
          <view class="panel-header">
            <text>选择排序</text>
          </view>
          <view class="sort-grid">
            <view 
              class="sort-item" 
              :class="{ active: sortBy === 'rank' }"
              @tap="setSortBy('rank')"
            >
              <text>排名</text>
            </view>
            <view 
              class="sort-item" 
              :class="{ active: sortBy === 'name' }"
              @tap="setSortBy('name')"
            >
              <text>校名</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 学校列表 -->
      <scroll-view 
        class="school-list" 
        v-if="schoolList.length > 0"
        scroll-y="true"
        @scrolltolower="onScrollToLower"
        :style="{height: scrollViewHeight + 'px'}"
      >
        <view 
          class="school-card" 
          v-for="(item, index) in sortedSchoolList" 
          :key="index"
          @tap="viewSchoolDetail(item)"
        >
          <view class="school-logo">
            <image v-if="item.coverImage" :src="item.coverImage" mode="aspectFit"></image>
            <view class="logo-placeholder" v-else>
              <text>{{ getSchoolInitial(item.name) }}</text>
            </view>
          </view>
          <view class="school-info">
            <view class="school-name">
              {{ item.name }}
            </view>
            <view class="school-location">
              {{ item.province }} {{ item.city }} | {{ item.type || '普通本科' }}
            </view>
            <view class="school-tags">
              <view class="tag tag-985" v-if="item.is985">985</view>
              <view class="tag tag-211" v-if="item.is211">211</view>
              <view class="tag tag-double" v-if="item.isDoubleFirst">双一流</view>
              <view class="tag tag-edu" v-if="item.educationLevel">{{ item.educationLevel }}</view>
              <view class="tag tag-property" v-if="item.collegeProperty">{{ item.collegeProperty }}</view>
              <view class="tag tag-type">{{ item.type || '综合类' }}</view>
            </view>
          </view>
          <!-- 已移除问招办按钮 -->
        </view>
        <!-- 底部加载更多指示器 -->
        <view class="load-more" v-if="hasMore || loadingMore">
          <view class="loading-spinner small" v-if="loadingMore">
            <view class="loading-circle"></view>
          </view>
          <text v-if="loadingMore">加载更多...</text>
          <text v-else-if="hasMore">上拉加载更多</text>
        </view>
      </scroll-view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && schoolList.length === 0 && !showProvinceIndex">
        <image :src="imgUrl + '/qdkbm/newimage/fhui/empty-search.png'" class="empty-icon"></image>
        <view class="empty-text">暂无符合条件的学校</view>
        <view class="empty-tips">尝试更换关键词或筛选条件</view>
      </view>
      
      <!-- 底部加载提示 -->
      <view class="loading-more" v-if="schoolList.length > 0 && hasMore && loadingMore">
        <view class="loading-dot"></view>
        <view class="loading-dot"></view>
        <view class="loading-dot"></view>
        <text class="loading-text">加载中...</text>
      </view>
    </view>

    <!-- 底部导航 -->
    <tabBar :current-page="5"></tabBar>
  </view>
</template>

<script>
import tabBar from "@/components/tabBar.vue";

export default {
      components: {
        tabBar
      },
      computed: {
        sortedSchoolList() {
          if (this.sortBy === 'rank') {
            return [...this.schoolList].sort((a, b) => {
              if (!a.rank) return 1;
              if (!b.rank) return -1;
              return a.rank - b.rank;
            });
          } else if (this.sortBy === 'name') {
            return [...this.schoolList].sort((a, b) => {
              return a.name.localeCompare(b.name, 'zh');
            });
          }
          return this.schoolList;
        }
      },
      data() {
        return {
          imgUrl: "https://www.qdkabao.com",
          titleTop: 0,
          loading: false,
          loadingMore: false,
          showFilterPanel: false,
          currentFilterPanel: 'location',
          searchKeyword: "",
          pageNo: 1,
          pageSize: 10,
          schoolList: [],
          hasMore: true,
          scrollViewHeight: 500, // 默认高度，将在onReady中动态计算
          currentFilter: "all",
          sortBy: "rank",
          showProvinceIndex: false,
          selectedProvince: null,
          provinces: [
            { code: "11", name: "北京" },
            { code: "12", name: "天津" },
            { code: "13", name: "河北" },
            { code: "14", name: "山西" },
            { code: "15", name: "内蒙古" },
            { code: "21", name: "辽宁" },
            { code: "22", name: "吉林" },
            { code: "23", name: "黑龙江" },
            { code: "31", name: "上海" },
            { code: "32", name: "江苏" },
            { code: "33", name: "浙江" },
            { code: "34", name: "安徽" },
            { code: "35", name: "福建" },
            { code: "36", name: "江西" },
            { code: "37", name: "山东" },
            { code: "41", name: "河南" },
            { code: "42", name: "湖北" },
            { code: "43", name: "湖南" },
            { code: "44", name: "广东" },
            { code: "45", name: "广西" },
            { code: "46", name: "海南" },
            { code: "50", name: "重庆" },
            { code: "51", name: "四川" },
            { code: "52", name: "贵州" },
            { code: "53", name: "云南" },
            { code: "54", name: "西藏" },
            { code: "61", name: "陕西" },
            { code: "62", name: "甘肃" },
            { code: "63", name: "青海" },
            { code: "64", name: "宁夏" },
            { code: "65", name: "新疆" }
          ],
        }
      },
      onLoad() {
        // 确保provinces数组已初始化
        if (!this.provinces || this.provinces.length === 0) {
          this.provinces = [
            { code: "11", name: "北京" },
            { code: "12", name: "天津" },
            { code: "13", name: "河北" },
            { code: "14", name: "山西" },
            { code: "15", name: "内蒙古" },
            { code: "21", name: "辽宁" },
            { code: "22", name: "吉林" },
            { code: "23", name: "黑龙江" },
            { code: "31", name: "上海" },
            { code: "32", name: "江苏" },
            { code: "33", name: "浙江" },
            { code: "34", name: "安徽" },
            { code: "35", name: "福建" },
            { code: "36", name: "江西" },
            { code: "37", name: "山东" },
            { code: "41", name: "河南" },
            { code: "42", name: "湖北" },
            { code: "43", name: "湖南" },
            { code: "44", name: "广东" },
            { code: "45", name: "广西" },
            { code: "46", name: "海南" },
            { code: "50", name: "重庆" },
            { code: "51", name: "四川" },
            { code: "52", name: "贵州" },
            { code: "53", name: "云南" },
            { code: "54", name: "西藏" },
            { code: "61", name: "陕西" },
            { code: "62", name: "甘肃" },
            { code: "63", name: "青海" },
            { code: "64", name: "宁夏" },
            { code: "65", name: "新疆" }
          ];
        }
        this.getSchoolList();
      },
      onReady() {
        const info = uni.getSystemInfoSync();
        this.titleTop = info.statusBarHeight;
        
        // 计算scroll-view的高度
        this.calculateScrollViewHeight();
        
        // 监听窗口大小变化，重新计算高度
        uni.onWindowResize(() => {
          this.calculateScrollViewHeight();
        });
      },
      
      // 滚动到底部时触发 (保留此方法作为备用触发方式)
      onReachBottom() {
        if (this.hasMore && !this.loading && !this.loadingMore) {
          this.loadMore();
        }
      },
      
      // 滚动到底部时触发（scroll-view的scrolltolower事件）
      onScrollToLower() {
        if (this.hasMore && !this.loading && !this.loadingMore) {
          this.loadMore();
        }
      },
      
      methods: {
        // 加载更多
        loadMore() {
          if (this.hasMore && !this.loadingMore) {
            this.loadingMore = true;
            this.pageNo++;
            
            // 使用延时模拟网络请求时间，使加载动画有足够时间显示
            setTimeout(() => {
              this.getSchoolList();
            }, 300);
          }
        },
        
        // 滚动到底部时触发（scroll-view的scrolltolower事件）
        onScrollToLower() {
          if (this.hasMore && !this.loading && !this.loadingMore) {
            this.loadMore();
          }
        },
        
        // 计算scroll-view的高度
        calculateScrollViewHeight() {
          const query = uni.createSelectorQuery();
          query.select('.fixed-header-area').boundingClientRect();
          query.select('.filter-section').boundingClientRect();
          query.selectViewport().boundingClientRect();
          
          query.exec(res => {
            if (res && res.length >= 3) {
              const headerHeight = res[0].height || 0;
              const filterHeight = res[1].height || 0;
              const windowHeight = res[2].height || 0;
              
              // 计算scroll-view高度 = 窗口高度 - 头部高度 - 筛选区高度 - tabBar高度(50px) - 额外边距(10px)
              this.scrollViewHeight = windowHeight - headerHeight - filterHeight - 50 - 10;
            }
          });
        },
        // 获取学校列表
        getSchoolList() {
          if (this.pageNo === 1) {
            this.loading = true;
          }
          this.showFilterPanel = false;
          
          // 构建请求参数
          const params = {
            appkey: '257KUWTX6ZUES8994QLLN5Z5PPLDTHV5', // 替换为实际的 APPKEY
            keywords: this.searchKeyword,
            pagesize: this.pageSize,
            pageindex: this.pageNo,
			isDeleted:false
          };
          
          // 根据筛选条件添加参数
          if (this.currentFilter === '985') {
            params.is985 = true;
          } else if (this.currentFilter === '211') {
            params.is211 = true;
          } else if (this.currentFilter === 'double') {
            params.isdualclass = true;
          } else if (this.currentFilter === 'normal') {
            params.collegecategory = '师范类';
          } else if (this.currentFilter === 'art') {
            params.collegecategory = '艺术类';
          }
          
          // 添加省份筛选
          if (this.selectedProvince) {
            params.keywords = params.keywords ? 
              `${params.keywords} ${this.selectedProvince.name}` : 
              this.selectedProvince.name;
          }
          // 发起请求
          uni.request({
            url: 'https://api.gugudata.com/location/college',
            method: 'GET',
            data: params,
            success: (res) => {
              if (this.pageNo === 1) {
                this.loading = false;
              }
              this.loadingMore = false;
              
              if (res.statusCode === 200 && res.data && res.data.DataStatus && res.data.DataStatus.StatusCode === 100) {
                // 处理返回的数据
                const collegeList = res.data.Data || [];
                
                // 转换数据格式以匹配现有的展示逻辑
                const formattedList = collegeList.map(item => ({
                  id: item.DataId,
                  name: item.CollegeName,
                  province: item.Province,
                  city: item.City,
                  type: item.CollegeCategory,
                  rank: item.Ranking,
                  is985: item.Is985,
                  is211: item.Is211,
                  isDoubleFirst: item.IsDualClass,
                  address: item.Address,
                  website: item.WebSite,
                  intro: item.Intro,
                  coverImage: item.CoverImage,
                  educationLevel: item.EduLevel,
                  collegeProperty: item.CollegeProperty
                }));
                
                if (this.pageNo === 1) {
                  this.schoolList = formattedList;
                } else {
                  this.schoolList = [...this.schoolList, ...formattedList];
                }
                
                this.hasMore = this.schoolList.length < res.data.DataStatus.DataTotalCount;
                this.showProvinceIndex = this.schoolList.length === 0 && !this.searchKeyword && !this.selectedProvince;
              } else {
                uni.showToast({
                  title: res.data?.DataStatus?.StatusDescription || '获取学校列表失败',
                  icon: 'none'
                });
              }
            },
            fail: (err) => {
              this.loading = false;
              this.loadingMore = false;
              uni.showToast({
                title: '网络异常，请稍后重试',
                icon: 'none'
              });
              console.error('请求失败:', err);
            }
          });
        },
        
        // 切换筛选面板
        toggleFilterPanel(panelType) {
          if (this.currentFilterPanel === panelType && this.showFilterPanel) {
            this.closeFilterPanel();
          } else {
            this.currentFilterPanel = panelType;
            this.showFilterPanel = true;
            
            // 确保provinces数组已初始化
            if (panelType === 'location' && (!this.provinces || this.provinces.length === 0)) {
              this.provinces = [
                { code: "11", name: "北京" },
                { code: "12", name: "天津" },
                { code: "13", name: "河北" },
                { code: "14", name: "山西" },
                { code: "15", name: "内蒙古" },
                { code: "21", name: "辽宁" },
                { code: "22", name: "吉林" },
                { code: "23", name: "黑龙江" },
                { code: "31", name: "上海" },
                { code: "32", name: "江苏" },
                { code: "33", name: "浙江" },
                { code: "34", name: "安徽" },
                { code: "35", name: "福建" },
                { code: "36", name: "江西" },
                { code: "37", name: "山东" },
                { code: "41", name: "河南" },
                { code: "42", name: "湖北" },
                { code: "43", name: "湖南" },
                { code: "44", name: "广东" },
                { code: "45", name: "广西" },
                { code: "46", name: "海南" },
                { code: "50", name: "重庆" },
                { code: "51", name: "四川" },
                { code: "52", name: "贵州" },
                { code: "53", name: "云南" },
                { code: "54", name: "西藏" },
                { code: "61", name: "陕西" },
                { code: "62", name: "甘肃" },
                { code: "63", name: "青海" },
                { code: "64", name: "宁夏" },
                { code: "65", name: "新疆" }
              ];
            }
          }
        },
        
        // 关闭筛选面板
        closeFilterPanel() {
          this.showFilterPanel = false;
        },
        
        // 获取筛选条件文本
        getFilterText() {
          switch (this.currentFilter) {
            case 'all':
              return '全部';
            case '985':
              return '985高校';
            case '211':
              return '211高校';
            case 'double':
              return '双一流';
            case 'normal':
              return '师范类';
            case 'art':
              return '艺术类';
            default:
              return '全部';
          }
        },
        
        // 搜索学校
        searchSchools() {
          this.pageNo = 1;
          this.showProvinceIndex = false;
          this.selectedProvince = null;
          this.getSchoolList();
        },
        
        // 清除搜索
        clearSearch() {
          this.searchKeyword = "";
          this.pageNo = 1;
        },
        
        // 选择省份
        selectProvince(province) {
          this.selectedProvince = province;
          this.pageNo = 1;
          this.showFilterPanel = false;
          this.getSchoolList();
        },
        
        // 设置筛选条件
        setFilter(filter) {
          this.currentFilter = filter;
          this.pageNo = 1;
          this.showFilterPanel = false;
          this.getSchoolList();
        },
        
        // 设置排序方式
        setSortBy(sortType) {
          this.sortBy = sortType;
        },
        
        // 查看学校详情
        viewSchoolDetail(school) {
          uni.navigateTo({
            url: `/pages/school/detail?id=${school.id}&name=${encodeURIComponent(school.name)}`
          });
        },
        
        // 获取学校名称首字母
        getSchoolInitial(name) {
          if (!name) return "校";
          
          // 特殊处理一些学校名称
          if (name.includes("北京大学")) return "北";
          if (name.includes("清华大学")) return "清";
          if (name.includes("复旦大学")) return "复";
          if (name.includes("上海交通大学")) return "交";
          
          // 默认使用第一个字符
          return name.charAt(0);
        }
      }
    }
</script>

<style lang="scss">
/* 全局样式 */
page {
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

/* 加载动画 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-circle {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid rgba(255, 137, 24, 0.2);
  border-top-color: #FF8918;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 主容器 */
.school-container {
  position: relative;
  padding-bottom: 150rpx;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 150rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 150rpx);
}

/* 禁止滚动状态 */
.no-scroll {
  position: fixed;
  width: 100%;
  height: 100%;
  overflow: hidden;
  top: 0;
  left: 0;
}

/* 固定头部 */
.fixed-header-area {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #fff;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #FF8918, #FFC107);
  padding: 20rpx 40rpx;
  padding-bottom: 40rpx;
  border-radius: 0 0 30rpx 30rpx;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-container {
  display: flex;
  flex-direction: column;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 10rpx;
}

.header-icon {
  width: 100rpx;
  height: 100rpx;
}

.header-icon image {
  width: 100%;
  height: 100%;
}

/* 搜索区域 */
.search-section {
  padding: 30rpx 40rpx;
  background-color: #fff;
}

.search-box {
  display: flex;
  align-items: center;
}

.search-input {
  flex: 1;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  margin-right: 20rpx;
  position: relative;
}

.search-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.search-input input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
}

.clear-btn {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-btn image {
  width: 30rpx;
  height: 30rpx;
}

.search-button {
  width: 120rpx;
  height: 80rpx;
  background-color: #FF8918;
  color: #fff;
  font-size: 28rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
}

/* 筛选区域 */
.filter-section {
  display: flex;
  justify-content: space-around;
  background-color: #fff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.filter-group {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  padding: 10rpx 20rpx;
}

.filter-label {
  color: #666;
  margin-right: 10rpx;
}

.filter-value {
  color: #333;
  max-width: 120rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.filter-arrow {
  width: 24rpx;
  height: 24rpx;
  margin-left: 10rpx;
}

/* 筛选面板遮罩层 */
.filter-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 998;
  touch-action: none;
  -webkit-backdrop-filter: blur(3px);
  backdrop-filter: blur(3px);
}

/* 筛选面板 */
.filter-panel {
  position: fixed;
  bottom: -100%;
  left: 0;
  right: 0;
  background-color: #fff;
  z-index: 999;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  padding: 30rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 150rpx);
  border-radius: 30rpx 30rpx 0 0;
  max-height: 80vh;
  overflow-y: auto;
  transition: bottom 0.3s ease-in-out;
  touch-action: pan-y;
  -webkit-overflow-scrolling: touch;
}

.filter-panel.show {
  bottom: 0;
}

/* 关闭按钮 */
.panel-close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.panel-close image {
  width: 32rpx;
  height: 32rpx;
}

.panel-header {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.province-grid, .type-grid, .sort-grid {
  display: flex;
  flex-wrap: wrap;
}

.province-item, .type-item, .sort-item {
  width: 22%;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #333;
  background-color: #f5f5f5;
  margin: 10rpx;
  border-radius: 8rpx;
}

.province-item.active, .type-item.active, .sort-item.active {
  background-color: #FF8918;
  color: #fff;
}

/* 学校列表 */
.school-list {
  padding: 0 20rpx;
  flex: 1;
  box-sizing: border-box;
}

/* 底部加载更多样式 */
.load-more {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  width: 100%;
  text-align: center;
}

.load-more text {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}

.loading-spinner.small {
  width: 30rpx;
  height: 30rpx;
}

.loading-spinner.small .loading-circle {
  width: 30rpx;
  height: 30rpx;
  border-width: 3rpx;
  border: 3rpx solid rgba(255, 137, 24, 0.2);
  border-top-color: #FF8918;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

.result-count {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.highlight {
  color: #666;
  font-weight: normal;
}

.school-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.school-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #FF8918, #FF5B03);
  border-radius: 0 0 12rpx 12rpx;
}

.school-logo {
  width: 100rpx;
  height: 100rpx;
  margin-right: 30rpx;
  flex-shrink: 0;
  border-radius: 50%;
  overflow: hidden;
}

.school-logo image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.logo-placeholder {
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #FF8918;
}

.school-info {
  flex: 1;
  overflow: hidden;
}

.school-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.school-location {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.school-tags {
  display: flex;
  flex-wrap: wrap;
}

.tag {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  margin-right: 10rpx;
}

.tag-985 {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1rpx solid #1890ff;
}

.tag-211 {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1rpx solid #52c41a;
}

.tag-double {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1rpx solid #fa8c16;
}

.tag-edu {
  background-color: #f9f0ff;
  color: #722ed1;
  border: 1rpx solid #722ed1;
}

.tag-property {
  background-color: #fff0f6;
  color: #eb2f96;
  border: 1rpx solid #eb2f96;
}

.tag-type {
  background-color: #f5f5f5;
  color: #666;
  border: 1rpx solid #ddd;
}

.school-action {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}

.action-btn {
  background-color: #FF8918;
  color: #fff;
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.empty-tips {
  font-size: 26rpx;
  color: #999;
}

/* 加载更多 */
.load-more {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  width: 100%;
  text-align: center;
}

.load-more text {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}

.loading-spinner.small {
  width: 30rpx;
  height: 30rpx;
}

.loading-spinner.small .loading-circle {
  width: 30rpx;
  height: 30rpx;
  border-width: 3rpx;
  border: 3rpx solid rgba(255, 137, 24, 0.2);
  border-top-color: #FF8918;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

.load-more-text {
  font-size: 28rpx;
  color: #999;
  display: inline-block;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  background-color: #f5f5f5;
}
/* 底部加载更多样式 */
.load-more {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  width: 100%;
  text-align: center;
  
  text {
    font-size: 24rpx;
    color: #999;
    margin-left: 10rpx;
  }
  
  .loading-spinner.small {
    width: 30rpx;
    height: 30rpx;
    
    .loading-circle {
      width: 30rpx;
      height: 30rpx;
      border-width: 3rpx;
    }
  }
}
</style>
