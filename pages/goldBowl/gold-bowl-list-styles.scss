/* 金饭碗列表页样式 */
@import './gold-bowl-styles.scss';

/* 列表页特有样式 */
.gold-bowl-list-container {
    position: relative;
    padding-bottom: 120rpx;

    /* 头部样式 */
    .head {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 100;
        background: transparent;

        .header1 {
            display: flex;
            align-items: center;
            padding: 15rpx 20rpx 5rpx;

            .back-btn {
                width: 60rpx;
                height: 60rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                background: rgba(255, 255, 255, 0.8);
                border-radius: 50%;
                box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;

                &:active {
                    transform: scale(0.95);
                }
            }

            .text {
                flex: 1;
                text-align: center;
                font-size: 36rpx;
                font-weight: bold;
                color: #333;
                margin-right: 60rpx;
            }
        }
    }

    /* 加载遮罩 */
    .loading-mask {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 251, 242, 0.8);
        z-index: 999;
        display: flex;
        align-items: center;
        justify-content: center;

        .loading-spinner {
            width: 80rpx;
            height: 80rpx;
            border: 6rpx solid #FFD700;
            border-top-color: #FFA500;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
    }

    /* 背景样式 */
    .bg {
        min-height: 100vh;
        background-size: cover;
        background-position: center;
        position: relative;
    }

    /* 固定顶部 */
    .fixed-top {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        padding: 20rpx 30rpx;
        z-index: 10;
        background: linear-gradient(to bottom, rgba(255, 251, 242, 0.95), rgba(255, 251, 242, 0.7));
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-bottom: 1px solid rgba(255, 165, 0, 0.1);

        .province-btn {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            border-radius: 40rpx;
            height: 80rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #FFFFFF;
            font-size: 28rpx;
            font-weight: bold;
            box-shadow: 0 4rpx 12rpx rgba(255, 165, 0, 0.3);
            transition: all 0.3s ease;
            animation: pulse 2s infinite;

            &:active {
                transform: scale(0.98);
            }

            &.disabled {
                background: #CCCCCC;
                animation: none;
                box-shadow: none;
                opacity: 0.7;
            }
        }
    }

    /* 主要内容区 */
    .main-content {
        padding: 10rpx 20rpx 120rpx;
        margin-top: 0;
    }
    
    /* 行业列表 */
    .industry-list {
        box-sizing: border-box;

        .industry-item {
            background-color: #FFFFFF;
            border-radius: 16rpx;
            padding: 20rpx;
            margin-bottom: 8rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            animation: slideIn 0.5s ease-out forwards;
            border-left: 4rpx solid transparent;

            &.selected {
                background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.05) 100%);
                border-left: 4rpx solid #FFA500;
                transform: translateX(10rpx);
            }

            &:active {
                transform: scale(0.98);
            }

            .item-text {
                font-size: 30rpx;
                color: #333;
                font-weight: 500;
            }

            .dot {
                width: 20rpx;
                height: 20rpx;
                border-radius: 50%;
                border: 2rpx solid #FFD700;
                transition: all 0.3s ease;

                &.selected {
                    background-color: #FFD700;
                    transform: scale(1.2);
                }
            }
        }

        .no-data-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 400rpx;
            width: 100%;
            padding: 30rpx;
            box-sizing: border-box;
            margin: 100rpx auto;
            text-align: center;

            .no-data-text {
                font-size: 28rpx;
                color: #999;
                margin-top: 20rpx;
                width: 100%;
                text-align: center;
            }
        }
    }

    /* 浮动按钮 */
    .floating-action-button {
        position: fixed;
        right: 30rpx;
        bottom: 180rpx;
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        border-radius: 50rpx;
        height: 90rpx;
        padding: 0 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #FFFFFF;
        font-size: 32rpx;
        font-weight: bold;
        box-shadow: 0 8rpx 16rpx rgba(255, 165, 0, 0.3);
        transition: all 0.3s ease;
        animation: pulse 2s infinite;
        z-index: 99;

        &:active {
            transform: scale(0.95);
            box-shadow: 0 4rpx 8rpx rgba(255, 165, 0, 0.3);
        }

        .button-text {
            margin-right: 10rpx;
        }

        .button-icon {
            width: 32rpx;
            height: 32rpx;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    /* 省份选择弹窗 */
    .cityPart {
        padding: 30rpx;
        background-color: #FFFFFF;
        border-radius: 20rpx 20rpx 0 0;

        .title {
            font-size: 32rpx;
            font-weight: bold;
            color: #333;
            text-align: center;
            margin-bottom: 30rpx;
            position: relative;

            .icon {
                position: absolute;
                right: 0;
                top: 0;
                width: 40rpx;
                height: 40rpx;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        .cityList {
            display: flex;
            flex-wrap: wrap;
            gap: 20rpx;

            .item {
                width: calc(25% - 15rpx);
                height: 80rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #F5F5F5;
                border-radius: 10rpx;
                font-size: 26rpx;
                color: #333;
                transition: all 0.3s ease;

                &:active {
                    transform: scale(0.95);
                }

                &.active {
                    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
                    color: #FFFFFF;
                    font-weight: bold;
                }
            }
        }
    }
}
