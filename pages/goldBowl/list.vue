<template>
	<view class="gold-bowl-list-container">
		<view class="loading-mask" v-if="loading">
			<view class="loading-spinner"></view>
			<view class="loading-text" style="margin-top: 20rpx; color: #FFA500; font-size: 28rpx;">加载中...</view>
		</view>

		<!-- 固定头部区域 -->
		<view class="fixed-header-area">
			<!-- 现代化头部设计 -->
			<view class="header" :style="{'padding-top': titleTop + 'px'}">
				<view class="header-content">
					<view class="back-btn" @tap="goBack">
						<u-icon name="arrow-left" color="#FFFFFF" size="20"></u-icon>
					</view>
					<view class="title-container">
						<text class="title">{{dirName || '金饭碗'}}</text>
						<text class="subtitle">对口院校推荐</text>
					</view>
					<view class="header-icon">
						<image :src="imgUrl+'/qdkbm/newimage/fhui/gold-bowl-icon.png'" mode="widthFix"></image>
					</view>
				</view>
			</view>

			<!-- 省份选择按钮 -->
			<view class="section-header">
				<view class="header-left">
					<view class="section-title">
						<view class="title-icon"></view>
						<text>院校列表</text>
					</view>
					<view class="section-subtitle">选择院校，查看专业详情</view>
				</view>
				<view class="header-action">
					<button class="view-schools-btn" @tap="toggleProvincePopup">
						<text>{{selectedProvince.name || '选择省份'}}</text>
						<u-icon name="arrow-down" color="#FFFFFF" size="14" style="margin-left: 6rpx;"></u-icon>
					</button>
				</view>
			</view>
		</view>
		
		<!-- 主要内容区 -->
		<view class="main-content">
			<!-- 院校列表 -->
			<view class="industry-list">
				<template v-if="industryList.length > 0">
					<view v-for="(item, index) in industryList" :key="index" class="industry-item"
						:class="{'selected': selectedIndex === item.id}" @tap="selectIndustry(item)" :style="{ 'animation-delay': index * 0.05 + 's' }">
						<text class="item-text">{{item.name}}</text>
						<view class="dot" :class="{'selected': selectedIndex === item.id}"></view>
					</view>
				</template>
				<view v-else class="no-data-container">
					<image :src="imgUrl+'/qdkbm/newimage/fhui/no-data.png'" mode="widthFix" style="width: 180rpx; margin-bottom: 15rpx;"></image>
					<text class="no-data-text">暂无对口院校</text>
				</view>
			</view>
		</view>
		
		<!-- 浮动按钮 -->
		<view class="floating-action-button" @tap="viewDetails">
			<text class="button-text">查看对口专业介绍</text>
			<view class="button-icon">
				<u-icon name="arrow-right" color="#FFFFFF" size="16"></u-icon>
			</view>
		</view>
		<u-popup safeAreaInsetBottom closeable :show="showProvincePopup" :closeable="false" :round="10" mode="bottom"
			@close="closeProvincePopup">
			<view class="cityPart">
				<view class="title">请选择高考省份
					<view class="icon" @tap="closeProvincePopup">
						<u-icon name="close" color="#000000" size="24"></u-icon>
					</view>
				</view>
				<view class="cityList">
					<view class="item" @tap="selectProvince(item)"
						:class="selectedProvince.value === item.value?'active':''" v-for="(item,index) in provinceList"
						:key="index">
						{{item.name}}
					</view>
				</view>
			</view>
		</u-popup>
		<myDialog :showbox="showbox" @closebox="closebox" @openVip="openVip"></myDialog>
		<tabBar :current-page="4"></tabBar>
	</view>
</template>

<script>
	import myDialog from '@/components/dialog.vue'
	import tabBar from '@/components/tabBar.vue'
	import loadingPopup from '@/components/loading-popup/loading-popup.vue'
	import '@/pages/goldBowl/gold-bowl-list-styles.scss'
	export default {
		components: {
			myDialog,
			tabBar,
			loadingPopup
		},
		data() {
			return {
				showProvincePopup: false,
				selectedProvince: {},
				showbox: false,
				loading: false,
				info: {},
				pName: "",
				titleTop2: 0,
				imgUrl: this.$base.uploadImgUrl,
				titleTop: 0,
				selectedIndex: 0,
				viewCount: 0,
				lastViewDate: '',
				popupContent: '',
				pList: [],
				dirName: "",
				dirId: "",
				currentProvinceId: "",
				provinceList: [],
				industryList: [
				]
			}
		},
		onLoad(options) {
			// 获取本地存储的查看次数和最后查看日期
			const today = new Date().toDateString();
			const storedData = uni.getStorageSync('trendViewData') || {
				count: 0,
				date: ''
			};
			if (options.dirName) {
				this.dirName = options.dirName
			}
			if (options.dirId) {
				this.dirId = options.dirId
			}
			if (storedData.date !== today) {
				// 如果是新的一天，重置计数
				this.viewCount = 0;
				this.lastViewDate = today;
				uni.setStorageSync('trendViewData', {
					count: 0,
					date: today
				});
			} else {
				this.viewCount = storedData.count;
				this.lastViewDate = storedData.date;
			}
			this.getProvinceList()
			// 不在这里调用getIndustrySchools，避免与getProvinceList中的调用冲突
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top;
			uni.createSelectorQuery().select('.fixed-header-area').boundingClientRect((res2) => {
				if (res2) {
					this.titleTop2 = res2.height
				} else {
					// 如果无法获取元素，设置一个默认值
					this.titleTop2 = 200
				}
			}).exec()
		},
		methods: {
			toggleProvincePopup() {
				// Force the popup to show regardless of current state
				this.showProvincePopup = false;
				setTimeout(() => {
					this.showProvincePopup = true;
				}, 50);
			},
			closeProvincePopup() {
				this.showProvincePopup = false;
			},
			selectProvince(province) {
				this.selectedProvince = province;

				this.closeProvincePopup();
				// 选择省份后加载内容
				this.getIndexPage()
			},
			getProvinceList() {
				this.$apis.getdictData({
					pageNo: 1,
					pageSize: 100,
					dictType: "province"
				}).then((res) => {
					if (res.code == 0) {
						if (res.data.list.length > 0) {
							res.data.list.map((item) => {
								item.name = ''
								if (item.label == '澳门特别行政区') {
									item.name = '澳门'
								}
								if (item.label == '香港特别行政区') {
									item.name = '香港'
								}
								if (item.label == '内蒙古自治区') {
									item.name = '内蒙古'
								}
								if (item.label == '新疆维吾尔自治区') {
									item.name = '新疆'
								}
								if (item.label == '宁夏回族自治区') {
									item.name = '宁夏'
								}
								if (item.label == '西藏自治区') {
									item.name = '西藏'
								}
								if (item.label == '广西壮族自治区') {
									item.name = '广西'
								}
								if (item.label.indexOf('省') !== -1 || item.label.indexOf('市') !== -1) {
									item.name = (item.label).substring(0, item.label.length - 1)
								}

							})
						}
						this.provinceList = res.data.list
						let currentProvice = uni.getStorageSync('myInfo').province
						this.provinceList.forEach((newitem) => {
							if (newitem.label === currentProvice) {
								this.selectedProvince = newitem
								this.pName = newitem.label
							}
						})
						this.$nextTick(() => {
							// 如果有行业ID，使用行业ID获取院校数据
							if (this.dirId) {
								this.getIndustrySchools()
							} else {
								// 否则使用省份获取学校列表
								this.getIndexPage()
							}
						})
					}
				})
			},
			getIndexPage() {
				this.loading = true
				this.$apis.getschoolList({
					pageNo: 1,
					pageSize: 100,
					province: this.selectedProvince?.value || '',
					dirId: this.dirId || ''
				}).then((res) => {
					if (res.code == 0) {
						this.industryList = res.data.list
						setTimeout(() => {
							this.loading = false
						}, 600)
					} else {
						setTimeout(() => {
							this.loading = false
						}, 600)
					}
				}).catch((err) => {
					setTimeout(() => {
						this.loading = false
					}, 600)
				})

			},
			getIndustrySchools() {
				this.loading = true;
				this.$apis.getemploymentdirGet({
					id: this.dirId
				}).then((res) => {
					if (res.code == 0 && res.data) {
						// 如果有学校数据，处理成列表形式
						if (res.data.schools) {
							const schoolNames = res.data.schools.split(',');
							// 将学校名称转换为列表项格式
							this.industryList = schoolNames.map((name, index) => {
								return {
									id: index + 1,
									name: name
								};
							});
						} else {
							this.industryList = [];
						}
						setTimeout(() => {
							this.loading = false;
						}, 600);
					} else {
						this.industryList = [];
						setTimeout(() => {
							this.loading = false;
						}, 600);
					}
				}).catch((err) => {
					this.industryList = [];
					setTimeout(() => {
						this.loading = false;
					}, 600);
				});
			},
			closebox(val) {
				this.showbox = val
			},
			openbox() {
				this.showbox = true
			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			},
			selectIndustry(item) {
				this.selectedIndex = item.id;
				this.info = item
			},
			async viewDetails(item) {
				// 检查是否是会员
				let token = uni.getStorageSync('token')
				if (token) {
					let res = await this.$store.dispatch('getuseInfo')
					const isVip = uni.getStorageSync('isVip') || false;
					this.viewCount = uni.getStorageSync('tys') || 0
					if (isVip) {
						if (this.info.id) {
							let obj = {
								dirName: this.dirName,
								schoolName: this.info.name,
								pName: this.pName
							}
							uni.navigateTo({
								url: `/pages/goldBowl/detail?item=${encodeURIComponent(JSON.stringify(obj))}`
							});
						} else {
							uni.showToast({
								title: '请选择一个院校',
								icon: 'none'
							})
						}
					} else {
						if (this.viewCount <= 0) {
							this.showbox = true
						} else {
							this.viewCount--
							uni.setStorageSync('tys', this.viewCount)
							if (this.info.id) {
								let obj = {
									dirName: this.dirName,
									schoolName: this.info.name,
									pName: this.pName
								}
								uni.navigateTo({
									url: `/pages/goldBowl/detail?item=${encodeURIComponent(JSON.stringify(obj))}`
								});
							} else {
								uni.showToast({
									title: '请选择一个院校',
									icon: 'none'
								})
							}
						}
					}
				} else {
					// 尝试自动登录
					let loginRes = await this.$store.dispatch('gologinPage')
					if (loginRes) {
						// 自动登录成功，重新调用本方法
						this.viewDetail()
					} else {
						// 自动登录失败，不做任何处理
						// 用户可以继续使用应用，但无法查看详情
					}
				}

			},
			confirmPopup() {
				this.$refs.popup.close();
			},
			closePopup() {
				this.$refs.popup.close();
			},
			openVip() {
				uni.navigateTo({
					url: '/pages/vip/index'
				});
				this.showbox = false
			},
			goBack() {
				uni.navigateBack({
					delta: 1
				})
			},
			goToAiChat() {
				uni.navigateTo({
					url: '/pages/aiChat/index'
				})
			}
		}
	}
</script>

<style lang="scss">
	.btn {
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 20rpx;
		font-size: 28rpx;
		white-space: nowrap;

		&.province-btn {
			background-image: linear-gradient(180deg, #FF8510 0, #FFBD73 100%);
			flex: 1;
			margin: 0 10rpx;
			color: #FFFFFF;
			font-weight: 600;
			font-size: 32rpx;
			border: none;
			border-radius: 12rpx;

			&.disabled {
				background-image: linear-gradient(180deg, #cccccc 0, #dddddd 100%);
				opacity: 0.7;
				color: #999999;
			}
		}

		&::after {
			border: none;
		}
	}

	.cityPart {
		padding: 40rpx 20rpx;

		.title {
			font-weight: 400;
			font-size: 32rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #666;
			position: relative;
			margin-bottom: 35rpx;
			.icon {
				font-weight: 700;
				position: absolute;
				right: 5rpx;
				top: -10rpx;
				color: #000;
			}
		}

		.cityList {
			max-height: 780rpx;
			padding-bottom: 30rpx;
			display: flex;
			flex-direction: row;
			flex-wrap: wrap;
			overflow-y: auto;

			.item {
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0 10rpx;
				margin-bottom: 20rpx;
				width: calc(25% - 20rpx);
				background: #f1f1f1;
				color: #333;
				height: 72rpx;
				line-height: 72rpx;
				font-size: 28rpx;
				border-radius: 12rpx;

				&.active {
					color: #fff;
					background: #FF8918;
				}

				&:active {
					transform: scale(0.98);
				}
			}
		}
	}

	@-webkit-keyframes countloading {
		0% {
			transform: rotate(180deg)
		}

		100% {
			transform: rotate(0deg)
		}
	}

	@keyframes countloading {
		0% {
			transform: rotate(180deg)
		}

		100% {
			transform: rotate(0deg)
		}
	}


	// .progress-active-loading{animation:progress-active 2.4s cubic-bezier(.23,1,.32,1) infinite;background-color:#fff;height:100%;position:absolute;width:100%;z-index:999}
	@-webkit-keyframes progress-active {
		0% {
			opacity: .1;
			transform: translateX(-100%) scaleX(0)
		}

		20% {
			opacity: .5;
			transform: translateX(-100%) scaleX(0)
		}

		100% {
			opacity: 0;
			transform: translateX(0) scaleX(1)
		}
	}

	@keyframes progress-active {
		0% {
			opacity: .1;
			transform: translateX(-100%) scaleX(0)
		}

		20% {
			opacity: .5;
			transform: translateX(-100%) scaleX(0)
		}

		100% {
			opacity: 0;
			transform: translateX(0) scaleX(1)
		}
	}


	.loading-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;

		.loading-spinner {
			width: 50rpx;
			height: 50rpx;
			// border: 8rpx solid #c8a178;
			// border-top-color: transparent;
			// border-radius: 50%;
			animation: countloading 1s ease infinite;
			// animation:progress-active 2.4s cubic-bezier(.23,1,.32,1) infinite;
			background-image: url('https://pic.kefeichangduo.top/qdkbm/newimage/fhui/waiting.png');
			background-position: 50%;
			background-repeat: no-repeat;
			background-size: contain;
		}
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(360deg);
		}
	}

	.trend-container {
		position: relative;
		padding-bottom: 120rpx;

		.head {
			width: 100%;
			position: fixed;
			top: 0;
			left: 0;
			z-index: 999;
			.header1 {
				position: relative;
				width: 100%;
				height: 44px;
				display: flex;
				align-items: center;
				justify-content: center;
				.back-btn {
					position: absolute;
					left: 15px;
					top: 50%;
					transform: translateY(-50%);
					width: 32px;
					height: 32px;
					display: flex;
					align-items: center;
					justify-content: center;
					z-index: 10;
					background: rgba(255, 255, 255, 0.8);
					border-radius: 50%;
					box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
				}
				.text {
					font-size: 18px;
					font-weight: 500;
					color: #fff;
				}
			}
		}

		.bg {
			position: absolute;
			left: 0;
			top: 0;
			background-repeat: no-repeat;
			background-size: 100% auto;
			width: 100%;
			min-height: 100vh;
		}

		.fixed-top {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			z-index: 100;
			padding: 10rpx 15rpx;
			background-color: rgba(255, 255, 255, 0.95);
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
		}

		.industry-list {
			padding: 10rpx 10rpx 300rpx 15rpx;
			display: flex;
			flex-direction: column;
			gap: 15rpx;
			height: calc(100vh - 280rpx);
			box-sizing: border-box;

			.industry-item {
				padding: 25rpx 20rpx;
				background-color: #FFFFFF;
				border-radius: 12rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
				transition: all 0.3s;
				border-left: 4rpx solid transparent;
				margin-right: 10rpx;

				&:active {
					transform: scale(0.98);
					box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
				}

				.item-text {
					font-size: 30rpx;
					font-weight: 600;
					color: #333333;
					line-height: 1.4;
				}

				.dot {
					width: 40rpx;
					height: 40rpx;
					border-radius: 50%;
					border: 2rpx solid #E6E6E6;
					transition: all 0.3s;
					position: relative;
					display: flex;
					align-items: center;
					justify-content: center;

					&::after {
						content: '';
						width: 20rpx;
						height: 20rpx;
						border-radius: 50%;
						background-color: #E6E6E6;
						transition: all 0.3s;
					}

					&.selected {
						border-color: #FF8918;

						&::after {
							background-color: #FF8918;
						}
					}
				}
			}
			.no-data-container {
				padding: 20rpx;
				text-align: center;
				color: #666;
				flex: 1;
				display: flex;
				align-items: center;
				justify-content: center;
				height: 200rpx;
				margin-top: 100rpx;
			}
			.no-data-text {
				font-size: 32rpx;
				color: #FF8510;
				background-color: rgba(255, 133, 16, 0.05);
				padding: 20rpx 40rpx;
				border-radius: 8rpx;
				border: 1px dashed #FFBD73;
			}
		}

		.bottom-btn {
			position: fixed;
			bottom: 100rpx;
			left: 0;
			right: 0;
			padding: 20rpx;
			z-index: 99;
			padding-bottom: calc(constant(safe-area-inset-bottom) + 20rpx);
			padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
			background-color: #FFFFFF;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);

			.view-btn {
				width: 100%;
				height: 88rpx;
				background-image: linear-gradient(180deg, #FF8510 0, #FFBD73 100%);
				color: #FFFFFF;
				font-size: 32rpx;
				border-radius: 44rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-weight: 500;

				&::after {
					border: none
				}
			}
		}

		.popup-buttons {
			display: flex;
			justify-content: space-between;
			margin-top: 30rpx;
			padding: 0 30rpx;

			.popup-btn {
				width: 200rpx;
				height: 70rpx;
				border-radius: 35rpx;
				font-size: 28rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				&.cancel {
					background-color: #F5F5F5;
					color: #666;
				}

				&.confirm {
					background-image: linear-gradient(180deg, #FF8510 0, #FFBD73 100%);
					color: #FFFFFF;
				}
			}
		}
	}
</style>