/* 响应式设计变量 */
$small-screen: 320px;
$medium-screen: 375px;
$large-screen: 414px;

/* 动画效果 */
@keyframes fadeIn {
	0% { opacity: 0; }
	100% { opacity: 1; }
}

@keyframes slideIn {
	0% { transform: translateY(20px); opacity: 0; }
	100% { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
	0% { transform: scale(1); }
	50% { transform: scale(1.05); }
	100% { transform: scale(1); }
}

@keyframes shimmer {
	0% { background-position: -200% 0; }
	100% { background-position: 200% 0; }
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* 金饭碗容器样式 */
.gold-bowl-container, .gold-bowl-list-container {
	background-color: #FFFBF2;
	min-height: 100vh;
	animation: fadeIn 0.5s ease-in-out;
	position: relative;
	padding-bottom: 120rpx;

	/* 头部样式 */
	.header {
		background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
		padding: 20rpx 30rpx 30rpx;
		border-radius: 0 0 40rpx 40rpx;
		box-shadow: 0 4rpx 20rpx rgba(255, 165, 0, 0.3);
		position: relative;
		z-index: 10;
		width: 100%;

		.header-content {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.title-container {
				display: flex;
				flex-direction: column;

				.title {
					font-size: 44rpx;
					font-weight: bold;
					color: #FFFFFF;
					text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.2);
					margin-bottom: 6rpx;
				}

				.subtitle {
					font-size: 24rpx;
					color: rgba(255, 255, 255, 0.9);
				}
			}

			.header-icon {
				width: 80rpx;
				height: 80rpx;
				animation: pulse 2s ease-in-out infinite;

				image {
					width: 100%;
					height: auto;
				}
			}
		}
	}

	/* 加载动画样式 */
	.loading-container {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(255, 251, 242, 0.8);
		z-index: 999;
		display: flex;
		align-items: center;
		justify-content: center;

		.loading-spinner {
			display: flex;
			flex-direction: column;
			align-items: center;

			.loading-circle {
				width: 80rpx;
				height: 80rpx;
				border: 6rpx solid #FFD700;
				border-top-color: #FFA500;
				border-radius: 50%;
				animation: spin 1s linear infinite;
				margin-bottom: 20rpx;
			}

			.loading-text {
				font-size: 28rpx;
				color: #FFA500;
			}
		}
	}

	/* 固定头部区域样式 */
	.fixed-header-area {
		position: sticky;
		top: 0;
		z-index: 20;
		background-color: #FFFBF2;
		box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
		width: 100%;
		
		.section-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 15rpx 20rpx;
			margin-bottom: 5rpx;
			
			.header-left {
				flex: 1;
				
				.section-title {
					display: flex;
					align-items: center;
					margin-bottom: 6rpx;

					.title-icon {
						width: 8rpx;
						height: 36rpx;
						background: linear-gradient(to bottom, #FFD700, #FFA500);
						border-radius: 4rpx;
						margin-right: 16rpx;
					}

					text {
						font-size: 36rpx;
						font-weight: bold;
						color: #333;
					}
				}

				.section-subtitle {
					font-size: 24rpx;
					color: #666;
					margin-left: 24rpx;
				}
			}
			
			.header-action {
				.view-schools-btn {
					background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
					border-radius: 30rpx;
					height: 70rpx;
					padding: 0 30rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					color: #FFFFFF;
					font-size: 28rpx;
					font-weight: bold;
					box-shadow: 0 4rpx 10rpx rgba(255, 165, 0, 0.3);
					transition: all 0.3s ease;
					
					&:active {
						transform: scale(0.95);
					}
				}
			}
		}
	}
	
	/* 主要内容区样式 */
	.main-content {
		padding: 20rpx 30rpx 30rpx;
		margin-bottom: 100rpx;

		/* 章节标题样式 */
		.section-header {
			margin-bottom: 30rpx;
			animation: slideIn 0.5s ease-out;

			.section-title {
				display: flex;
				align-items: center;
				margin-bottom: 6rpx;

				.title-icon {
					width: 6rpx;
					height: 30rpx;
					background: linear-gradient(to bottom, #FFD700, #FFA500);
					border-radius: 3rpx;
					margin-right: 12rpx;
				}

				text {
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
				}
			}

			.section-subtitle {
				font-size: 22rpx;
				color: #666;
				margin-left: 18rpx;
			}
		}

		/* 行业列表样式 */
		.industry-list {
			display: flex;
			flex-direction: column;
			gap: 30rpx;
			margin-bottom: 40rpx;

			.industry-card {
				background-color: #FFFFFF;
				border-radius: 20rpx;
				padding: 30rpx;
				box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
				transition: all 0.3s ease;
				animation: slideIn 0.5s ease-out forwards;
				opacity: 0;
				border-left: 6rpx solid #FFD700;

				&.selected {
					background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.05) 100%);
					border-left: 6rpx solid #FFA500;
					transform: translateX(10rpx);
				}

				&:active {
					transform: scale(0.98);
				}

				.card-header {
					display: flex;
					align-items: center;
					margin-bottom: 20rpx;

					.industry-icon {
						width: 60rpx;
						height: 60rpx;
						border-radius: 12rpx;
						overflow: hidden;
						margin-right: 20rpx;

						.icon-bg {
							width: 100%;
							height: 100%;
							background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
						}

						.icon-text {
							width: 100%;
							height: 100%;
							background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
							display: flex;
							align-items: center;
							justify-content: center;
							color: #FFFFFF;
							font-size: 28rpx;
							font-weight: bold;
						}
					}

					.industry-name {
						font-size: 32rpx;
						font-weight: bold;
						color: #333;
						flex: 1;
					}

					.selection-indicator {
						width: 24rpx;
						height: 24rpx;
						border-radius: 50%;
						border: 2rpx solid #FFD700;
						transition: all 0.3s ease;

						&.active {
							background-color: #FFD700;
							transform: scale(1.2);
						}
					}
				}

				.card-body {
					margin-bottom: 20rpx;

					.industry-desc {
						font-size: 26rpx;
						color: #666;
						line-height: 1.5;
					}
				}

				.schools-indicator {
					display: flex;
					flex-direction: column;
					margin-top: 20rpx;
					border-top: 1px dashed rgba(255, 165, 0, 0.2);
					padding-top: 20rpx;

					.schools-label {
						font-size: 24rpx;
						color: #999;
						margin-bottom: 10rpx;
					}

					.schools-value {
						display: flex;
						flex-wrap: wrap;

						.school-item {
							font-size: 28rpx;
							color: #FF8C00;
							font-weight: 500;
							max-width: 100%;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}

						.no-schools {
							font-size: 28rpx;
							color: #999;
							font-style: italic;
						}
					}
				}

				.salary-indicator {
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding-top: 16rpx;
					border-top: 1px dashed rgba(255, 165, 0, 0.3);

					.salary-label {
						font-size: 24rpx;
						color: #999;
					}

					.salary-value {
						font-size: 28rpx;
						color: #FF8510;
						font-weight: bold;
						background: linear-gradient(90deg, #FFD700, #FFA500);
						background-clip: text;
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;
					}
				}
			}
		}

		/* 底部按钮样式 */
		.action-buttons {
			padding: 20rpx 0 40rpx;

			.primary-button {
				background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
				border-radius: 50rpx;
				height: 90rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				color: #FFFFFF;
				font-size: 32rpx;
				font-weight: bold;
				box-shadow: 0 8rpx 16rpx rgba(255, 165, 0, 0.3);
				transition: all 0.3s ease;
				animation: pulse 2s infinite;

				&:active {
					transform: scale(0.98);
					box-shadow: 0 4rpx 8rpx rgba(255, 165, 0, 0.3);
				}

				.button-text {
					margin-right: 10rpx;
				}

				.button-icon {
					width: 32rpx;
					height: 32rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					image {
						width: 100%;
						height: auto;
					}
				}
			}
		}

		/* 浮动按钮样式 */
		.floating-action-button {
			position: fixed;
			right: 30rpx;
			bottom: 180rpx;
			background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
			border-radius: 50rpx;
			height: 90rpx;
			padding: 0 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #FFFFFF;
			font-size: 32rpx;
			font-weight: bold;
			box-shadow: 0 8rpx 16rpx rgba(255, 165, 0, 0.3);
			transition: all 0.3s ease;
			animation: pulse 2s infinite;
			z-index: 99;

			&:active {
				transform: scale(0.95);
				box-shadow: 0 4rpx 8rpx rgba(255, 165, 0, 0.3);
			}

			.button-text {
				margin-right: 10rpx;
			}

			.button-icon {
				width: 32rpx;
				height: 32rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				image {
					width: 100%;
					height: auto;
				}
			}
		}
	}
}
