<template>
  <view class="gold-bowl-container">
    <!-- 加载动画 -->
    <view class="loading-container" v-if="loading">
      <view class="loading-spinner">
        <view class="loading-circle"></view>
        <view class="loading-text">加载中</view>
      </view>
    </view>

    <!-- 固定头部和概览区 -->
    <view class="fixed-header-area">
      <!-- 现代化头部设计 -->
      <view class="header" :style="{ 'padding-top': titleTop + 'px' }">
        <view class="header-content">
          <view class="title-container">
            <text class="title">金饭碗</text>
            <text class="subtitle">高薪就业行业导航</text>
          </view>
          <view class="header-icon">
            <image
              :src="imgUrl + '/qdkbm/newimage/fhui/gold-bowl-icon.png'"
              mode="widthFix"
            ></image>
          </view>
        </view>
      </view>
      <!-- 热门行业概览 -->
      <view class="section-header">
        <view class="header-left">
          <view class="section-title">
            <view class="title-icon"></view>
            <text>热门高薪行业</text>
          </view>
          <view class="section-subtitle">选择行业，发现高薪就业机会</view>
        </view>
      </view>
    </view>

    <!-- 主要内容区 -->
    <view class="main-content">
      <!-- 行业列表 -->
      <view class="industry-list">
        <view
          v-for="(item, index) in industryList"
          :key="index"
          class="industry-card"
          @tap="navigateToDetail(item)"
          :style="{ 'animation-delay': index * 0.1 + 's' }"
        >
          <view class="card-header">
            <view class="industry-icon">
              <view class="icon-text">{{ getKeyChar(item.name) }}</view>
            </view>
            <text class="industry-name">{{ item.name }}</text>
          </view>
          <view class="card-body" v-if="item.remark">
            <text class="industry-desc">{{ item.remark }}</text>
          </view>
          <view class="schools-indicator">
            <text class="schools-label">热门对口院校</text>
            <view class="schools-value">
              <text v-if="item.schools && Array.isArray(item.schools) && item.schools.length > 0" class="school-item">{{ item.schools.slice(0, 2).join('、') }}{{ item.schools.length > 2 ? '等' : '' }}</text>
              <text v-else class="no-schools">暂无对口院校</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部导航和对话框 -->
    <tabBar :current-page="4"></tabBar>
    <myDialog
      :showbox="showbox"
      @closebox="closebox"
      @openVip="openVip"
    ></myDialog>
  </view>
</template>

<script>
import myDialog from "@/components/dialog.vue";
import tabBar from "@/components/tabBar.vue";
import loadingPopup from "@/components/loading-popup/loading-popup.vue";
import "@/pages/goldBowl/gold-bowl-styles.scss";
export default {
  components: {
    tabBar,
    myDialog,
    loadingPopup,
  },
  data() {
    return {
      showbox: false,
      loading: false,
      info: {},
      titleTop2: 0,
      imgUrl: this.$base.uploadImgUrl,
      titleTop: 0,
      viewCount: 0,
      lastViewDate: "",
      popupContent: "",
      industryList: [],
      schoolsList: [],
      showSchoolsPopup: false,
    };
  },
  onLoad() {
    // 获取本地存储的查看次数和最后查看日期
    const today = new Date().toDateString();
    const storedData = uni.getStorageSync("trendViewData") || {
      count: 0,
      date: "",
    };
    if (storedData.date !== today) {
      // 如果是新的一天，重置计数
      this.viewCount = 0;
      this.lastViewDate = today;
      uni.setStorageSync("trendViewData", {
        count: 0,
        date: today,
      });
    } else {
      this.viewCount = storedData.count;
      this.lastViewDate = storedData.date;
    }
    this.getIndexPage();
  },
  onReady() {
    let res = uni.getMenuButtonBoundingClientRect();
    this.titleTop = res.top;
    uni
      .createSelectorQuery()
      .select(".header")
      .boundingClientRect((res2) => {
        console.log(res2);
        this.titleTop2 = res2.height;
      })
      .exec();
  },
  methods: {
    // 从行业名称中提取关键字符
    getKeyChar(name) {
      if (!name) return "行";

      // 特殊处理一些行业名称
      if (name.includes("中芯国际")) return "芯";
      if (name.includes("中国中车")) return "车";
      if (name.includes("华为")) return "华";
      if (name.includes("核工业")) return "核";
      if (name.includes("国家能源")) return "能";
      if (name.includes("中国电子")) return "电";
      if (name.includes("船舶")) return "船";
      if (name.includes("航天")) return "航";
      if (name.includes("邮政")) return "邮";
      if (name.includes("银行")) return "银";
      if (name.includes("警察")) return "警";
      if (name.includes("军士")) return "军";
      if (name.includes("医学生")) return "医";
      if (name.includes("师范生")) return "师";
      if (name.includes("石油")) return "油";
      if (name.includes("铁路")) return "铁";
      if (name.includes("烟草")) return "烟";
      if (name.includes("电网")) return "电";

      // 默认使用中文名称的第二个字（如"中国xx"取第三个字）
      if (name.startsWith("中国") && name.length > 2) {
        return name.charAt(2);
      }

      // 默认使用第一个字符
      return name.charAt(0);
    },
    getIndexPage() {
      this.loading = true;
      this.$apis
        .getemploymentdir({
          pageNo: 1,
          pageSize: 100,
        })
        .then((res) => {
          if (res.code == 0) {
            this.industryList = res.data.list;
            // 获取每个行业的对口院校
            this.industryList.forEach(item => {
              this.getIndustrySchools(item);
            });
            setTimeout(() => {
              this.loading = false;
            }, 600);
          } else {
            setTimeout(() => {
              this.loading = false;
            }, 600);
          }
        })
        .catch((err) => {
          setTimeout(() => {
            this.loading = false;
          }, 600);
        });
    },
    closebox(val) {
      this.showbox = val;
    },
    openbox() {
      this.showbox = true;
    },
    // 点击行业项直接导航到详情页
    async navigateToDetail(item) {
      // 检查是否是会员
      let token = uni.getStorageSync("token");
      if (token) {
        let res = await this.$store.dispatch("getuseInfo");
        const isVip = uni.getStorageSync("isVip") || false;
        this.viewCount = uni.getStorageSync("tys") || 0;
        if (isVip) {
          uni.navigateTo({
            url: "/pages/goldBowl/list?dirName=" + item.name + "&dirId=" + item.id,
          });
        } else {
          if (this.viewCount <= 0) {
            this.showbox = true;
          } else {
            this.viewCount--;
            uni.setStorageSync("tys", this.viewCount);
            uni.navigateTo({
              url: "/pages/goldBowl/list?dirName=" + item.name + "&dirId=" + item.id,
            });
          }
        }
      } else {
        // 尝试自动登录
        let loginRes = await this.$store.dispatch('gologinPage')
        if (loginRes) {
          // 自动登录成功，重新调用本方法
          this.navigateToDetail(item)
        } else {
          // 自动登录失败，不做任何处理
          // 用户可以继续使用应用，但无法查看详情
        }
      }
    },
    confirmPopup() {
      this.$refs.popup.close();
    },
    closePopup() {
      this.$refs.popup.close();
    },
    openVip() {
      uni.navigateTo({
        url: "/pages/vip/index",
      });
      this.showbox = false;
    },
    // 获取行业对口院校
    getIndustrySchools(item) {
      // 先初始化为空数组，确保类型一致
      this.$set(item, 'schools', []);

      this.$apis.getemploymentdirGet({
        id: item.id
      }).then((res) => {
        if (res.code == 0 && res.data && res.data.schools && typeof res.data.schools === 'string') {
          try {
            // 分割逗号分隔的学校名称
            const schoolNames = res.data.schools.split(',');
            // 过滤掉空值并去除空格
            const filteredSchools = schoolNames.filter(name => name && name.trim().length > 0)
                                              .map(name => name.trim());
            this.$set(item, 'schools', filteredSchools);
          } catch (error) {
            console.error('处理学校数据出错:', error);
            this.$set(item, 'schools', []);
          }
        } else {
          this.$set(item, 'schools', []);
        }
      }).catch((error) => {
        console.error('获取学校数据出错:', error);
        this.$set(item, 'schools', []);
      });
    },
    // 根据行业索引生成薪资范围
    getSalaryRange(index) {
      // 为不同行业生成不同的薪资范围
      const baseSalaries = [
        "12K-25K",
        "15K-30K",
        "18K-35K",
        "20K-40K",
        "25K-45K",
        "30K-50K",
      ];

      // 使用模运算确保即使行业数量超过基础薪资数组长度也能正常工作
      const salaryIndex = index % baseSalaries.length;
      return baseSalaries[salaryIndex];
    },
    // 关闭加载弹窗
    closepopbox() {
      this.showpopbox = false;
    },
    // 关闭弹窗并返回上一页
    backWithPopupClose() {
      this.showpopbox = false;
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss">
@-webkit-keyframes countloading {
  0% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

@keyframes countloading {
  0% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

@-webkit-keyframes progress-active {
  0% {
    opacity: 0.1;
    transform: translateX(-100%) scaleX(0);
  }
  20% {
    opacity: 0.5;
    transform: translateX(-100%) scaleX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(0) scaleX(1);
  }
}

@keyframes progress-active {
  0% {
    opacity: 0.1;
    transform: translateX(-100%) scaleX(0);
  }
  20% {
    opacity: 0.5;
    transform: translateX(-100%) scaleX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(0) scaleX(1);
  }
}

.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .loading-spinner {
    width: 50rpx;
    height: 50rpx;
    // border: 8rpx solid #c8a178;
    // border-top-color: transparent;
    // border-radius: 50%;
    animation: countloading 1s ease infinite;
    // animation:progress-active 2.4s cubic-bezier(.23,1,.32,1) infinite;
    background-image: url("https://pic.kefeichangduo.top/qdkbm/newimage/fhui/waiting.png");
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: contain;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.trend-container {
  position: relative;
  padding-bottom: 30rpx;

  .head {
    position: relative;
    z-index: 10;
  }

  .bg {
    position: absolute;
    left: 0;
    top: 0;
    background-repeat: no-repeat;
    background-size: 100% auto;
    width: 100%;
    min-height: 100vh;
  }

  .industry-list {
    padding: 30rpx 35rpx;
    display: flex;
    padding-bottom: 200rpx;
    flex-direction: column;
    gap: 30rpx;

    .industry-item {
      padding: 40rpx 30rpx;
      background-color: #ffffff;
      border-radius: 20rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 4rpx 12rpx rgba(188, 106, 29, 0.15);
      transition: all 0.3s;
      position: relative;
      overflow: hidden;
      animation: fadeInUp 0.5s ease forwards;
      animation-delay: calc(0.1s * var(--i, 0));
      opacity: 0;
      transform: translateY(20rpx);

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 6rpx;
        background-image: linear-gradient(to bottom, #ff8510, #ffbd73);
        opacity: 0;
        transition: opacity 0.3s;
      }

      &:active {
        transform: scale(0.98);
        box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
      }

      .item-icon {
        width: 60rpx;
        height: 60rpx;
        margin-right: 20rpx;
        border-radius: 12rpx;
        overflow: hidden;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;

        .icon-bg {
          width: 100%;
          height: 100%;
          background-image: linear-gradient(135deg, #ff8510 0%, #ffbd73 100%);
          border-radius: 12rpx;
          opacity: 0.8;
          transition: opacity 0.3s;
        }
      }

      .item-content {
        flex: 1;
        overflow: hidden;

        .item-text {
          font-size: 32rpx;
          font-weight: 700;
          color: #333333;
          display: block;
        }

        .item-desc {
          font-size: 24rpx;
          color: #666;
          margin-top: 10rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }
    }
  }

  .industry-icon {
    width: 60rpx;
    height: 60rpx;
    border-radius: 12rpx;
    overflow: hidden;
    margin-right: 20rpx;

    .icon-text {
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #ffd700 0%, #ffa500 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ffffff;
      font-size: 28rpx;
      font-weight: bold;
    }
  }
  .popup-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 30rpx;
    padding: 0 30rpx;

    .popup-btn {
      width: 200rpx;
      height: 70rpx;
      border-radius: 35rpx;
      font-size: 28rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      &.cancel {
        background-color: #f5f5f5;
        color: #666;
      }

      &.confirm {
        background-image: linear-gradient(180deg, #ff8510 0, #ffbd73 100%);
        color: #ffffff;
      }
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
