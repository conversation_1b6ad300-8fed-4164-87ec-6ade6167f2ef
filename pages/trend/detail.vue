<template>
	<view class="trend-detail-container">
		<view class="head" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="left" @tap="back">
					<view class="itemList">
						<view class="item">
							<button>
								<image style="width:20rpx" :src="imgUrl+'/qdkbm/newimage/fhui/back.png'">
								</image>
							</button>
						</view>
					</view>
				</view>
				<view class="text">{{title?title:''}}行业趋势</view>
			</view>
		</view>

		<view class="content" style="padding-top: 20rpx;">
			<view class="none" v-if="!myhtml">
				暂无数据
			</view>
			<view class="industry-content" v-if="myhtml">
				<mpHtml :content="myhtml" :tagStyle="mpHtmlStyle"></mpHtml>
			</view>
		</view>

		<view class="bottom-buttons">
			<block v-if="!selectedProvince.choiceContent">


				<button class="btn qa-btn" @tap="openQA">
					<image class="btn-icon" :src="imgUrl+'/qdkbm/newimage/fhui/logos4.png'">
					</image>

				</button>
				<button class="btn province-btn" @tap="showProvincePopup = true">
					<text>{{selectedProvince.choiceContent || '按省份查看相关院校'}}</text>
				</button>
			</block>
			<block v-if="selectedProvince.choiceContent">
				<button class="btn province-btn" @tap="openQA">
					<text>智能问答</text>
				</button>
			</block>


			<!-- <button class="btn download-btn" @tap="downloadContent">
				<image class="btn-icon" :src="imgUrl+'/qdkbm/newimage/fhui/icon-download.png'">
				</image>
			</button> -->
		</view>

		<loading-popup :show="showpopbox" :imgUrl="imgUrl" @close="closepopbox" @back="backWithPopupClose"></loading-popup>

		<u-popup :round="8" mode="center" :safe-area-inset-bottom="false" closeOnClickOverlay :show="showBackPopup"
			@close="closeBackPopup">
			<view class="mybox">
				<view class="tit">提示</view>
				<view class="desc">当前为收费页面，您确定已了解全部内容，直接返回吗？</view>
				<view class="btnlist" style="display: flex;">
					<view class="btn">
						<button style="width:220rpx" @tap="confirmBack">确认</button>
					</view>
					<view class="btn" style="margin:0 20rpx">
						<button
							style="width:220rpx;color:#FF5500;border:2rpx solid #FF5500;background-image: linear-gradient(180deg, #fff 0, #fff 100%);"
							class="btns" @tap="closeBackPopup">关闭</button>
					</view>
				</view>
			</view>
		</u-popup>

		<u-popup safeAreaInsetBottom closeable :show="showProvincePopup" :closeable="false" :round="10" mode="bottom"
			@close="closeProvincePopup">
			<view class="cityPart">
				<view class="title">请选择高考省份
					<view class="icon" @tap="closeProvincePopup">
						<u-icon name="close" color="#000000" size="24"></u-icon>
					</view>
				</view>
				<view class="cityList">
					<view class="item" @tap="selectProvince(item)" :class="selectedProvince.id === item.id?'active':''"
						v-for="(item,index) in provinces" :key="index">
						{{item.choiceContent}}
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import mpHtml from '@/uni_modules/mp-html/components/mp-html/mp-html.vue'
	import LoadingPopup from '@/components/loading-popup/loading-popup.vue'
	export default {
		components: {
			mpHtml,
			LoadingPopup
		},
		data() {
			return {
				loading: false,
				title: "",
				mytitle: "",
				myhtml: "",
				industryId: "",
				imgUrl: this.$base.uploadImgUrl,
				titleTop: 0,
				titleTop2: 0,
				showpopbox: false,
				showProvincePopup: false,
				selectedProvince: {},
				dsCus: "",
				version: "",
				mpHtmlStyle: {
					h3: 'font-size: 36rpx; font-weight: 700; color: #FF8510; margin: 30rpx 0 20rpx; padding: 16rpx 20rpx; border-left: 8rpx solid #FF8510; background-color: rgba(255, 133, 16, 0.05); border-radius: 0 8rpx 8rpx 0;',
					p: 'font-size: 30rpx; line-height: 1.8; color: #333; margin-bottom: 20rpx; text-align: justify;',
					ol: 'padding-left: 40rpx; margin: 20rpx 0;',
					ul: 'padding-left: 40rpx; margin: 20rpx 0;',
					li: 'position: relative; margin-bottom: 16rpx; padding-left: 10rpx; font-size: 30rpx; line-height: 1.6; color: #333;',
					strong: 'color: #FF5B03; font-weight: 700;',
					a: 'color: #209BFF; text-decoration: underline;',
					hr: 'border: none; height: 2rpx; background: linear-gradient(to right, transparent, rgba(255, 133, 16, 0.5), transparent); margin: 30rpx 0;'
				},
				provinces: [{
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 1,
					"questionId": 1,
					"choiceContent": "北京",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 2,
					"questionId": 1,
					"choiceContent": "天津",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 3,
					"questionId": 1,
					"choiceContent": "上海",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 4,
					"questionId": 1,
					"choiceContent": "重庆",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 5,
					"questionId": 1,
					"choiceContent": "河北",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 6,
					"questionId": 1,
					"choiceContent": "山西",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 7,
					"questionId": 1,
					"choiceContent": "辽宁",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 8,
					"questionId": 1,
					"choiceContent": "吉林",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 9,
					"questionId": 1,
					"choiceContent": "黑龙江",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 10,
					"questionId": 1,
					"choiceContent": "江苏",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 11,
					"questionId": 1,
					"choiceContent": "浙江",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 12,
					"questionId": 1,
					"choiceContent": "安徽",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 13,
					"questionId": 1,
					"choiceContent": "福建",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 14,
					"questionId": 1,
					"choiceContent": "江西",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 15,
					"questionId": 1,
					"choiceContent": "山东",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 16,
					"questionId": 1,
					"choiceContent": "河南",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 17,
					"questionId": 1,
					"choiceContent": "湖北",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 18,
					"questionId": 1,
					"choiceContent": "湖南",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 19,
					"questionId": 1,
					"choiceContent": "广东",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 20,
					"questionId": 1,
					"choiceContent": "海南",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 21,
					"questionId": 1,
					"choiceContent": "四川",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 22,
					"questionId": 1,
					"choiceContent": "贵州",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 23,
					"questionId": 1,
					"choiceContent": "云南",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 24,
					"questionId": 1,
					"choiceContent": "陕西",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 25,
					"questionId": 1,
					"choiceContent": "甘肃",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 26,
					"questionId": 1,
					"choiceContent": "青海",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 27,
					"questionId": 1,
					"choiceContent": "台湾",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 28,
					"questionId": 1,
					"choiceContent": "内蒙古",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 29,
					"questionId": 1,
					"choiceContent": "广西",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 30,
					"questionId": 1,
					"choiceContent": "西藏",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 31,
					"questionId": 1,
					"choiceContent": "宁夏",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 32,
					"questionId": 1,
					"choiceContent": "新疆",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 33,
					"questionId": 1,
					"choiceContent": "香港",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}, {
					"createTime": 1742905924000,
					"updateTime": 1742905924000,
					"creator": "admin",
					"updater": "admin",
					"deleted": false,
					"id": 34,
					"questionId": 1,
					"choiceContent": "澳门",
					"sort": 1,
					"type": 1,
					"imageUrl": null
				}],
			}
		},
		onLoad(options) {
			if (options.title) {
				this.mytitle = options.title
				this.title = this.mytitle
			}
			if (options.id) {
				this.industryId = options.id
			}
		},
		onShow(){
			if (this.industryId) {
				this.getReport()
			}
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top;
			uni.createSelectorQuery().select('.head').boundingClientRect((res2) => {
				this.titleTop2 = res2.height + this.titleTop
			}).exec()
		},
		methods: {
			getReport() {
				// uni.showLoading({
				// 	title:"玩命加载中..."
				// })
				this.showpopbox = true
				this.$apis.indexquestionContent({
					questionId: this.industryId,
					version: this.version,
					dsCus: this.dsCus
				}).then((res) => {
					if (res.code == 0) {
						// uni.hideLoading()
						this.showpopbox = false
						if (res.data.list) {
							this.myhtml = res.data.list[0]?.content
						} else {
							this.myhtml = ''
						}
					}
				}).catch((err) => {
					this.showpopbox = false
					// uni.hideLoading()
				})
			},
			back() {
				if (this.selectedProvince.choiceContent) {
					// If province is selected, clear it and refresh the current page
					this.selectedProvince = {}
					this.title = this.mytitle
					this.dsCus = ""
					this.version = ""
					this.myhtml = ""
					this.getReport()
				} else {
					// If no province is selected, navigate back to previous page
					uni.navigateBack({
						delta: 1
					});
				}
			},
			closeBackPopup() {
				this.showBackPopup = false;
			},
			confirmBack() {
				this.closeBackPopup();
				if (this.selectedProvince.choiceContent) {
					// If province is selected, clear it and refresh the current page
					this.selectedProvince = {}
					this.title = this.mytitle
					this.dsCus = ""
					this.version = ""
					this.myhtml = ""
					this.getReport()
				} else {
					// If no province is selected, navigate back to previous page
					uni.navigateBack({
						delta: 1
					});
				}
			},
			openQA() {
				uni.navigateTo({
					url: '/pages_chat/chat/chat'
				});
			},
			downloadContent() {
				uni.showLoading({
					title: '复制中...'
				});

				// TODO: 实现下载逻辑


				setTimeout(() => {
					uni.hideLoading();

					uni.setClipboardData({
						data: this.myhtml,
						success: function(res) {
							uni.showToast({
								title: "复制成功",
								icon: "success",
							});
						},
					});

					// uni.showToast({
					// 	title: '下载成功',
					// 	icon: 'success'
					// });
				}, 1500);
			},
			closepopbox() {
				this.showpopbox = false
			},
			// 关闭弹窗并返回上一页
			backWithPopupClose() {
				this.showpopbox = false;
				uni.navigateBack({
					delta: 1
				});
			},
			openpopbox() {
				this.showpopbox = true
			},
			closeProvincePopup() {
				this.showProvincePopup = false;
			},
			selectProvince(province) {
				this.selectedProvince = province;
				this.dsCus = this.selectedProvince.choiceContent
				this.title = this.selectedProvince.choiceContent + this.mytitle
				this.version = 1
				this.myhtml = ""
				this.closeProvincePopup();
				// 选择省份后加载内容
				this.getReport()
			}
		}
	}
</script>
<style>
	page {
		background: #FFFBF3;
	}
</style>
<style lang="scss">
	.closeicon {
		position: absolute;
		right: -25rpx;
		top: 5rpx;

		image {
			width: 64rpx;
			height: 64rpx;
		}
	}

	.picbox {
		background-repeat: no-repeat;
		background-size: 100% 100%;
		width: 600rpx;
	}

	.myboxs {
		margin: 50rpx 30rpx 20rpx 30rpx;
		height: 320rpx;
		flex-direction: column;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;

		.tit2 {
			margin-bottom: 20rpx;
			color: #AA7248;
			margin-top: 30rpx;
			font-size: 42rpx;
			font-weight: 700;
		}

		.cont {
			margin-bottom: 20rpx;

			text {
				font-weight: 700;
				letter-spacing: 0rpx;
				font-size: 26rpx;
				color: #AA7248;
				line-height: 42rpx;

				.bold {
					color: #e6702b;
					margin: 0 4rpx;
				}
			}
		}

		.jiazaizhong {
			margin-bottom: 30rpx;
		}

		.btn-actions {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			width: 100%;
			padding: 0 20rpx;

			.action-btn {
				font-size: 28rpx;
				height: 70rpx;
				line-height: 70rpx;
				padding: 0 30rpx;
				border-radius: 35rpx;
				margin: 0 10rpx;
				box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
				transition: all 0.3s ease;

				&::after {
					border: none;
				}

				&:active {
					transform: scale(0.95);
				}

				&.cancel-btn {
					background-color: #F5F5F5;
					color: #666666;
				}

				&.back-btn {
					background-image: linear-gradient(90deg, #FF8C38 0, #FD5819 100%);
					color: #FFFFFF;
				}
			}
		}

		.btn {
			button {
				color: #fff;
				padding: 5rpx 80rpx;
				border-radius: 70rpx;
				font-size: 32rpx;
				background-image: linear-gradient(180deg, #FF8C38 0, #FD5819 100%);

				&::after {
					border: none;
				}
			}
		}
	}

	.none {
		text-align: center;
		display: flex;
		padding: 180rpx 0;
		justify-content: center;
		align-items: center;
		font-size: 32rpx;
		color: #666;
	}

	.cityPart {
		padding: 40rpx 20rpx;

		.title {
			font-weight: 400;
			font-size: 32rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #666;
			position: relative;
			margin-bottom: 35rpx;

			.icon {
				font-weight: 700;
				position: absolute;
				right: 5rpx;
				top: -10rpx;
				color: #000;
			}
		}

		.cityList {
			max-height: 780rpx;
			padding-bottom: 30rpx;
			display: flex;
			flex-direction: row;
			flex-wrap: wrap;
			overflow-y: auto;

			.item {
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0 10rpx;
				margin-bottom: 20rpx;
				width: calc(25% - 20rpx);
				background: #f1f1f1;
				color: #333;
				height: 72rpx;
				line-height: 72rpx;
				font-size: 28rpx;
				border-radius: 12rpx;

				&.active {
					color: #fff;
					background: #FF8918;
				}

				&:active {
					transform: scale(0.98);
				}
			}
		}
	}

	.mybox {
		flex-direction: column;
		padding: 40rpx 60rpx 60rpx 60rpx;
		width: 480rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.tit {
			font-weight: 700;
			font-size: 32rpx;
			color: #000;
			margin-bottom: 40rpx;
		}

		.desc {
			font-size: 28rpx;
			color: #FF5500;
			text-align: justify;
			padding: 0 20rpx;
			margin-bottom: 40rpx;
		}

		.btnlist {
			display: flex;
			flex-direction: row;
			width: 100%;

			.btn {
				flex: 1;
				margin: 0 10rpx;

				button {
					font-size: 28rpx;
					padding: 0;
					height: 78rpx;
					line-height: 78rpx;
					color: #fff;
					font-weight: 700;
					background-image: linear-gradient(180deg, #FF8510 0, #FFBD73 100%);
					border-radius: 50rpx;

					&.btns {
						border: 2rpx solid #FF8510;
						height: 72rpx;
						line-height: 72rpx;
						color: #FF8510;
						background-image: linear-gradient(180deg, #fff 0, #fff 100%);
					}

					&::after {
						border: none;
					}
				}
			}
		}
	}

	.trend-detail-container {
		padding-bottom: 120rpx;

		.content {
			padding: 30rpx;

			.industry-title {
				font-size: 36rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 30rpx;
				text-align: center;
			}

			.industry-content {
				line-height: 1.6;
			}
		}

		.bottom-buttons {
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			display: flex;
			justify-content: space-between;
			padding: 20rpx 20rpx;
			background-color: #FFFFFF;
			box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

			.btn {

				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 40rpx;
				font-size: 24rpx;

				white-space: nowrap;

				.btn-icon {
					width: 70rpx;
					height: 70rpx;
				}

				&.qa-btn {
					margin: 0;
					padding: 0;
				}

				&.province-btn {
					background-image: linear-gradient(180deg, #FF8510 0, #FFBD73 100%);
					flex: 1;
					margin: 0 20rpx;
					color: #fff;
					font-size: 32rpx;
					border: none;
				}

				&.download-btn {
					background-color: transparent;
					color: #333;
					width: auto;
					border: none;
					padding: 0;
					margin: 0;
				}

				&::after {
					border: none;
				}
			}
		}
	}
</style>