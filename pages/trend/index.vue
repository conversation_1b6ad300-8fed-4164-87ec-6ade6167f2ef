<template>
	<view class="trend-container">
		<view class="head" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header-content">
				<view class="back-button" @tap="back">
					<image class="back-icon" :src="imgUrl+'/qdkbm/newimage/fhui/back.png'"></image>
				</view>
				<view class="page-title">
					<text>行业趋势</text>
					<view class="title-decoration"></view>
				</view>
				<view class="placeholder-right"></view>
			</view>
		</view>
		<view class="loading-mask" v-if="loading">
			<view class="loading-spinner"></view>
		</view>
		<view class="bg" :style="{'background-image':'url('+imgUrl+'/qdkbm/newimage/fhui/member-bg.png)'}">
			<view class="industry-list" :style="{'padding-top':titleTop2+'px'}">
				<view
					v-for="(item, index) in industryList"
					:key="index"
					class="industry-item"
					:class="{
						'selected': selectedIndex === item.id,
						'card-color-1': index % 5 === 0,
						'card-color-2': index % 5 === 1,
						'card-color-3': index % 5 === 2,
						'card-color-4': index % 5 === 3,
						'card-color-5': index % 5 === 4
					}"
					@tap="handleItemClick(item)"
				>
					<view class="card-decoration"></view>
					<view class="item-content">
						<view class="item-icon">
							<text class="icon-number">{{index + 1}}</text>
						</view>
						<text class="item-text">{{item.question}}</text>
						<text v-if="item.answer" class="item-desc">{{item.answer}}</text>
					</view>
					<view class="card-shine" v-if="selectedIndex === item.id"></view>
				</view>
			</view>

			<!-- 底部查看详情按钮 -->
			<view class="bottom-btn">
				<view class="view-btn" @tap="viewDetails(info)">
					<text>查看详情</text>
					<view class="btn-icon"></view>
				</view>
			</view>
		</view>

		<myDialog :showbox="showbox" @closebox="closebox" @openVip="openVip"></myDialog>
	</view>
</template>

<script>
	import myDialog from '@/components/dialog.vue'
	export default {
		components:{
			myDialog
		},
		data() {
			return {
				showBackPopup:true,
				showbox:false,
				loading:false,
				info:{},
				titleTop2: 0,
				imgUrl: this.$base.uploadImgUrl,
				titleTop: 0,
				selectedIndex: 0,
				viewCount: 0,
				lastViewDate: '',
				popupContent: '',
				industryList: [
					// {id:1,name:"低空经济",img:"/static/img/myicons6.png",desc:"低空经济崛起，职教人才助力"},
					// {id:2,name:"银发经济",img:"/static/img/myicons1.png",desc:"发教育兴起，老年生活添彩"},
					// {id:3,name:"航空航天",img:"/static/img/myicons2.png",desc:"航天产业发展，高校培育人才"},
					// {id:4,name:"人工智能",img:"/static/img/myicons3.png",desc:"AI融入教育，驱动教学变革"},
					// {id:5,name:"AI芯片",img:"/static/img/myicons4.png",desc:"AI芯片发展，赋能教育智能"},
					// {id:6,name:"生物医疗",img:"/static/img/myicons5.png",desc:"生物医疗进步，教育培养精英"}
				]
			}
		},
		onLoad() {
			// 获取本地存储的查看次数和最后查看日期
			const today = new Date().toDateString();
			const storedData = uni.getStorageSync('trendViewData') || { count: 0, date: '' };

			if (storedData.date !== today) {
				// 如果是新的一天，重置计数
				this.viewCount = 0;
				this.lastViewDate = today;
				uni.setStorageSync('trendViewData', { count: 0, date: today });
			} else {
				this.viewCount = storedData.count;
				this.lastViewDate = storedData.date;
			}
			this.getIndexPage()
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top;
			uni.createSelectorQuery().select('.head').boundingClientRect((res2) => {
				console.log(res2)
				this.titleTop2 = res2.height + this.titleTop
			}).exec()
		},
		methods: {
			getIndexPage(){
				this.loading = true
				this.$apis.indexpage({
					parentType:1,
					pageNo:1,
					pageSize:20
				}).then((res)=>{
					if(res.code == 0){
						let arr = res.data.list.sort((a, b) => a.id - b.id);
						this.industryList = arr
						setTimeout(()=>{
							this.loading = false
						},600)
					} else{
						setTimeout(()=>{
							this.loading = false
						},600)
					}
				}).catch((err)=>{
					setTimeout(()=>{
						this.loading = false
					},600)
				})

			},
			closebox(val){
				this.showbox = val
			},
			openbox(){
				this.showbox = true
			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			},
			selectIndustry(item) {
				this.selectedIndex = item.id;
				this.info = item
			},

			// 处理卡片点击事件 - 只选中卡片，不直接跳转
			handleItemClick(item) {
				// 只选中该项，不直接查看详情
				this.selectIndustry(item);
			},
			async viewDetails(item) {
				// 检查是否是会员
				let token = uni.getStorageSync('token')
				if(token){
					let res = await this.$store.dispatch('getuseInfo')
					const isVip = uni.getStorageSync('isVip') || false;
					this.viewCount = uni.getStorageSync('tys') || 0
					if(isVip){
						if(this.info.id){
							uni.navigateTo({
								url: '/pages/trend/detail?id=' + this.info.id + '&title=' + this.info.question
							});
						} else{
							uni.showToast({
								title:'请选择一个行业',
								icon:'none'
							})
						}
					} else{
						if (this.viewCount <= 0) {
							this.showbox = true
						} else{
							this.viewCount--
							uni.setStorageSync('tys',this.viewCount)
							if(this.info.id){
								uni.navigateTo({
									url: '/pages/trend/detail?id=' + this.info.id + '&title=' + this.info.question
								});
							} else{
								uni.showToast({
									title:'请选择一个行业',
									icon:'none'
								})
							}
						}
					}
				} else{
					// 尝试自动登录
					let loginRes = await this.$store.dispatch('gologinPage')
					if (loginRes) {
						// 自动登录成功，重新调用本方法
						this.viewDetails(item)
					} else {
						// 自动登录失败，不做任何处理
						// 用户可以继续使用应用，但无法查看详情
					}
				}

			},
			confirmPopup() {
				this.$refs.popup.close();
			},
			closePopup() {
				this.$refs.popup.close();
			},
			openVip() {
				uni.navigateTo({
					url: '/pages/vip/index'
				});
				this.showbox = false
			}
		}
	}
</script>

<style lang="scss">
	@-webkit-keyframes countloading{0%{transform:rotate(180deg)}100%{transform:rotate(0deg)}}

	@keyframes countloading{0%{transform:rotate(180deg)}100%{transform:rotate(0deg)}}

	@-webkit-keyframes progress-active{0%{opacity:.1;transform:translateX(-100%) scaleX(0)}20%{opacity:.5;transform:translateX(-100%) scaleX(0)}100%{opacity:0;transform:translateX(0) scaleX(1)}}
	@keyframes progress-active{0%{opacity:.1;transform:translateX(-100%) scaleX(0)}20%{opacity:.5;transform:translateX(-100%) scaleX(0)}100%{opacity:0;transform:translateX(0) scaleX(1)}}

	@keyframes float {
		0% {
			transform: translateY(0px);
		}
		50% {
			transform: translateY(-6px);
		}
		100% {
			transform: translateY(0px);
		}
	}


	.loading-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(255, 255, 255, 0.9);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;

		.loading-spinner {
			width: 80rpx;
			height: 80rpx;
			animation: countloading 1s ease infinite;
			background-image: url('https://pic.kefeichangduo.top/qdkbm/newimage/fhui/waiting.png');
			background-position: 50%;
			background-repeat: no-repeat;
			background-size: contain;
			filter: drop-shadow(0 4rpx 8rpx rgba(255, 133, 16, 0.3));
		}
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(360deg);
		}
	}

	/* 添加新的动画效果 */
	@keyframes shine {
		0% {
			background-position: -100% 0;
		}
		100% {
			background-position: 200% 0;
		}
	}

	@keyframes float-subtle {
		0% {
			transform: translateY(0);
		}
		50% {
			transform: translateY(-8rpx);
		}
		100% {
			transform: translateY(0);
		}
	}

	@keyframes pulse {
		0% {
			transform: scale(1);
			opacity: 0.6;
		}
		50% {
			transform: scale(1.1);
			opacity: 0.9;
		}
		100% {
			transform: scale(1);
			opacity: 0.6;
		}
	}

	.trend-container {
		position: relative;
		padding-bottom: 120rpx;

		.head {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			z-index: 100;
			background: linear-gradient(135deg, rgba(255, 133, 16, 0.9), rgba(255, 189, 115, 0.9));
			backdrop-filter: blur(10px);
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
			border-bottom-left-radius: 15rpx;
			border-bottom-right-radius: 15rpx;

			.header-content {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 15rpx 30rpx;

				.back-button {
					width: 50rpx;
					height: 50rpx;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					background-color: rgba(255, 255, 255, 0.3);
					transition: all 0.3s ease;

					&:active {
						transform: scale(0.95);
					}

					.back-icon {
						width: 20rpx;
						height: 20rpx;
					}
				}

				.page-title {
					display: flex;
					flex-direction: column;
					align-items: center;

					text {
						font-size: 32rpx;
						font-weight: 600;
						color: #FFFFFF;
					}

					.title-decoration {
						width: 30rpx;
						height: 4rpx;
						background: #FFFFFF;
						border-radius: 2rpx;
						margin-top: 6rpx;
					}
				}

				.placeholder-right {
					width: 50rpx;
				}
			}
		}

		.bg{
			position: absolute;
			left: 0;
			top: 0;
			background-repeat: no-repeat;
			background-size: 100% auto;
			width: 100%;
			min-height: 100vh;
		}

		.industry-list {
			padding: 15rpx 20rpx;
			display: grid;
			grid-template-columns: repeat(auto-fill, minmax(200rpx, 1fr));
			padding-bottom: 100rpx;
			gap: 15rpx;

			.industry-item {
				padding: 15rpx 12rpx;
				background-color: #FFFFFF;
				border-radius: 20rpx;
				display: flex;
				align-items: center;
				box-shadow: 0 6rpx 16rpx rgba(188, 106, 29, 0.1);
				transition: all 0.3s ease;
				position: relative;
				overflow: hidden;
				height: 130rpx;
				border: 1px solid rgba(255, 133, 16, 0.1);
				cursor: pointer;
				transform: translateZ(0);

				// 卡片装饰元素
				.card-decoration {
					position: absolute;
					top: -15rpx;
					right: -15rpx;
					width: 80rpx;
					height: 80rpx;
					border-radius: 50%;
					background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
					opacity: 0.6;
					z-index: 0;
				}

				// 不同底色的卡片
				&.card-color-1 {
					background-color: #FFF8F0;
					border-left: 3px solid #FF8510;
				}

				&.card-color-2 {
					background-color: #F0F8FF;
					border-left: 3px solid #4A90E2;
				}

				&.card-color-3 {
					background-color: #F5FFF0;
					border-left: 3px solid #7ED321;
				}

				&.card-color-4 {
					background-color: #FFF0F5;
					border-left: 3px solid #E55D87;
				}

				&.card-color-5 {
					background-color: #F0FFFF;
					border-left: 3px solid #50E3C2;
				}

				&::before {
					content: '';
					position: absolute;
					top: 0;
					left: 0;
					width: 8rpx;
					height: 100%;
					background: linear-gradient(180deg, #FF8510 0, #FFBD73 100%);
					opacity: 0;
					transition: opacity 0.3s ease;
				}

				&.selected {
					box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
					transform: translateY(-6rpx);
					animation: float-subtle 3s ease-in-out infinite;
					border: 2rpx solid;

					&::before {
						opacity: 1;
					}

					.item-text {
						font-weight: 800;
					}

					// 选中状态下的装饰效果
					.card-decoration {
						opacity: 0.9;
						animation: pulse 2s infinite;
					}

					// 选中状态下保持各自的颜色
					&.card-color-1 {
						background-color: #FFF5E6;
						border-color: #FF8510;
						.item-text { color: #FF8510; }
					}

					&.card-color-2 {
						background-color: #E6F2FF;
						border-color: #4A90E2;
						.item-text { color: #4A90E2; }
					}

					&.card-color-3 {
						background-color: #EFFFEA;
						border-color: #7ED321;
						.item-text { color: #7ED321; }
					}

					&.card-color-4 {
						background-color: #FFE6EF;
						border-color: #E55D87;
						.item-text { color: #E55D87; }
					}

					&.card-color-5 {
						background-color: #E6FFFC;
						border-color: #50E3C2;
						.item-text { color: #50E3C2; }
					}

					// 添加选中指示器
					&::after {
						content: '';
						position: absolute;
						top: 8rpx;
						right: 8rpx;
						width: 12rpx;
						height: 12rpx;
						border-radius: 50%;
						background-color: currentColor;
						box-shadow: 0 0 8rpx currentColor;
					}
				}

				&:active {
					transform: scale(0.97);
					box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
				}

				.item-icon {
					width: 48rpx;
					height: 48rpx;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-bottom: 8rpx;
					margin-left: auto;
					margin-right: auto;
					flex-shrink: 0;
					box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
					z-index: 1;

					// 根据卡片颜色变化图标背景 - 使用浅色背景
					.card-color-1 & {
						background: #FFEDDC;
						border: 2rpx solid #FF8510;
					}

					.card-color-2 & {
						background: #E1F0FF;
						border: 2rpx solid #4A90E2;
					}

					.card-color-3 & {
						background: #EAFFDC;
						border: 2rpx solid #7ED321;
					}

					.card-color-4 & {
						background: #FFE1EC;
						border: 2rpx solid #E55D87;
					}

					.card-color-5 & {
						background: #DFFFF9;
						border: 2rpx solid #50E3C2;
					}

					.icon-number {
						color: #000000;
						font-size: 26rpx;
						font-weight: 800;
						letter-spacing: -1px;
					}
				}

				.item-content {
					display: flex;
					flex-direction: column;
					align-items: center;
					gap: 5rpx;
					flex: 1;
					padding: 0 8rpx;
					text-align: center;
				}

				.item-text {
					font-size: 28rpx;
					font-weight: 700;
					color: #333333;
					line-height: 1.2;
					transition: color 0.3s ease;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
				}

				.item-desc {
					font-size: 22rpx;
					color: #888888;
					line-height: 1.2;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 1;
					-webkit-box-orient: vertical;
				}

				.card-shine {
					position: absolute;
					top: 0;
					left: 0;
					width: 100%;
					height: 100%;
					background: linear-gradient(
						90deg,
						transparent,
						rgba(255, 255, 255, 0.4),
						transparent
					);
					background-size: 200% 100%;
					animation: shine 1.5s infinite linear;
					pointer-events: none;
					z-index: 1;
				}

				.dot {
					width: 44rpx;
					height: 44rpx;
					border-radius: 50%;
					border: 2rpx solid #E6E6E6;
					transition: all 0.3s ease;
					position: relative;
					display: flex;
					align-items: center;
					justify-content: center;
					flex-shrink: 0;
					margin-left: 20rpx;

					&::after {
						content: '';
						width: 20rpx;
						height: 20rpx;
						border-radius: 50%;
						background-color: #E6E6E6;
						transition: all 0.3s ease;
					}

					&.selected {
						border-color: #FF8918;
						transform: scale(1.1);

						&::after {
							background-color: #FF8918;
							transform: scale(1.2);
						}
					}

					.ripple {
						position: absolute;
						width: 100%;
						height: 100%;
						border-radius: 50%;
						background-color: rgba(255, 137, 24, 0.3);
						transform: scale(0);
						animation: ripple 1.5s ease-out infinite;
					}

					@keyframes ripple {
						to {
							transform: scale(2.5);
							opacity: 0;
						}
					}
				}
			}
		}

		.bottom-btn {
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			padding: 20rpx;

			padding-bottom: calc(constant(safe-area-inset-bottom) + 20rpx);
			padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
			background-color: rgba(255, 255, 255, 0.95);
			backdrop-filter: blur(10px);
			box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
			border-top-left-radius: 30rpx;
			border-top-right-radius: 30rpx;
			transition: transform 0.3s ease;
			z-index: 99;

			.view-btn {
				width: 100%;
				height: 80rpx;
				background-image: linear-gradient(135deg, #FF8510 0%, #FFBD73 100%);
				color: #FFFFFF;
				font-size: 30rpx;
				border-radius: 40rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-weight: 600;
				box-shadow: 0 8rpx 16rpx rgba(255, 133, 16, 0.3);
				transition: all 0.3s ease;
				position: relative;
				overflow: hidden;
				animation: pulse 2s infinite;

				&::before {
					content: '';
					position: absolute;
					top: 0;
					left: 0;
					width: 100%;
					height: 100%;
					background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
					background-size: 200% 100%;
					transform: translateX(-100%);
					transition: transform 0s;
				}

				&:active {
					transform: scale(0.98);
					box-shadow: 0 4rpx 8rpx rgba(255, 133, 16, 0.2);

					&::before {
						transform: translateX(100%);
						transition: transform 0.5s ease-in-out;
					}
				}

				text {
					margin-right: 10rpx;
				}

				.btn-icon {
					width: 14rpx;
					height: 14rpx;
					border-top: 2rpx solid #fff;
					border-right: 2rpx solid #fff;
					transform: rotate(45deg);
					margin-left: 6rpx;
					transition: transform 0.3s ease;
				}

				&::after {
					border: none
				}
			}
		}

		.popup-buttons {
			display: flex;
			justify-content: space-between;
			margin-top: 30rpx;
			padding: 0 30rpx;

			.popup-btn {
				width: 200rpx;
				height: 70rpx;
				border-radius: 35rpx;
				font-size: 28rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				&.cancel {
					background-color: #F5F5F5;
					color: #666;
				}

				&.confirm {
					background-image: linear-gradient(180deg, #FF8510 0, #FFBD73 100%);
					color: #FFFFFF;
				}
			}
		}
	}
</style>