<template>
	<view class="trend-container">
		<view class="head" style="background: none;" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="left" @tap="back">
					<view class="itemList">
						<view class="item">
							<button>
								<image style="width:20rpx"
									:src="imgUrl+'/qdkbm/newimage/fhui/back.png'">
								</image>
							</button>
						</view>
					</view>
				</view>
				<view class="text" style="color: #333;">行业趋势</view>
			</view>
		</view>
		<view class="loading-mask" v-if="loading">
			<view class="loading-spinner"></view>
		</view>
		<view class="bg" :style="{'background-image':'url('+imgUrl+'/qdkbm/newimage/fhui/member-bg.png)'}">
			<view class="industry-list" :style="{'padding-top':titleTop2+'px'}">
				<view 
					v-for="(item, index) in industryList" 
					:key="index"
					class="industry-item"
					:class="{'selected': selectedIndex === item.id}"
					@tap="selectIndustry(item)"
				>
					<text class="item-text">{{item.question}}</text>
					<view class="dot" :class="{'selected': selectedIndex === item.id}"></view>
				</view>
			</view>

			<view class="bottom-btn">
				<button class="view-btn" @tap="viewDetails">查看</button>
			</view>
		</view>
	
		<myDialog :showbox="showbox" @closebox="closebox" @openVip="openVip"></myDialog>
	</view>
</template>

<script>
	import myDialog from '@/components/dialog.vue'
	export default {
		components:{
			myDialog
		},
		data() {
			return {
				showBackPopup:true,
				showbox:false,
				loading:false,
				info:{},
				titleTop2: 0,
				imgUrl: this.$base.uploadImgUrl,
				titleTop: 0,
				selectedIndex: 0,
				viewCount: 0,
				lastViewDate: '',
				popupContent: '',
				industryList: [
					// {id:1,name:"低空经济",img:"/static/img/myicons6.png",desc:"低空经济崛起，职教人才助力"},
					// {id:2,name:"银发经济",img:"/static/img/myicons1.png",desc:"发教育兴起，老年生活添彩"},
					// {id:3,name:"航空航天",img:"/static/img/myicons2.png",desc:"航天产业发展，高校培育人才"},
					// {id:4,name:"人工智能",img:"/static/img/myicons3.png",desc:"AI融入教育，驱动教学变革"},
					// {id:5,name:"AI芯片",img:"/static/img/myicons4.png",desc:"AI芯片发展，赋能教育智能"},
					// {id:6,name:"生物医疗",img:"/static/img/myicons5.png",desc:"生物医疗进步，教育培养精英"}
				]
			}
		},
		onLoad() {
			// 获取本地存储的查看次数和最后查看日期
			const today = new Date().toDateString();
			const storedData = uni.getStorageSync('trendViewData') || { count: 0, date: '' };
			
			if (storedData.date !== today) {
				// 如果是新的一天，重置计数
				this.viewCount = 0;
				this.lastViewDate = today;
				uni.setStorageSync('trendViewData', { count: 0, date: today });
			} else {
				this.viewCount = storedData.count;
				this.lastViewDate = storedData.date;
			}
			this.getIndexPage()
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top;
			uni.createSelectorQuery().select('.head').boundingClientRect((res2) => {
				console.log(res2)
				this.titleTop2 = res2.height + this.titleTop
			}).exec()
		},
		methods: {
			getIndexPage(){
				this.loading = true
				this.$apis.indexpage({
					parentType:1,
					pageNo:1,
					pageSize:20
				}).then((res)=>{
					if(res.code == 0){
						let arr = res.data.list.sort((a, b) => a.id - b.id);
						this.industryList = arr
						setTimeout(()=>{
							this.loading = false
						},600)
					} else{
						setTimeout(()=>{
							this.loading = false
						},600)
					}
				}).catch((err)=>{
					setTimeout(()=>{
						this.loading = false
					},600)
				})
				
			},
			closebox(val){
				this.showbox = val
			},
			openbox(){
				this.showbox = true
			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			},
			selectIndustry(item) {
				this.selectedIndex = item.id;
				this.info = item
			},
			async viewDetails(item) {
				// 检查是否是会员
				let token = uni.getStorageSync('token')
				if(token){
					let res = await this.$store.dispatch('getuseInfo')
					const isVip = uni.getStorageSync('isVip') || false;
					this.viewCount = uni.getStorageSync('tys') || 0
					if(isVip){
						if(this.info.id){
							uni.navigateTo({
								url: '/pages/trend/detail?id=' + this.info.id + '&title=' + this.info.question
							});
						} else{
							uni.showToast({
								title:'请选择一个行业',
								icon:'none'
							})
						}
					} else{
						if (this.viewCount <= 0) {
							this.showbox = true
						} else{
							this.viewCount--
							uni.setStorageSync('tys',this.viewCount)
							if(this.info.id){
								uni.navigateTo({
									url: '/pages/trend/detail?id=' + this.info.id + '&title=' + this.info.question 
								});
							} else{
								uni.showToast({
									title:'请选择一个行业',
									icon:'none'
								})
							}
						}
					}
				} else{
					uni.showModal({
						title: '提示',
						content: '您还未登录，请先登录',
						confirmText: '去登录',
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/login/login'
								});
							}
						}
					});
				}
				
			},
			confirmPopup() {
				this.$refs.popup.close();
			},
			closePopup() {
				this.$refs.popup.close();
			},
			openVip() {
				uni.navigateTo({
					url: '/pages/vip/index'
				});
				this.showbox = false
			}
		}
	}
</script>

<style lang="scss">
	@-webkit-keyframes countloading{0%{transform:rotate(180deg)}100%{transform:rotate(0deg)}}
	
	@keyframes countloading{0%{transform:rotate(180deg)}100%{transform:rotate(0deg)}}
	
	
	// .progress-active-loading{animation:progress-active 2.4s cubic-bezier(.23,1,.32,1) infinite;background-color:#fff;height:100%;position:absolute;width:100%;z-index:999}
	@-webkit-keyframes progress-active{0%{opacity:.1;transform:translateX(-100%) scaleX(0)}20%{opacity:.5;transform:translateX(-100%) scaleX(0)}100%{opacity:0;transform:translateX(0) scaleX(1)}}
	@keyframes progress-active{0%{opacity:.1;transform:translateX(-100%) scaleX(0)}20%{opacity:.5;transform:translateX(-100%) scaleX(0)}100%{opacity:0;transform:translateX(0) scaleX(1)}}
	
	
	.loading-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
	
		.loading-spinner {
			width: 50rpx;
			height: 50rpx;
			// border: 8rpx solid #c8a178;
			// border-top-color: transparent;
			// border-radius: 50%;
			 animation:countloading 1s ease infinite;
			// animation:progress-active 2.4s cubic-bezier(.23,1,.32,1) infinite;
			 background-image:url('https://pic.kefeichangduo.top/qdkbm/newimage/fhui/waiting.png');background-position:50%;background-repeat:no-repeat;background-size:contain;
		}
	}
	
	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
	
		100% {
			transform: rotate(360deg);
		}
	}
	
	.trend-container {
		position: relative;
		padding-bottom: 120rpx;
		
		.bg{
			position: absolute;
			left: 0;
			top: 0;
			background-repeat: no-repeat;
			background-size: 100% auto;
			width: 100%;
			min-height: 100vh;
		}
		
		.industry-list {
			padding: 30rpx 35rpx;
			display: flex;
			padding-bottom:200rpx;
			flex-direction: column;
			gap: 30rpx;
			
			.industry-item {
				padding: 50rpx 40rpx;
				background-color: #FFFFFF;
				border-radius: 20rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				box-shadow: 0 4rpx 12rpx rgba(188, 106, 29, 0.3);
				transition: all 0.3s;
				
				&:active {
					transform: scale(0.98);
					box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
				}
				
				.item-text {
					font-size: 32rpx;
					font-weight: 700;
					color: #333333;
				}
				
				.dot {
					width: 40rpx;
					height: 40rpx;
					border-radius: 50%;
					border: 2rpx solid #E6E6E6;
					transition: all 0.3s;
					position: relative;
					display: flex;
					align-items: center;
					justify-content: center;
					
					&::after {
						content: '';
						width: 20rpx;
						height: 20rpx;
						border-radius: 50%;
						background-color: #E6E6E6;
						transition: all 0.3s;
					}
					
					&.selected {
						border-color: #e6e6e6;
						
						&::after {
							background-color: #FF8918;
						}
					}
				}
			}
		}

		.bottom-btn {
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			padding: 30rpx;
			
			padding-bottom: calc(constant(safe-area-inset-bottom) + 30rpx);
			padding-bottom: calc(env(safe-area-inset-bottom) + 30rpx);
			background-color: #FFFFFF;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
			
			.view-btn {
				width: 100%;
				height: 88rpx;
				background-image: linear-gradient(180deg, #FF8510 0, #FFBD73 100%);
				color: #FFFFFF;
				font-size: 32rpx;
				border-radius: 44rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-weight: 500;
				
				&::after {
					border: none
				}
			}
		}

		.popup-buttons {
			display: flex;
			justify-content: space-between;
			margin-top: 30rpx;
			padding: 0 30rpx;
			
			.popup-btn {
				width: 200rpx;
				height: 70rpx;
				border-radius: 35rpx;
				font-size: 28rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				
				&.cancel {
					background-color: #F5F5F5;
					color: #666;
				}
				
				&.confirm {
					background-image: linear-gradient(180deg, #FF8510 0, #FFBD73 100%);
					color: #FFFFFF;
				}
			}
		}
	}
</style>