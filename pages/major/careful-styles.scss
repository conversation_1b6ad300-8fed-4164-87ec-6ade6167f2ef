/* 动画效果 */
@keyframes fadeIn {
	0% { opacity: 0; }
	100% { opacity: 1; }
}

@keyframes slideIn {
	0% { transform: translateY(-20rpx); opacity: 0; }
	100% { transform: translateY(0); opacity: 1; }
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

@keyframes countloading {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

@keyframes pulse {
	0% { transform: scale(1); }
	50% { transform: scale(1.05); }
	100% { transform: scale(1); }
}

.careful-container {
	background-color: #fffaed; // 淡黄色背景，与热门专业页面一致
	min-height: 100vh;
	animation: fadeIn 0.5s ease-in-out;

	/* 头部样式 - 暖色系主题 */
	.header {
		background: linear-gradient(135deg, #FF8918 0%, #FFA94D 100%);
		padding: 20rpx 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
		border-radius: 0 0 30rpx 30rpx;

		.header-content {
			display: flex;
			align-items: center;
			margin-bottom: 20rpx;
			position: relative;

			.back-button {
				width: 50rpx;
				height: 50rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				background: rgba(255,255,255,0.3);
				border-radius: 50%;
				margin-right: 20rpx;

				image {
					width: 20rpx;
					height: auto;
				}
			}

			.title {
				font-size: 36rpx;
				color: #FFFFFF;
				font-weight: bold;
			}

			.info-icon {
				position: absolute;
				right: 0;
				width: 50rpx;
				height: 50rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				background: rgba(255,255,255,0.3);
				border-radius: 50%;

				image {
					width: 30rpx;
					height: auto;
				}
			}
		}

		/* 搜索框样式 */
		.search-box {
			margin: 10rpx 0 20rpx;

			.search-input-wrapper {
				position: relative;
				background: rgba(255,255,255,0.9);
				border-radius: 40rpx;
				display: flex;
				align-items: center;
				padding: 0 20rpx;
				height: 80rpx;
				box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.05);

				.search-icon {
					width: 36rpx;
					height: auto;
					margin-right: 15rpx;
				}

				.search-input {
					flex: 1;
					height: 100%;
					font-size: 28rpx;
					color: #333;
				}

				.clear-icon {
					width: 32rpx;
					height: auto;
					padding: 10rpx;
				}
			}
		}
	}

	/* 警告横幅样式 */
	.warning-banner {
		background: rgba(255, 87, 34, 0.1);
		padding: 20rpx 30rpx;
		display: flex;
		align-items: center;
		margin: 0 20rpx 20rpx;
		border-radius: 12rpx;
		border-left: 8rpx solid #FF5722;
		animation: slideIn 0.5s ease;

		.warning-icon {
			width: 40rpx;
			height: auto;
			margin-right: 15rpx;
		}

		.warning-text {
			flex: 1;
			font-size: 28rpx;
			color: #FF5722;
		}

		.close-banner {
			width: 40rpx;
			height: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			image {
				width: 24rpx;
				height: auto;
			}
		}
	}

	/* 筛选标签区域与统计信息 */
	.filter-area {
		padding: 10rpx 20rpx;

		/* 筛选区头部 */
		.filter-header {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			padding: 10rpx 10rpx 20rpx;

			.counter {
				font-size: 28rpx;
				color: #666;

				.counter-highlight {
					color: #FF8918; // 暖色系主题
					font-weight: bold;
					margin: 0 5rpx;
				}
			}
		}

		.category-tabs {
			white-space: nowrap;
			padding: 10rpx 0;

			.category-tab {
				display: inline-block;
				padding: 15rpx 30rpx;
				margin-right: 20rpx;
				font-size: 28rpx;
				color: #666;
				background: #fff;
				border: 2rpx solid #E5E5E5;
				border-radius: 50rpx;
				transition: all 0.3s ease;

				&.active {
					border-color: #FF8918;
					background: #FF8918;
					color: #fff;
					box-shadow: 0 4rpx 8rpx rgba(255, 137, 24, 0.2);
				}
			}
		}
	}

	/* 专业列表样式 */
	.major-list {
		padding: 10rpx 30rpx;
		padding-bottom: 0; // Reduce bottom padding
		overflow-y: auto;

		.major-list-content {
			padding-bottom: 10rpx; // Reduce bottom padding

			.major-item {
				background: #FFFFFF;
				border-radius: 16rpx;
				padding: 30rpx;
				margin-bottom: 20rpx;
				display: flex;
				align-items: center;
				box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
				transition: all 0.3s ease;
				border-left: 4rpx solid #FF8918;
				animation: slideIn 0.5s ease-out;

				&:active {
					transform: scale(0.98);
				}

				.major-icon {
					width: 80rpx;
					height: 80rpx;
					background: rgba(255, 137, 24, 0.1);
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 20rpx;

					image {
						width: 40rpx;
						height: auto;
					}

					.major-avatar {
						font-size: 36rpx;
						font-weight: bold;
						color: #FF8918;
						text-align: center;
						width: 100%;
						height: 100%;
						display: flex;
						align-items: center;
						justify-content: center;
					}
				}

				.major-content {
					flex: 1;

					.major-name {
						font-size: 32rpx;
						color: #333;
						font-weight: 600;
						display: block;
						margin-bottom: 8rpx;
					}

					.major-tag {
						display: inline-block;
						font-size: 24rpx;
						color: #FF8918;
						background: rgba(255, 137, 24, 0.1);
						padding: 4rpx 16rpx;
						border-radius: 20rpx;
					}
				}

				.major-warning {
					width: 40rpx;
					height: 40rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 10rpx;

					image {
						width: 30rpx;
						height: auto;
					}
				}

				.major-arrow {
					width: 40rpx;
					height: 40rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					image {
						width: 24rpx;
						height: auto;
					}
				}
			}
		}
	}
}

/* 空状态样式 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;

	.empty-icon {
		width: 200rpx;
		height: auto;
		margin-bottom: 30rpx;
		opacity: 0.7;
	}

	.empty-text {
		font-size: 30rpx;
		color: #999;
	}
}

/* 加载状态样式 */
.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;

	.loading-content {
		background: #FFFFFF;
		border-radius: 16rpx;
		padding: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;

		.loading-spinner {
			width: 80rpx;
			height: auto;
			animation: spin 1.5s linear infinite;
			margin-bottom: 20rpx;
		}

		.loading-text {
			font-size: 28rpx;
			color: #666;
		}
	}
}

/* 提示弹窗样式 */
.tips-popup {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
	animation: fadeIn 0.3s ease;

	.tips-content {
		background: #FFFFFF;
		border-radius: 16rpx;
		width: 80%;
		max-width: 600rpx;
		overflow: hidden;

		.tips-header {
			padding: 30rpx;
			background: #3498db;
			color: #FFFFFF;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.tips-title {
				font-size: 32rpx;
				font-weight: bold;
			}

			.tips-close {
				width: 40rpx;
				height: 40rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				image {
					width: 24rpx;
					height: auto;
				}
			}
		}

		.tips-body {
			padding: 30rpx;

			text {
				font-size: 28rpx;
				color: #333;
				line-height: 1.6;
			}
		}

		.tips-footer {
			padding: 20rpx 30rpx 30rpx;
			display: flex;
			justify-content: center;

			.tips-button {
				background: #3498db;
				color: #FFFFFF;
				font-size: 28rpx;
				padding: 15rpx 40rpx;
				border-radius: 50rpx;
				box-shadow: 0 4rpx 8rpx rgba(52, 152, 219, 0.2);

				&::after {
					border: none;
				}
			}
		}
	}
}

/* QA容器样式 */
.qa-container {
	.search-box {
		margin-bottom: 30rpx;

		input {
			width: 100%;
			height: 80rpx;
			background: #f5f5f5;
			border-radius: 40rpx;
			padding: 0 30rpx;
			font-size: 28rpx;
		}
	}

	.category-tabs {
		display: flex;
		overflow-x: auto;
		margin: 0 20rpx 30rpx 20rpx;
		padding: 0 10rpx;
		white-space: nowrap;

		.category-tab {
			padding: 15rpx 30rpx;
			margin-right: 20rpx;
			font-size: 28rpx;
			color: #666;
			background: #fff;
			border: 2rpx solid #cecece;
			border-radius: 50rpx;

			&.active {
				border-color: #FF8918;
				background: #FF8918;
				color: #fff;
			}
		}
	}

	.qa-list {
		margin: 0 30rpx;
		padding-bottom: 50rpx;

		.qa-list-con {
			background: #fff;
		}

		.qa-item {
			.question {
				display: flex;
				align-items: flex-start;
				padding: 15rpx 0;

				// border-bottom:2rpx solid #e5e5e5;
				.q-mark {
					width: 40rpx;
					height: 40rpx;
					background: #4d6bfe;
					color: #fff;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 15rpx;
					font-size: 24rpx;
					font-weight: bold;
				}

				.q-text {
					flex: 1;
					font-size: 30rpx;
					font-weight: 700;
					color: #333;
				}
			}

			.answer {
				display: flex;
				align-items: flex-start;
				padding-top: 20rpx;
				border-top: 1rpx solid #eee;

				.a-mark {
					width: 40rpx;
					height: 40rpx;
					background: #41d996;
					color: #fff;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 15rpx;
					font-size: 24rpx;
					font-weight: bold;
				}

				.a-text {
					flex: 1;
					font-size: 28rpx;
					color: #666;
					line-height: 1.6;
				}
			}
		}
	}
}
