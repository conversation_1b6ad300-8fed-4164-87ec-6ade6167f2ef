<template>
	<view class="careful-container">
		<!-- 现代化的头部设计 - 使用蓝色系主题，表示谨慎、理性 -->
		<view class="header" :style="{'padding-top': titleTop + 'px'}">
			<view class="header-content">
				<view class="back-button" @tap="back">
					<image :src="imgUrl+'/qdkbm/newimage/fhui/back.png'" mode="widthFix"></image>
				</view>
				<text class="title">慎选专业</text>
				<view class="info-icon" @tap="showTips">
					<image :src="imgUrl+'/qdkbm/newimage/fhui/info.png'" mode="widthFix"></image>
				</view>
			</view>

			<!-- 搜索功能 -->
			<view class="search-box">
				<view class="search-input-wrapper">
					<image class="search-icon" :src="imgUrl+'/qdkbm/newimage/fhui/search.png'" mode="widthFix"></image>
					<input
						class="search-input"
						type="text"
						v-model="searchQuery"
						placeholder="搜索需谨慎选择的专业"
						confirm-type="search"
						@confirm="handleSearch"
					/>
					<image
						v-if="searchQuery"
						class="clear-icon"
						:src="imgUrl+'/qdkbm/newimage/fhui/close.png'"
						mode="widthFix"
						@tap="clearSearch"
					></image>
				</view>
			</view>
		</view>

		<!-- 筛选标签区与统计信息整合 -->
		<view class="filter-area">
			<!-- <view class="filter-header">
				<view class="counter">共<text class="counter-highlight">{{filteredQuestions.length}}</text>个专业</view>
			</view> -->
			<scroll-view class="category-tabs" scroll-x="true" scroll-with-animation :scroll-left="scrollLeft">
				<view
					v-for="(category, index) in categories"
					:key="index"
					class="category-tab"
					:class="{ active: currentCategory === category.id }"
					@tap="switchCategory(category.id, index)"
				>
					{{category.name}}
				</view>
			</scroll-view>
		</view>

		<!-- 专业列表 -->
		<view class="major-list" :style="{'height':'calc(100vh - '+height+'px)'}">
			<view class="major-list-content">
				<view class="major-item" v-for="(item, index) in filteredQuestions" :key="index" @tap="showAnswer(item)">
					<view class="major-icon">
						<view class="major-avatar">{{ item.question.charAt(0) }}</view>
					</view>
					<view class="major-content">
						<text class="major-name">{{item.question}}</text>
						<view class="major-tag" v-if="item.type">{{item.type}}</view>
					</view>
					<view class="major-warning">
						<image :src="imgUrl+'/qdkbm/newimage/fhui/warning-small.png'" mode="widthFix"></image>
					</view>
					<view class="major-arrow">
						<image :src="imgUrl+'/qdkbm/newimage/fhui/arrow-right.png'" mode="widthFix"></image>
					</view>
				</view>

				<!-- 空状态显示 -->
				<view class="empty-state" v-if="filteredQuestions.length === 0 && !loading">
					<image class="empty-icon" :src="imgUrl+'/qdkbm/newimage/fhui/empty.png'" mode="widthFix"></image>
					<text class="empty-text">没有找到相关专业</text>
				</view>
			</view>
		</view>

		<!-- 加载状态优化 -->
		<view class="loading-overlay" v-if="loading">
			<view class="loading-content">
				<image class="loading-spinner" :src="imgUrl+'/qdkbm/newimage/fhui/waiting.png'" mode="widthFix"></image>
				<text class="loading-text">加载中...</text>
			</view>
		</view>

		<!-- 提示弹窗 -->
		<view class="tips-popup" v-if="showTipsPopup">
			<view class="tips-content">
				<view class="tips-header">
					<text class="tips-title">慎选专业提示</text>
					<view class="tips-close" @tap="closeTips">
						<image :src="imgUrl+'/qdkbm/newimage/fhui/close.png'" mode="widthFix"></image>
					</view>
				</view>
				<view class="tips-body">
					<text>本页面列出了需要谨慎选择的专业，这些专业可能存在就业难度大、行业前景不明朗等问题。选择前请充分了解相关信息，理性决策。</text>
				</view>
				<view class="tips-footer">
					<button class="tips-button" @tap="closeTips">我知道了</button>
				</view>
			</view>
		</view>

		<!-- 弹窗组件 -->
		<myDialog :showbox="showbox" @closebox="closebox" @openVip="openVip"></myDialog>
		<loading-popup :show="showpopbox" :imgUrl="imgUrl" @close="closepopbox" @back="backWithPopupClose"></loading-popup>
		<tabBar></tabBar>


	</view>
</template>

<script>
	import myDialog from '@/components/dialog.vue'
	import LoadingPopup from '@/components/loading-popup/loading-popup.vue'
	export default {
		components: {
			myDialog,
			LoadingPopup,
			tabBar: () => import('@/components/tabBar.vue')
		},
		data() {
			return {
				imgUrl: this.$base.uploadImgUrl,
				showbox: false,
				showpopbox: false,
				showBanner: true,
				showTipsPopup: false,
				scrollLeft: 0,
				loading: true,
				titleTop: 0,
				height: 0,
				currentCategory: 'all',
				categories: [],
				questions: [],
				currentPage: 2,
				searchQuery: '', // 搜索查询文本
				isSearching: false // 是否正在搜索
			}
		},
		onLoad() {
			this.getList()
			setTimeout(() => {
				this.loading = false
			}, 800)
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top;

			// 计算各部分高度以确定内容区域高度
			setTimeout(() => {
				let query = uni.createSelectorQuery().in(this);
				query.selectAll('.header, .warning-banner, .stats-area, .filter-area').boundingClientRect(data => {
					let totalHeight = 0;
					data.forEach(item => {
						if (item) totalHeight += item.height;
					});
					// 减少底部间距，移除多余的空白区域
					this.height = totalHeight + 20 + 50; // 添加适当间距和底部导航栏高度
				}).exec();
			}, 300);
		},
		computed: {
			filteredQuestions() {
				// 首先按分类筛选
				let filtered = this.questions;
				if (this.currentCategory !== 'all') {
					filtered = filtered.filter(item => item.type === this.categories[this.currentCategory].name);
				}

				// 如果有搜索关键词，进一步筛选
				if (this.searchQuery && this.searchQuery.trim() !== '') {
					const query = this.searchQuery.trim().toLowerCase();
					filtered = filtered.filter(item => {
						// 在专业名称中搜索关键词
						return item.question.toLowerCase().includes(query);
					});
				}

				this.refresh()
				return filtered;
			}
		},
		methods: {
			// 关闭对话框
			closebox(val) {
				this.showbox = val
			},
			// 打开对话框
			openbox() {
				this.showbox = true
			},
			// 打开VIP页面
			openVip() {
				uni.navigateTo({
					url: '/pages/mine/vip'
				})
				this.showbox = false
			},
			// 关闭加载弹窗
			closepopbox() {
				this.showpopbox = false
			},
			// 关闭弹窗并返回上一页
			backWithPopupClose() {
				this.showpopbox = false
				setTimeout(() => {
					uni.navigateBack({
						delta: 1
					})
				}, 300)
			},
			// 显示提示信息
			showTips() {
				this.showTipsPopup = true
			},
			// 关闭提示信息
			closeTips() {
				this.showTipsPopup = false
			},
			// 关闭提示横幅
			closeBanner() {
				// 添加渐隐动画
				const banner = document.querySelector('.warning-banner')
				if (banner) {
					banner.style.opacity = '0'
					banner.style.transition = 'opacity 0.3s ease'

					setTimeout(() => {
						this.showBanner = false
					}, 300)
				} else {
					// 如果无法获取DOM元素，直接关闭
					this.showBanner = false
				}
			},
			// 搜索处理
			handleSearch() {
				this.isSearching = true
				// 搜索后自动关闭键盘
				uni.hideKeyboard()
			},
			// 清除搜索
			clearSearch() {
				this.searchQuery = ''
				this.isSearching = false
				uni.hideKeyboard()
			},
			// 刷新列表
			refresh() {
				setTimeout(() => {
					uni.$emit('refreshList')
				}, 100)
			},
			getList() {
				this.loading = true
				// 参照热门专业页面的实现方式
				this.$apis.indexpage({
					parentType: 4,
					pageNo: 1,
					pageSize: 1000
				}).then((res) => {
					console.log('原始返回数据:', res)

					if (res.code == 0) {
						console.log('成功获取数据，列表长度:', res.data.list.length)
						this.questions = res.data.list

						// 收集所有不同的分类
						let arr = []
						if (res.data.list.length > 0) {
							res.data.list.forEach(item => {
								if (!arr.includes(item.type)) {
									arr.push(item.type);
								}
							});
						}

						// 构建分类数组
						let newarr = []
						if (arr.length > 0) {
							arr.map((myitem, myindex) => {
								newarr.push({
									id: Number(myindex + 1),
									name: myitem
								})
							})
						}

						// 添加全部选项
						newarr.unshift({
							id: 'all',
							name: "全部"
						})
						this.categories = newarr
					} else {
						console.error('API返回错误:', res.msg || '未知错误')
						uni.showToast({
							title: res.msg || '获取数据失败',
							icon: 'none'
						})
					}

					// 无论成功失败都关闭加载状态
					this.loading = false
				}).catch(err => {
					console.error(err)
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none'
					})
					this.loading = false
				})
			},
			back() {
				uni.navigateBack()
			},

			handleSearch() {
				// 处理搜索逻辑
			},
			showAnswer(item) {
				// 显示加载弹窗
				this.showpopbox = true

				// 模拟加载
				setTimeout(() => {
					// 关闭加载弹窗
					this.closepopbox()

					// 跳转到详情页
					setTimeout(() => {
						uni.navigateTo({
							url: '/pages/major/sxdetail?id=' + item.id
						})
					}, 300)
				}, 800)
			},
			moveTo(index) {
				// 计算滚动位置
				let scrollWidth = 0
				let query = uni.createSelectorQuery().in(this)

				query.selectAll('.category-tab').boundingClientRect(data => {
					// 计算目标位置前所有tab的宽度总和
					for (let i = 0; i < index; i++) {
						if (data[i]) {
							scrollWidth += data[i].width + 20 // 20是margin-right
						}
					}

					// 设置滚动位置
					this.scrollLeft = scrollWidth
				}).exec()
			},
			switchCategory(categoryId, index) {
				this.currentCategory = categoryId
				this.moveTo(index)
			}
		}
	}
</script>

<style lang="scss">
	@import "./careful-styles.scss";
</style>
