<template>
	<view class="major-container">
		<!-- 现代化的头部设计 -->
		<view class="header" :style="{'padding-top': titleTop + 'px'}">
			<view class="header-content">
				<view class="back-button" @tap="back">
					<image :src="imgUrl+'/qdkbm/newimage/fhui/back.png'" mode="widthFix"></image>
				</view>
				<text class="title">热门专业</text>
			</view>

			<!-- 搜索功能 -->
			<view class="search-box">
				<view class="search-input-wrapper">
					<image class="search-icon" :src="imgUrl+'/qdkbm/newimage/fhui/search.png'" mode="widthFix"></image>
					<input
						class="search-input"
						type="text"
						v-model="searchQuery"
						placeholder="搜索感兴趣的专业"
						confirm-type="search"
						@confirm="handleSearch"
					/>
					<image
						v-if="searchQuery"
						class="clear-icon"
						:src="imgUrl+'/qdkbm/newimage/fhui/close.png'"
						mode="widthFix"
						@tap="clearSearch"
					></image>
				</view>
			</view>
		</view>

		<!-- 统计信息 -->
		<!-- <view class="stats-area">
			<view class="counter">共<text class="counter-highlight">{{filteredQuestions.length}}</text>个专业</view>
		</view> -->

		<!-- 筛选标签区 -->
		<view class="filter-area">
			<scroll-view class="category-tabs" scroll-x="true" scroll-with-animation :scroll-left="scrollLeft">
				<view
					v-for="(category, index) in categories"
					:key="index"
					class="category-tab"
					:class="{ active: currentCategory === category.id }"
					@tap="switchCategory(category.id, index)"
				>
					{{category.name}}
				</view>
			</scroll-view>
		</view>

		<!-- 专业列表 -->
		<view class="major-list" :style="{'height':'calc(100vh - '+height+'px)'}">
			<view class="major-list-content">
				<view class="major-item" v-for="(item, index) in filteredQuestions" :key="index" @tap="showAnswer(item)">
					<view class="major-icon" :style="{backgroundColor: getRandomColor(item.question)}">
						<text>{{getFirstChar(item.question)}}</text>
					</view>
					<view class="major-content">
						<text class="major-name">{{item.question}}</text>
						<view class="major-tag" v-if="item.type">{{item.type}}</view>
					</view>
					<view class="major-arrow">
						<image :src="imgUrl+'/qdkbm/newimage/fhui/arrow-right.png'" mode="widthFix"></image>
					</view>
				</view>

				<!-- 空状态显示 -->
				<view class="empty-state" v-if="filteredQuestions.length === 0 && !loading">
					<image class="empty-icon" :src="imgUrl+'/qdkbm/newimage/fhui/empty.png'" mode="widthFix"></image>
					<text class="empty-text">没有找到相关专业</text>
				</view>
			</view>
		</view>

		<!-- 加载状态优化 -->
		<view class="loading-overlay" v-if="loading">
			<view class="loading-content">
				<image class="loading-spinner" :src="imgUrl+'/qdkbm/newimage/fhui/waiting.png'" mode="widthFix"></image>
				<text class="loading-text">加载中...</text>
			</view>
		</view>

		<!-- 弹窗组件 -->
		<myDialog :showbox="showbox" @closebox="closebox" @openVip="openVip"></myDialog>
		<loading-popup :show="showpopbox" :imgUrl="imgUrl" @close="closepopbox" @back="backWithPopupClose"></loading-popup>
	</view>
</template>

<script>
	import myDialog from '@/components/dialog.vue'
import LoadingPopup from '@/components/loading-popup/loading-popup.vue'
	export default {
		components:{
			myDialog,
			LoadingPopup
		},
		data() {
			return {
				imgUrl:this.$base.uploadImgUrl,
				viewCount: 0,
				scrollLeft:0,
				showbox:false,
				showpopbox: false,
				loading: true,
				titleTop: 0,
				height: 0,
				currentCategory: 'all',
				categories: [],
				questions: [],
				searchQuery: '', // 搜索查询文本
				isSearching: false // 是否正在搜索
			}
		},
		onLoad() {
			this.getList()
			setTimeout(() => {
				this.loading = false
			}, 800)
		},
		onShow() {
			// 当页面显示时，确保内容生成中的弹窗被关闭
			this.showpopbox = false
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top;

			// 计算各部分高度以确定内容区域高度
			setTimeout(() => {
				let query = uni.createSelectorQuery().in(this);
				query.selectAll('.header, .stats-area, .filter-area').boundingClientRect(data => {
					let totalHeight = 0;
					data.forEach(item => {
						totalHeight += item.height;
					});
					this.height = totalHeight + 20; // 添加额外间距
				}).exec();
			}, 300);
		},
		computed: {
			filteredQuestions() {
				// 首先按分类筛选
				let filtered = this.questions;
				if (this.currentCategory !== 'all') {
					filtered = filtered.filter(item => item.type === this.categories[this.currentCategory].name);
				}

				// 如果有搜索关键词，进一步筛选
				if (this.searchQuery && this.searchQuery.trim() !== '') {
					const query = this.searchQuery.trim().toLowerCase();
					filtered = filtered.filter(item => {
						// 在专业名称中搜索关键词
						return item.question.toLowerCase().includes(query);
					});
				}

				this.refresh()
				return filtered;
			}
		},
		methods: {
			// 获取专业名称的第一个字符
			getFirstChar(text) {
				return text ? text.charAt(0) : '专';
			},
			// 根据专业名称生成一致的背景颜色
			getRandomColor(text) {
				if (!text) return '#ff9933';
				// 使用专业名称作为种子生成固定颜色
				let hash = 0;
				for (let i = 0; i < text.length; i++) {
					hash = text.charCodeAt(i) + ((hash << 5) - hash);
				}
				// 生成橙色系列颜色
				const h = 30; // 橙色色调
				const s = 70 + (hash % 20); // 70%-90%饱和度
				const l = 45 + (hash % 20); // 45%-65%亮度
				return `hsl(${h}, ${s}%, ${l}%)`;
			},
			// 关闭对话框
			closebox(val) {
				this.showbox = val
			},
			// 打开对话框
			openbox() {
				this.showbox = true
			},
			// 打开VIP页面
			openVip() {
				uni.navigateTo({
					url: '/pages/vip/index'
				});
				this.showbox = false
			},
			// 关闭加载弹窗
			closepopbox() {
				this.showpopbox = false
			},
			// 关闭弹窗并返回上一页
			backWithPopupClose() {
				this.showpopbox = false
				uni.navigateBack({
					delta: 1
				})
			},
			// 搜索处理
			handleSearch() {
				// 搜索已在计算属性中实现
				this.isSearching = !!this.searchQuery
			},
			// 清除搜索
			clearSearch() {
				this.searchQuery = ''
				this.isSearching = false
			},
			// 刷新列表
			refresh() {
				setTimeout(() => {
					this.loading = false
				}, 600)
			},
			getList() {
				this.$apis.indexpage({
					parentType: 3,
					pageNo:1,
					pageSize:1000
				}).then((res) => {
					if (res.code == 0) {
						this.questions = res.data.list
						let arr = []
						if (res.data.list.length > 0) {
							res.data.list.forEach(item => {
								if (!arr.includes(item.type)) {
									arr.push(item.type);
								}
							});
						}

						let newarr = []
						if (arr.length > 0) {
							arr.map((myitem, myindex) => {
								newarr.push({
									id: Number(myindex + 1),
									name: myitem
								})
							})
						}
						newarr.unshift({
							id: 'all',
							name: "全部"
						})
						this.categories = newarr
					}
				})

			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			},
			handleSearch() {},
			async showAnswer(item) {
				let token = uni.getStorageSync('token')
				if (token) {
					let res = await this.$store.dispatch('getuseInfo')
					const isVip = uni.getStorageSync('isVip') || false;
					this.viewCount = uni.getStorageSync('tys') || 0
					if (isVip) {
						// 直接导航到详情页，不显示弹窗
						uni.navigateTo({
							url: "/pages/major/detail?id=" + item.id + '&name=' + item.question
						});
					} else {
						if (this.viewCount <= 0) {
							this.openbox()
						} else {
							this.viewCount--
							uni.setStorageSync('tys', this.viewCount)
							// 直接导航到详情页，不显示弹窗
							uni.navigateTo({
								url: "/pages/major/detail?id=" + item.id + '&name=' + item.question
							});
						}
					}
				} else {
					uni.showModal({
						title: '提示',
						content: '您还未登录，请先登录',
						confirmText: '去登录',
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/login/login'
								});
							}
						}
					});
				}
			},
			moveTo(index) {
				const query = uni.createSelectorQuery().in(this)
				query
					.selectAll(`.category-tab`)
					.boundingClientRect(rect => {
						const {
							windowWidth
						} = uni.getSystemInfoSync();
						let width = 0

						for (let i = 0; i < index; i++) {
							width += rect[i].width
						}

						if (width > windowWidth / 2) {
							this.scrollLeft = width + rect[index].width / 2 - windowWidth / 2
						} else {
							this.scrollLeft = 0
						}
					}).exec()
			},
			switchCategory(categoryId,index) {
				this.loading = true
				this.questions = []
				this.moveTo(index)
				this.getList();
				this.currentCategory = categoryId;
			}
		}
	}
</script>

<style lang="scss">
	/* 动画效果 */
	@keyframes fadeIn {
		0% { opacity: 0; }
		100% { opacity: 1; }
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	@keyframes pulse {
		0% { transform: scale(1); }
		50% { transform: scale(1.05); }
		100% { transform: scale(1); }
	}

	.major-container {
		background-color: #fffaed;
		min-height: 100vh;
		animation: fadeIn 0.5s ease-in-out;

		/* 头部样式 */
		.header {
			background: linear-gradient(135deg, #FF8918 0%, #FFA94D 100%);
			padding: 20rpx 30rpx;
			box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
			border-radius: 0 0 30rpx 30rpx;

			.header-content {
				display: flex;
				align-items: center;
				margin-bottom: 20rpx;

				.back-button {
					width: 50rpx;
					height: 50rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					background: rgba(255,255,255,0.3);
					border-radius: 50%;
					margin-right: 20rpx;

					image {
						width: 20rpx;
						height: auto;
					}
				}

				.title {
					font-size: 36rpx;
					color: #FFFFFF;
					font-weight: bold;
				}
			}

			/* 搜索框样式 */
			.search-box {
				margin: 10rpx 0 20rpx;

				.search-input-wrapper {
					position: relative;
					background: rgba(255,255,255,0.9);
					border-radius: 40rpx;
					display: flex;
					align-items: center;
					padding: 0 20rpx;
					height: 80rpx;
					box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.05);

					.search-icon {
						width: 36rpx;
						height: auto;
						margin-right: 15rpx;
					}

					.search-input {
						flex: 1;
						height: 100%;
						font-size: 28rpx;
						color: #333;
					}

					.clear-icon {
						width: 32rpx;
						height: auto;
						padding: 10rpx;
					}
				}
			}
		}

		/* 统计区域样式 */
		.stats-area {
			padding: 20rpx 30rpx 10rpx;
			display: flex;
			justify-content: flex-end;

			.counter {
				font-size: 28rpx;
				color: #666;

				.counter-highlight {
					color: #FF8918;
					font-weight: bold;
					margin: 0 5rpx;
				}
			}
		}

		/* 筛选标签区域 */
		.filter-area {
			padding: 10rpx 20rpx;

			.category-tabs {
				white-space: nowrap;
				padding: 10rpx 0;

				.category-tab {
					display: inline-block;
					padding: 15rpx 30rpx;
					margin-right: 20rpx;
					font-size: 28rpx;
					color: #666;
					background: #fff;
					border: 2rpx solid #E5E5E5;
					border-radius: 50rpx;
					transition: all 0.3s ease;

					&.active {
						border-color: #FF8918;
						background: #FF8918;
						color: #fff;
						box-shadow: 0 4rpx 8rpx rgba(255, 137, 24, 0.2);
					}
				}
			}
		}

		/* 专业列表样式 */
		.major-list {
			padding: 10rpx 30rpx;
			overflow-y: auto;

			.major-list-content {
				padding-bottom: 30rpx;

				.major-item {
					background: #FFFFFF;
					border-radius: 16rpx;
					padding: 30rpx;
					margin-bottom: 20rpx;
					display: flex;
					align-items: center;
					box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
					transition: transform 0.3s ease;

					&:active {
						transform: scale(0.98);
					}

					.major-icon {
						width: 80rpx;
						height: 80rpx;
						background-color: #ff9933;
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-right: 20rpx;
						box-shadow: 0 2rpx 8rpx rgba(255, 137, 24, 0.2);

						text {
							color: #ffffff;
							font-size: 32rpx;
							font-weight: bold;
						}
					}

					.major-content {
						flex: 1;

						.major-name {
							font-size: 32rpx;
							color: #333;
							font-weight: 600;
							display: block;
							margin-bottom: 8rpx;
						}

						.major-tag {
							display: inline-block;
							font-size: 24rpx;
							color: #FF8918;
							background: rgba(255, 137, 24, 0.1);
							padding: 4rpx 16rpx;
							border-radius: 20rpx;
						}
					}

					.major-arrow {
						width: 40rpx;
						height: 40rpx;
						display: flex;
						align-items: center;
						justify-content: center;

						image {
							width: 24rpx;
							height: auto;
						}
					}
				}
			}

			/* 空状态样式 */
			.empty-state {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				padding: 100rpx 0;

				.empty-icon {
					width: 200rpx;
					height: auto;
					margin-bottom: 30rpx;
					opacity: 0.7;
				}

				.empty-text {
					font-size: 30rpx;
					color: #999;
				}
			}
		}

		/* 加载状态样式 */
		.loading-overlay {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0, 0, 0, 0.5);
			display: flex;
			align-items: center;
			justify-content: center;
			z-index: 9999;

			.loading-content {
				background: #FFFFFF;
				border-radius: 16rpx;
				padding: 40rpx;
				display: flex;
				flex-direction: column;
				align-items: center;

				.loading-spinner {
					width: 80rpx;
					height: auto;
					animation: spin 1.5s linear infinite;
					margin-bottom: 20rpx;
				}

				.loading-text {
					font-size: 28rpx;
					color: #666;
				}
			}
		}
	}
</style>