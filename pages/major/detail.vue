<template>
	<view class="trend-detail-container">
		<view class="head" style="background:#fff;" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="left" @tap="back">
					<view class="itemList">
						<view class="item">
							<button style="width: auto;color:#333;">
								<image v-if="source" :src="imgUrl+'/qdkbm/newimage/fhui/logos4.png'"
									style="width: 64rpx;margin-right:10rpx;" mode="widthFix"></image>
								<text v-if="source" style="font-size: 32rpx;">飞鸿AI志愿规划</text>
								<image v-if="!source" style="width:20rpx"
									:src="imgUrl+'/qdkbm/newimage/fhui/back.png'">
								</image>
							</button>
						</view>
					</view>
				</view>
				<view class="text" style="color:#333" :style="{'color':source?'transparent':''}">{{title?title:'热门专业'}}</view>
			</view>
		</view>

		<view class="content" style="padding-top: 10rpx;">
			<view class="mylist" v-if="myhtml&&!version">
				<view class="item" @tap="clickdanci"
					:style="{'background-image':'url('+imgUrl+'/qdkbm/newimage/fhui/myboxs11.png)'}">
					<view class="box11">
						<view class="left">
							<text>各分段学校推荐</text>
						</view>
						<view class="right">
							<image :src="imgUrl+'/qdkbm/newimage/fhui/icon-rights2.png'"></image>
						</view>
					</view>
				</view>
				<view class="item" @tap="clickqianjing"
					:style="{'background-image':'url('+imgUrl+'/qdkbm/newimage/fhui/myboxs12.png)'}">
					<view class="box11">
						<view class="left">
							<text>各分段就业前景</text>
						</view>
						<view class="right">
							<image :src="imgUrl+'/qdkbm/newimage/fhui/icon-rights2.png'"></image>
						</view>
					</view>
				</view>
			</view>
			<view class="contents">
				<view class="none" v-if="!myhtml">
					暂无数据
				</view>
				<view class="industry-content" v-if="myhtml">
					<mpHtml :content="myhtml" :tagStyle="mpHtmlStyle"></mpHtml>
				</view>
			</view>
		</view>

		<view class="bottom-buttons">
			<button class="btn province-btn" @tap="openQA">
				<text>智能问答</text>
			</button>
			<!-- <button class="btn download-btn" @tap="downloadContent">
				<image class="btn-icon" :src="imgUrl+'/qdkbm/newimage/fhui/icon-download.png'">
				</image>
			</button> -->
		</view>

		<loading-popup :show="showpopbox" :imgUrl="imgUrl" @close="closepopbox" @back="backWithPopupClose"></loading-popup>
		<u-popup :round="8" mode="center" :safe-area-inset-bottom="false" closeOnClickOverlay :show="showBackPopup"
			@close="closeBackPopup">
			<view class="mybox">
				<view class="tit">提示</view>
				<view class="desc">当前为收费页面，您确定已了解全部内容，直接返回吗？</view>
				<view class="btnlist" style="display: flex;">
					<view class="btn">
						<button style="width:220rpx" @tap="confirmBack">确认</button>
					</view>
					<view class="btn" style="margin:0 20rpx">
						<button
							style="width:220rpx;color:#FF5500;border:2rpx solid #FF5500;background-image: linear-gradient(180deg, #fff 0, #fff 100%);"
							class="btns" @tap="closeBackPopup">关闭</button>
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import mpHtml from '@/uni_modules/mp-html/components/mp-html/mp-html.vue'
	import LoadingPopup from '@/components/loading-popup/loading-popup.vue'
	export default {
		components: {
			mpHtml,
			LoadingPopup
		},
		data() {
			return {
				showpopbox: false,
				title: "",
				imgUrl: this.$base.uploadImgUrl,
				titleTop: 0,
				titleTop2: 0,
				industryName: '',
				content: '',
				industryId: '',
				myhtml: "",
				showBackPopup: false,
				showProvincePopup: false,
				selectedProvince: {},
				dsCus: "",
				source: "",
				version: "",
				mpHtmlStyle: {
					h3: 'font-size: 36rpx; font-weight: 700; color: #FF8510; margin: 30rpx 0 20rpx; padding: 16rpx 20rpx; border-left: 8rpx solid #FF8510; background-color: rgba(255, 133, 16, 0.05); border-radius: 0 8rpx 8rpx 0;',
					p: 'font-size: 30rpx; line-height: 1.8; color: #333; margin-bottom: 20rpx; text-align: justify;',
					ol: 'padding-left: 40rpx; margin: 20rpx 0;',
					ul: 'padding-left: 40rpx; margin: 20rpx 0;',
					li: 'position: relative; margin-bottom: 16rpx; padding-left: 10rpx; font-size: 30rpx; line-height: 1.6; color: #333;',
					strong: 'color: #FF5B03; font-weight: 700;',
					a: 'color: #209BFF; text-decoration: underline;',
					hr: 'border: none; height: 2rpx; background: linear-gradient(to right, transparent, rgba(255, 133, 16, 0.5), transparent); margin: 30rpx 0;',
					img: 'max-width: 100%; height: auto; margin: 10rpx auto; display: block;'
				}
			}
		},
		onLoad(options) {
			if (options.name) {
				this.title = options.name
			}
			if (options.id) {
				this.industryId = options.id
			}
		},
		onShow(){
			if (this.industryId) {
				this.getReport()
			}
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top;
			uni.createSelectorQuery().select('.head').boundingClientRect((res2) => {
				this.titleTop2 = res2.height + this.titleTop
			}).exec()
		},
		methods: {
			clickdanci() {
				this.version = 2
				this.dsCus = 2
				this.myhtml = ''
				this.getReport()
			},
			clickqianjing() {
				this.version = 3
				this.dsCus = 3
				this.myhtml = ''
				this.getReport()
			},
			getReport() {
				// uni.showLoading({
				// 	title:"玩命加载中..."
				// })
				this.showpopbox = true
				this.$apis.indexquestionContent({
					questionId: this.industryId,
					version: this.version,
					dsCus: this.dsCus
				}).then((res) => {
					if (res.code == 0) {
						// uni.hideLoading()
						this.showpopbox = false
						if (res.data.list) {
							this.myhtml = res.data.list[0].content
						} else {
							this.myhtml = ''
						}
					}
				}).catch((err) => {
					this.showpopbox = false
					// uni.hideLoading()
				})
			},
			back() {
				this.showBackPopup = true;
			},
			closeBackPopup() {
				this.showBackPopup = false;
			},
			confirmBack() {
				this.closeBackPopup();
				if (this.version) {
					this.version = ""
					this.dsCus = ""
					this.myhtml = ""
					this.getReport()
				} else {
					uni.navigateBack({
						delta: 1
					});
				}

			},
			openQA() {
				uni.navigateTo({
					url: '/pages_chat/chat/chat'
				});
			},
			downloadContent() {
				uni.showLoading({
					title: '复制中...'
				});
				setTimeout(() => {
					uni.hideLoading();
					uni.setClipboardData({
						data: this.myhtml,
						success: function(res) {
							console.log(res)
							uni.showToast({
								title: "复制成功",
								icon: "success",
							});
						},
						fail(err) {
							console.log(err)
						}
					});
					// uni.showToast({
					// 	title: '下载成功',
					// 	icon: 'success'
					// });
				}, 1500);
			},
			// 关闭弹窗
			closepopbox() {
				this.showpopbox = false;
			},
			// 关闭弹窗并返回上一页
			backWithPopupClose() {
				this.showpopbox = false;
				uni.navigateBack({
					delta: 1
				});
			}
		}
	}
</script>
<style>
	page {
		/* background: #FFFBF3; */
	}
</style>
<style lang="scss">
	.closeicon {
		position: absolute;
		right: -25rpx;
		top: 5rpx;

		image {
			width: 64rpx;
			height: 64rpx;
		}
	}

	.picbox {
		background-repeat: no-repeat;
		background-size: 100% 100%;
		width: 600rpx;
	}

	.myboxs {
		margin: 50rpx 30rpx 20rpx 30rpx;
		height: 320rpx;
		flex-direction: column;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;

		.tit2 {
			margin-bottom: 20rpx;
			color: #AA7248;
			margin-top: 30rpx;
			font-size: 42rpx;
			font-weight: 700;
		}

		.cont {
			margin-bottom: 20rpx;

			text {
				font-weight: 700;
				letter-spacing: 0rpx;
				font-size: 26rpx;
				color: #AA7248;
				line-height: 42rpx;

				.bold {
					color: #e6702b;
					margin: 0 4rpx;
				}
			}
		}

		.jiazaizhong {
			margin-bottom: 20rpx;
		}

		.btn {
			button {
				color: #fff;
				padding: 5rpx 80rpx;
				border-radius: 70rpx;
				font-size: 32rpx;
				background-image: linear-gradient(90deg, #FF8C38 0, #FD5819 100%);

				&::after {
					border: none;
				}
			}
		}
	}

	.contents {
		background: #FFFBF3;
		padding: 20rpx;
		border-radius: 20rpx;
		min-height: 60vh;
	}

	.mylist {
		margin: 0 -5rpx;
		display: flex;
		flex-direction: row;
		margin-bottom: 15rpx;

		.item {
			flex: 1;
			margin: 0 5rpx;
			background-repeat: no-repeat;
			width: 100%;
			background-size: 100% 100%;
			border-radius: 12rpx;
			transition: all 0.2s ease;

			&:active {
				transform: scale(0.98);
			}

			.box11 {
				padding: 15rpx 20rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.left {
					font-size: 28rpx;
					font-weight: 700;
					color: #000;
				}

				.right {
					image {
						width: 12rpx;
						height: 20rpx;
					}
				}
			}
		}
	}

	.none {
		text-align: center;
		display: flex;
		padding: 180rpx 0;
		justify-content: center;
		align-items: center;
		font-size: 32rpx;
		color: #666;
	}

	.cityPart {
		padding: 40rpx 20rpx;

		.title {
			font-weight: 700;
			font-size: 32rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #000;
			margin-bottom: 35rpx;
		}

		.cityList {
			max-height: 600rpx;
			padding-bottom: 30rpx;
			display: flex;
			flex-direction: row;
			flex-wrap: wrap;
			overflow-y: auto;

			.item {
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0 10rpx;
				margin-bottom: 20rpx;
				width: calc(25% - 20rpx);
				background: #efefef;
				height: 70rpx;
				line-height: 70rpx;
				font-size: 28rpx;
				border-radius: 8rpx;

				&.active {
					color: #fff;
					background: #ff7e17;
				}

				&:active {
					transform: scale(0.98);
				}
			}
		}
	}

	.mybox {
		flex-direction: column;
		padding: 40rpx 60rpx;
		width: 480rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.tit {
			font-weight: 700;
			font-size: 32rpx;
			color: #000;
			margin-bottom: 40rpx;
		}

		.desc {
			font-size: 28rpx;
			color: #FF5500;
			text-align: justify;
			padding: 0 20rpx;
			margin-bottom: 40rpx;
		}

		.btnlist {
			display: flex;
			flex-direction: row;
			width: 100%;

			.btn {
				flex: 1;
				margin: 0 10rpx;

				button {
					font-size: 28rpx;
					padding: 0;
					height: 78rpx;
					line-height: 78rpx;
					color: #fff;
					font-weight: 700;
					background-image: linear-gradient(180deg, #FF8510 0, #FFBD73 100%);
					border-radius: 50rpx;

					&.btns {
						border: 2rpx solid #FF8510;
						height: 72rpx;
						line-height: 72rpx;
						color: #FF8510;
						background-image: linear-gradient(180deg, #fff 0, #fff 100%);
					}

					&::after {
						border: none;
					}
				}
			}
		}
	}

	.trend-detail-container {

		padding-bottom: 120rpx;

		.content {
			padding: 15rpx 20rpx;

			.industry-title {
				font-size: 36rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 30rpx;
				text-align: center;
			}



			.industry-content {
				line-height: 1.6;
			}
		}

		.bottom-buttons {
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			display: flex;
			justify-content: space-between;
			padding: 20rpx 10rpx;
			background-color: #FFFFFF;
			box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

			.btn {

				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 40rpx;
				font-size: 24rpx;

				white-space: nowrap;

				.btn-icon {
					width: 70rpx;
					height: 70rpx;
				}

				&.qa-btn {
					margin: 0;
					padding: 0;
				}

				&.province-btn {
					background-image: linear-gradient(180deg, #FF8510 0, #FFBD73 100%);
					flex: 1;
					margin: 0 20rpx;
					color: #fff;
					font-size: 32rpx;
					border: none;
				}

				&.download-btn {
					background-color: transparent;
					color: #333;
					width: auto;
					border: none;
					padding: 0;
					margin: 0;
				}

				&::after {
					border: none;
				}
			}
		}
	}
</style>