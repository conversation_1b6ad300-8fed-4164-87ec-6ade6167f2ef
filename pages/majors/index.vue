<template>
	<view class="majors-container">
		<!-- 头部区域 -->
		<view class="header" :style="'padding-top:' + (titleTop) + 'px'">
			<view class="header-content">
				<view class="title">专业查询</view>
			</view>

			<!-- 搜索框 -->
			<view class="search-box">
				<view class="search-input-wrapper">
					<image class="search-icon" :src="imgUrl+'/qdkbm/newimage/fhui/icon-search4.png'"></image>
					<input
						class="search-input"
						type="text"
						v-model="searchKeyword"
						@input="handleInput"
						placeholder="请输入专业名称"
						placeholder-style="color: #999"
						confirm-type="search"
						@confirm="search"
					/>
					<image
						v-if="searchKeyword"
						class="clear-icon"
						src="https://pic.kefeichangduo.top/qdkbm/newimage/fhui/icon-clear.png"
						@tap="clearSearch"
					></image>
				</view>
			</view>

			<!-- 教育层次标签页 -->
			<view class="education-tabs">
				<view
					class="tab-item"
					:class="{active: currentEducationLevel === '本科(普通教育)'}"
					@tap="changeEducationLevel('本科(普通教育)')"
				>
					本科(普通)
				</view>
				<view
					class="tab-item"
					:class="{active: currentEducationLevel === '本科(职业教育)'}"
					@tap="changeEducationLevel('本科(职业教育)')"
				>
					本科(职业)
				</view>
				<view
					class="tab-item"
					:class="{active: currentEducationLevel === '高职（专科）'}"
					@tap="changeEducationLevel('高职（专科）')"
				>
					专科(高职)
				</view>
			</view>
		</view>

		<!-- 加载动画 -->
		<view class="loading-mask" v-if="loading">
			<view class="loading-spinner"></view>
		</view>

		<!-- 内容区域 -->
		<view class="content-area" :style="'padding-top:' + height + 'px'">
			<wyh-tree-select-detail
				ref="treeSelect"
				:height="height"
				:items="filteredItems"
				:highlightedIds="highlightedIds"
				@clickItem="onItemClick"
				@clickNav="handleCategoryClick"
			/>
		</view>

		<!-- 热门专业推荐 -->
		<view class="recommendation" v-if="!searchKeyword && filteredItems.length === 0">
			<view class="section-title">热门专业</view>
			<view class="hot-majors">
				<view
					class="hot-major-item"
					v-for="(item, index) in hotMajors"
					:key="index"
					@tap="navigateToDetail(item)"
				>
					{{ item.name }}
				</view>
			</view>

			<view class="section-title">专业类别</view>
			<view class="major-categories">
				<view
					class="category-item"
					v-for="(category, index) in majorCategories"
					:key="index"
					@tap="selectCategory(category)"
				>
					<view class="category-name">{{ category.name }}</view>
					<view class="category-count">{{ category.count }}个专业</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import wyhTreeSelectDetail from '@/components/wyh-tree-select/wyh-tree-select-detail.vue'

	export default {
		components: {
			wyhTreeSelectDetail
		},
		data() {
			return {
				imgUrl: this.$base.uploadImgUrl,
				titleTop: 0,
				height: 0,
				loading: false,
				searchKeyword: '',
				currentEducationLevel: '本科(普通教育)',
				items: [],
				originalItems: null,
				highlightedIds: [],
				inputTimer: null,

				// 热门专业示例数据
				hotMajors: [
					{ id: 1, name: '计算机科学与技术', code: '080901' },
					{ id: 2, name: '软件工程', code: '080902' },
					{ id: 3, name: '临床医学', code: '100201' },
					{ id: 4, name: '人工智能', code: '080717' },
					{ id: 5, name: '数据科学与大数据技术', code: '080910' },
					{ id: 6, name: '金融学', code: '020301K' },
					{ id: 7, name: '会计学', code: '120203K' },
					{ id: 8, name: '电子信息工程', code: '080701' }
				],

				// 专业类别示例数据
				majorCategories: [
					{ id: 'philosophy', name: '哲学', count: 4 },
					{ id: 'economics', name: '经济学', count: 16 },
					{ id: 'law', name: '法学', count: 14 },
					{ id: 'education', name: '教育学', count: 19 },
					{ id: 'literature', name: '文学', count: 31 },
					{ id: 'history', name: '历史学', count: 6 },
					{ id: 'science', name: '理学', count: 36 },
					{ id: 'engineering', name: '工学', count: 169 },
					{ id: 'agriculture', name: '农学', count: 22 },
					{ id: 'medicine', name: '医学', count: 34 },
					{ id: 'management', name: '管理学', count: 46 },
					{ id: 'art', name: '艺术学', count: 32 }
				]
			}
		},
		computed: {
			// 根据搜索关键词过滤专业列表
			filteredItems() {
				if (!this.items || this.items.length === 0) return [];

				// 如果没有搜索关键词，直接返回原始数据
				if (!this.searchKeyword) {
					return this.items;
				}

				// 复制一份数据进行过滤
				let filteredData = JSON.parse(JSON.stringify(this.items));

				// 如果有搜索关键词，进行搜索过滤
				if (this.searchKeyword) {
					const keyword = this.searchKeyword.toLowerCase();

					// 过滤函数，递归搜索子项
					const filterByKeyword = (items) => {
						return items.filter(item => {
							// 检查当前项名称是否匹配
							const nameMatch = item.name && item.name.toLowerCase().includes(keyword);

							// 如果有子项，递归过滤子项
							if (item.children && item.children.length > 0) {
								item.children = filterByKeyword(item.children);
							}

							// 如果名称匹配或者有匹配的子项，则保留
							return nameMatch || (item.children && item.children.length > 0);
						});
					};

					filteredData = filterByKeyword(filteredData);
				}

				return filteredData;
			}
		},
		onShow() {
			this.getMajorData();
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top;

			const query = uni.createSelectorQuery().in(this);
			query.selectAll('.header').boundingClientRect(data => {
				this.height = data[0].height;
			}).exec();
		},
		methods: {
			// 清空搜索框
			clearSearch() {
				this.searchKeyword = '';
				this.search();
			},

			// 处理输入事件，实现实时搜索
			handleInput() {
				// 使用防抖技术，避免频繁请求
				if (this.inputTimer) {
					clearTimeout(this.inputTimer);
				}

				// 设置300毫秒的防抖延迟
				this.inputTimer = setTimeout(() => {
					this.search();
				}, 300);
			},

			// 搜索专业
			search() {
				// 如果没有保存原始数据，先保存
				if (!this.originalItems && this.items.length > 0) {
					this.originalItems = JSON.parse(JSON.stringify(this.items));
				}

				// 如果搜索关键字为空且有原始数据，恢复原始数据
				if (!this.searchKeyword.trim() && this.originalItems) {
					this.items = JSON.parse(JSON.stringify(this.originalItems));
					return;
				}

				// 如果有搜索关键字，调用API进行搜索
				if (this.searchKeyword.trim()) {
					this.loading = true;
					this.$apis.getmajordata1({
						majorName: this.searchKeyword.trim()
					}).then((res) => {
						if (res.code == 0) {
							if (res.data && res.data.tree && res.data.tree.categories) {
								this.processApiData(res.data.tree.categories, '');
							}
						}
						this.loading = false;
					}).catch((err) => {
						console.error('搜索专业失败:', err);
						this.loading = false;
						uni.showToast({
							title: '搜索失败，请稍后重试',
							icon: 'none'
						});
					});
				}
			},

			// 切换教育层次
			changeEducationLevel(level) {
				// 如果点击的是当前层次，不做任何操作
				if (this.currentEducationLevel === level) return;

				// 更新当前教育层次
				this.currentEducationLevel = level;

				// 清空搜索关键词
				this.searchKeyword = '';

				// 重新获取专业数据
				this.getMajorData();
			},

			// 处理专业点击事件
			onItemClick(item) {
				// 更新高亮显示的专业ID
				this.highlightedIds = [item.id];

				// 导航到专业详情页在组件内部已经处理
			},

			// 处理左侧导航点击事件
			handleCategoryClick(index) {
				// 获取选中的类别
				const selectedCategory = this.items[index];

				// 如果该类别尚未加载数据或者children为null，则加载数据
				if (selectedCategory && (!selectedCategory.loaded || !selectedCategory.children || selectedCategory.children.length === 0)) {
					this.getMajorData(selectedCategory.id);
				}
			},

			// 选择专业类别
			selectCategory(category) {
				this.loading = true;
				this.$apis.getmajordata1({
					categoryId: category.id,
					educationLevel: this.currentEducationLevel
				}).then((res) => {
					if (res.code == 0) {
						if (res.data && res.data.tree && res.data.tree.categories) {
							this.processApiData(res.data.tree.categories, category.id);
						}
					}
					this.loading = false;
				}).catch((err) => {
					console.error('获取专业类别失败:', err);
					this.loading = false;
					uni.showToast({
						title: '加载失败，请稍后重试',
						icon: 'none'
					});
				});
			},

			// 导航到专业详情页
			navigateToDetail(item) {
				// 如果有id，优先传递majorId，否则传递majorName
				let url = '/pages/plan/majorDetail?majorName=' + item.name;
				if (item.id) {
					url += '&majorId=' + item.id;
				}
				uni.navigateTo({
					url: url
				});
			},

			// 获取专业数据
			getMajorData(categoryId = '') {
				this.loading = true;

				const params = {
					name: "",
					categoryId: categoryId
				};

				// 添加教育层次参数
				if (!this.searchKeyword.trim()) {
					params.educationLevel = this.currentEducationLevel;
				}

				this.$apis.getmajordata1(params).then((res) => {
					if (res.code == 0) {
						if (res.data && res.data.tree && res.data.tree.categories) {
							this.processApiData(res.data.tree.categories, categoryId);
						}
					}
					this.loading = false;
				}).catch((err) => {
					console.error('获取专业数据失败:', err);
					this.loading = false;
					uni.showToast({
						title: '加载失败，请稍后重试',
						icon: 'none'
					});
				});
			},

			// 处理API返回的数据
			processApiData(categories, categoryId) {
				if (!categoryId || this.items.length === 0) {
					let items = [];
					items = categories.map((item) => {
						let categoryItem = {
							id: item.id,
							name: item.name,
							text: item.name,
							badge: 'number',
							children: [],
							loaded: item.loaded || false
						};

						if (item.children && item.children.length > 0) {
							categoryItem.children = item.children.map((subItem) => {
								let subCategoryItem = {
									id: subItem.id,
									name: subItem.name,
									text: subItem.name,
									isshow: true,
									children: []
								};

								if (subItem.children && subItem.children.length > 0) {
									subCategoryItem.children = subItem.children.map((majorItem) => {
										return {
											id: majorItem.id,
											name: majorItem.name,
											text: majorItem.name,
											code: majorItem.code || ''
										};
									});
								}

								return subCategoryItem;
							});
						}

						return categoryItem;
					});

					this.items = items;
				} else {
					// 如果是加载某个类别的数据，则更新该类别的children
					let index = this.items.findIndex(item => item.id === categoryId);
					if (index !== -1) {
						// 在API返回数据中找到对应的类别
						let categoryData = categories.find(cat => cat.id === categoryId);
						if (categoryData) {
							// 更新children
							this.items[index].children = categoryData.children.map((subItem) => {
								let subCategoryItem = {
									id: subItem.id,
									name: subItem.name,
									text: subItem.name,
									isshow: true,
									children: []
								};

								if (subItem.children && subItem.children.length > 0) {
									subCategoryItem.children = subItem.children.map((majorItem) => {
										return {
											id: majorItem.id,
											name: majorItem.name,
											text: majorItem.name,
											code: majorItem.code || ''
										};
									});
								}

								return subCategoryItem;
							});

							// 标记为已加载
							this.items[index].loaded = true;
						}
					}
				}
			}
		}
	}
</script>

<style lang="scss">
	.majors-container {
		background-color: #f5f5f5;
		min-height: 100vh;

		/* 头部样式 */
		.header {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			z-index: 100;
			background: linear-gradient(135deg, #FF8918 0%, #FFA94D 100%);
			padding: 0 30rpx 20rpx;
			box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
			border-radius: 0 0 30rpx 30rpx;

			.header-content {
				display: flex;
				align-items: center;
				padding: 10rpx 0;

				.title {
					font-size: 36rpx;
					color: #FFFFFF;
					font-weight: bold;
				}
			}

			/* 搜索框样式 */
			.search-box {
				margin: 20rpx 0;

				.search-input-wrapper {
					position: relative;
					background: rgba(255,255,255,0.9);
					border-radius: 40rpx;
					display: flex;
					align-items: center;
					padding: 0 20rpx;
					height: 80rpx;
					box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.05);

					.search-icon {
						width: 36rpx;
						height: 36rpx;
						margin-right: 15rpx;
					}

					.search-input {
						flex: 1;
						height: 100%;
						font-size: 28rpx;
						color: #333;
					}

					.clear-icon {
						width: 32rpx;
						height: 32rpx;
						padding: 10rpx;
					}
				}
			}

			/* 教育层次标签页 */
			.education-tabs {
				display: flex;
				justify-content: space-between;
				background: rgba(255,255,255,0.2);
				border-radius: 10rpx;
				overflow: hidden;
				margin-top: 10rpx;

				.tab-item {
					flex: 1;
					text-align: center;
					padding: 20rpx 0;
					font-size: 28rpx;
					color: #fff;
					transition: all 0.3s;

					&.active {
						background-color: rgba(255,255,255,0.3);
						font-weight: bold;
					}

					&:active {
						opacity: 0.8;
					}
				}
			}
		}

		/* 加载动画 */
		.loading-mask {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background-color: rgba(255,255,255,0.6);
			display: flex;
			justify-content: center;
			align-items: center;
			z-index: 1000;

			.loading-spinner {
				width: 60rpx;
				height: 60rpx;
				border: 6rpx solid #f3f3f3;
				border-top: 6rpx solid #FF8918;
				border-radius: 50%;
				animation: spin 1s linear infinite;
			}

			@keyframes spin {
				0% { transform: rotate(0deg); }
				100% { transform: rotate(360deg); }
			}
		}

		/* 内容区域 */
		.content-area {
			position: relative;
		}

		/* 热门专业推荐 */
		.recommendation {
			padding: 30rpx;

			.section-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
				margin: 30rpx 0 20rpx;
				position: relative;
				padding-left: 20rpx;

				&:before {
					content: '';
					position: absolute;
					left: 0;
					top: 6rpx;
					width: 8rpx;
					height: 32rpx;
					background-color: #FF8918;
					border-radius: 4rpx;
				}
			}

			.hot-majors {
				display: flex;
				flex-wrap: wrap;
				margin: 0 -10rpx;

				.hot-major-item {
					width: calc(50% - 20rpx);
					margin: 10rpx;
					height: 80rpx;
					background-color: #fff;
					border-radius: 10rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 28rpx;
					color: #666;
					box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					padding: 0 20rpx;

					&:active {
						background-color: #FFF0E0;
						color: #FF8918;
					}
				}
			}

			.major-categories {
				display: flex;
				flex-wrap: wrap;
				margin: 0 -10rpx;

				.category-item {
					width: calc(33.33% - 20rpx);
					margin: 10rpx;
					height: 120rpx;
					background-color: #fff;
					border-radius: 10rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);

					.category-name {
						font-size: 28rpx;
						color: #333;
						font-weight: bold;
						margin-bottom: 8rpx;
					}

					.category-count {
						font-size: 24rpx;
						color: #999;
					}

					&:active {
						background-color: #FFF0E0;

						.category-name {
							color: #FF8918;
						}
					}
				}
			}
		}
	}
</style>