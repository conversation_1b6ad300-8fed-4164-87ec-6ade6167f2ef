<template>
	<view class="major-detail-container">
		<!-- 头部 -->
		<view class="header" :style="'padding-top:' + titleTop + 'px'">
			<view class="header-content">
				<view class="back-button" @tap="back">
					<image src="https://pic.kefeichangduo.top/qdkbm/newimage/fhui/back.png"></image>
				</view>
				<view class="title">{{majorName || '专业详情'}}</view>
			</view>
		</view>
		
		<!-- 加载动画 -->
		<view class="loading-mask" v-if="loading">
			<view class="loading-spinner"></view>
		</view>
		
		<!-- 内容区域 -->
		<view class="content">
			<!-- 专业基本信息卡片 -->
			<view class="major-card">
				<view class="major-name">{{majorName || '专业名称'}}</view>
				<view class="major-code" v-if="majorCode">专业代码：{{majorCode}}</view>
				<view class="major-category" v-if="majorCategory">所属门类：{{majorCategory}}</view>
				
				<view class="major-stats">
					<view class="stat-item">
						<view class="stat-value">{{majorData.popularity || '暂无'}}%</view>
						<view class="stat-label">热度</view>
					</view>
					<view class="stat-item">
						<view class="stat-value">{{majorData.employment || '暂无'}}%</view>
						<view class="stat-label">就业率</view>
					</view>
					<view class="stat-item">
						<view class="stat-value">{{majorData.salary || '暂无'}}</view>
						<view class="stat-label">平均薪资</view>
					</view>
					<view class="stat-item">
						<view class="stat-value">{{majorData.difficulty || '暂无'}}</view>
						<view class="stat-label">难度系数</view>
					</view>
				</view>
			</view>
			
			<!-- 专业介绍内容 -->
			<view class="content-section">
				<view class="section-tabs">
					<view 
						class="tab-item" 
						:class="{ active: activeTab === 'intro' }"
						@tap="changeTab('intro')"
					>专业介绍</view>
					<view 
						class="tab-item" 
						:class="{ active: activeTab === 'courses' }"
						@tap="changeTab('courses')"
					>主要课程</view>
					<view 
						class="tab-item" 
						:class="{ active: activeTab === 'jobs' }"
						@tap="changeTab('jobs')"
					>就业方向</view>
					<view 
						class="tab-item" 
						:class="{ active: activeTab === 'schools' }"
						@tap="changeTab('schools')"
					>开设院校</view>
				</view>
				
				<view class="tab-content">
					<!-- 专业介绍 -->
					<view v-if="activeTab === 'intro'" class="intro-content">
						<view class="content-placeholder" v-if="!majorData.introduction">暂无专业介绍信息</view>
						<mpHtml v-else :content="majorData.introduction" :tagStyle="mpHtmlStyle"></mpHtml>
					</view>
					
					<!-- 主要课程 -->
					<view v-if="activeTab === 'courses'" class="courses-content">
						<view class="content-placeholder" v-if="!majorData.courses || majorData.courses.length === 0">暂无课程信息</view>
						<view v-else class="course-list">
							<view class="course-item" v-for="(course, index) in majorData.courses" :key="index">
								<view class="course-name">{{course.name}}</view>
								<view class="course-desc" v-if="course.description">{{course.description}}</view>
							</view>
						</view>
					</view>
					
					<!-- 就业方向 -->
					<view v-if="activeTab === 'jobs'" class="jobs-content">
						<view class="content-placeholder" v-if="!majorData.jobs || majorData.jobs.length === 0">暂无就业方向信息</view>
						<view v-else class="jobs-list">
							<view class="job-item" v-for="(job, index) in majorData.jobs" :key="index">
								<view class="job-name">{{job.name}}</view>
								<view class="job-salary" v-if="job.salary">薪资范围：{{job.salary}}</view>
								<view class="job-desc" v-if="job.description">{{job.description}}</view>
							</view>
						</view>
					</view>
					
					<!-- 开设院校 -->
					<view v-if="activeTab === 'schools'" class="schools-content">
						<view class="content-placeholder" v-if="!majorData.schools || majorData.schools.length === 0">暂无开设院校信息</view>
						<view v-else class="schools-list">
							<view class="school-item" v-for="(school, index) in majorData.schools" :key="index" @tap="viewSchoolDetail(school)">
								<view class="school-name">{{school.name}}</view>
								<view class="school-info">
									<text class="school-level">{{school.level}}</text>
									<text class="school-location">{{school.location}}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部工具栏 -->
		<view class="bottom-toolbar">
			<button class="ask-btn" @tap="openQA">
				<image src="https://pic.kefeichangduo.top/qdkbm/newimage/fhui/icon-chat.png"></image>
				<text>智能问答</text>
			</button>
			<button class="share-btn" open-type="share">
				<image src="https://pic.kefeichangduo.top/qdkbm/newimage/fhui/shareicon.png"></image>
				<text>分享</text>
			</button>
		</view>
	</view>
</template>

<script>
	import mpHtml from '@/uni_modules/mp-html/components/mp-html/mp-html.vue'
	
	export default {
		components: {
			mpHtml
		},
		data() {
			return {
				loading: true,
				titleTop: 0,
				majorName: '',
				majorCode: '',
				majorCategory: '',
				activeTab: 'intro',
				imgUrl: this.$base.uploadImgUrl,
				majorData: {
					popularity: '85',
					employment: '92',
					salary: '6.5k',
					difficulty: '中等',
					introduction: '',
					courses: [],
					jobs: [],
					schools: []
				},
				mpHtmlStyle: {
					h3: 'font-size: 36rpx; font-weight: 700; color: #FF8510; margin: 30rpx 0 20rpx; padding: 16rpx 20rpx; border-left: 8rpx solid #FF8510; background-color: rgba(255, 133, 16, 0.05); border-radius: 0 8rpx 8rpx 0;',
					p: 'font-size: 30rpx; line-height: 1.8; color: #333; margin-bottom: 20rpx; text-align: justify;',
					ol: 'padding-left: 40rpx; margin: 20rpx 0;',
					ul: 'padding-left: 40rpx; margin: 20rpx 0;',
					li: 'position: relative; margin-bottom: 16rpx; padding-left: 10rpx; font-size: 30rpx; line-height: 1.6; color: #333;',
					strong: 'color: #FF5B03; font-weight: 700;',
					a: 'color: #209BFF; text-decoration: underline;',
					hr: 'border: none; height: 2rpx; background: linear-gradient(to right, transparent, rgba(255, 133, 16, 0.5), transparent); margin: 30rpx 0;',
					img: 'max-width: 100%; height: auto; margin: 10rpx auto; display: block;'
				}
			}
		},
		onLoad(options) {
			if (options.majorName) {
				this.majorName = options.majorName;
				this.getMajorDetail();
			}
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top;
		},
		onShareAppMessage() {
			return {
				title: `${this.majorName || '专业详情'} - 高考志愿填报专业解析`,
				path: `/pages/majors/detail?majorName=${this.majorName}`,
				imageUrl: ''
			}
		},
		methods: {
			back() {
				uni.navigateBack({
					delta: 1
				});
			},
			changeTab(tab) {
				this.activeTab = tab;
			},
			getMajorDetail() {
				this.loading = true;
				
				// 调用API获取专业详情
				this.$apis.getmajorsDetail({
					majorName: this.majorName
				}).then(res => {
					this.loading = false;
					
					if (res.code === 0 && res.data) {
						// 更新专业基本信息
						this.majorCode = res.data.code || '';
						this.majorCategory = res.data.category || '';
						
						// 更新专业详细信息
						if (res.data.content) {
							this.majorData.introduction = res.data.content;
						}
						
						// 如果API返回了课程、就业、院校信息，也相应更新
						if (res.data.courses) {
							this.majorData.courses = res.data.courses;
						}
						
						if (res.data.jobs) {
							this.majorData.jobs = res.data.jobs;
						}
						
						if (res.data.schools) {
							this.majorData.schools = res.data.schools;
						}
					} else {
						// 处理数据为空的情况
						uni.showToast({
							title: '获取专业信息失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					this.loading = false;
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none'
					});
				});
			},
			openQA() {
				uni.navigateTo({
					url: '/pages_chat/chat/chat'
				});
			},
			viewSchoolDetail(school) {
				uni.navigateTo({
					url: `/pages/school/detail?id=${school.id}`
				});
			}
		}
	}
</script>

<style lang="scss">
	.major-detail-container {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding-bottom: 120rpx;
		
		/* 头部样式 */
		.header {
			position: sticky;
			top: 0;
			left: 0;
			right: 0;
			z-index: 100;
			background: linear-gradient(135deg, #FF8918 0%, #FFA94D 100%);
			padding: 0 30rpx 20rpx;
			box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
			border-radius: 0 0 30rpx 30rpx;
			
			.header-content {
				display: flex;
				align-items: center;
				padding: 10rpx 0;
				
				.back-button {
					width: 50rpx;
					height: 50rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					background: rgba(255,255,255,0.3);
					border-radius: 50%;
					margin-right: 20rpx;
					
					image {
						width: 20rpx;
						height: 20rpx;
					}
				}
				
				.title {
					font-size: 36rpx;
					color: #FFFFFF;
					font-weight: bold;
					width: 80%;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}
			}
		}
		
		/* 加载动画 */
		.loading-mask {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background-color: rgba(255,255,255,0.6);
			display: flex;
			justify-content: center;
			align-items: center;
			z-index: 1000;
			
			.loading-spinner {
				width: 60rpx;
				height: 60rpx;
				border: 6rpx solid #f3f3f3;
				border-top: 6rpx solid #FF8918;
				border-radius: 50%;
				animation: spin 1s linear infinite;
			}
			
			@keyframes spin {
				0% { transform: rotate(0deg); }
				100% { transform: rotate(360deg); }
			}
		}
		
		/* 内容区域 */
		.content {
			padding: 30rpx;
			
			/* 专业卡片 */
			.major-card {
				background: #FFFFFF;
				border-radius: 20rpx;
				padding: 30rpx;
				margin-bottom: 30rpx;
				box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
				
				.major-name {
					font-size: 40rpx;
					font-weight: bold;
					color: #333;
					margin-bottom: 16rpx;
				}
				
				.major-code, .major-category {
					font-size: 28rpx;
					color: #666;
					margin-bottom: 10rpx;
				}
				
				.major-stats {
					display: flex;
					justify-content: space-between;
					padding-top: 30rpx;
					margin-top: 20rpx;
					border-top: 1px solid #f0f0f0;
					
					.stat-item {
						text-align: center;
						
						.stat-value {
							font-size: 32rpx;
							font-weight: bold;
							color: #FF8918;
							margin-bottom: 6rpx;
						}
						
						.stat-label {
							font-size: 24rpx;
							color: #999;
						}
					}
				}
			}
			
			/* 内容部分 */
			.content-section {
				background: #FFFFFF;
				border-radius: 20rpx;
				overflow: hidden;
				box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
				
				/* 选项卡 */
				.section-tabs {
					display: flex;
					border-bottom: 1px solid #f0f0f0;
					
					.tab-item {
						flex: 1;
						text-align: center;
						padding: 24rpx 0;
						font-size: 28rpx;
						color: #666;
						position: relative;
						transition: all 0.3s;
						
						&.active {
							color: #FF8918;
							font-weight: bold;
							
							&:after {
								content: '';
								position: absolute;
								bottom: 0;
								left: 50%;
								transform: translateX(-50%);
								width: 40rpx;
								height: 6rpx;
								background-color: #FF8918;
								border-radius: 3rpx;
							}
						}
					}
				}
				
				/* 选项卡内容 */
				.tab-content {
					padding: 30rpx;
					min-height: 500rpx;
					
					.content-placeholder {
						display: flex;
						justify-content: center;
						align-items: center;
						height: 200rpx;
						color: #999;
						font-size: 28rpx;
					}
					
					/* 主要课程 */
					.course-list {
						.course-item {
							padding: 20rpx;
							border-bottom: 1px solid #f0f0f0;
							
							&:last-child {
								border-bottom: none;
							}
							
							.course-name {
								font-size: 30rpx;
								font-weight: bold;
								color: #333;
								margin-bottom: 10rpx;
							}
							
							.course-desc {
								font-size: 26rpx;
								color: #666;
								line-height: 1.6;
							}
						}
					}
					
					/* 就业方向 */
					.jobs-list {
						.job-item {
							padding: 20rpx;
							border-bottom: 1px solid #f0f0f0;
							margin-bottom: 20rpx;
							
							&:last-child {
								border-bottom: none;
								margin-bottom: 0;
							}
							
							.job-name {
								font-size: 30rpx;
								font-weight: bold;
								color: #333;
								margin-bottom: 10rpx;
							}
							
							.job-salary {
								font-size: 26rpx;
								color: #FF5B03;
								margin-bottom: 10rpx;
							}
							
							.job-desc {
								font-size: 26rpx;
								color: #666;
								line-height: 1.6;
							}
						}
					}
					
					/* 开设院校 */
					.schools-list {
						.school-item {
							padding: 20rpx;
							background: #f9f9f9;
							border-radius: 10rpx;
							margin-bottom: 20rpx;
							
							&:active {
								background: #fff0e0;
							}
							
							.school-name {
								font-size: 30rpx;
								font-weight: bold;
								color: #333;
								margin-bottom: 10rpx;
							}
							
							.school-info {
								display: flex;
								font-size: 24rpx;
								
								.school-level {
									color: #FF8918;
									background: rgba(255, 137, 24, 0.1);
									padding: 4rpx 12rpx;
									border-radius: 6rpx;
									margin-right: 10rpx;
								}
								
								.school-location {
									color: #666;
								}
							}
						}
					}
				}
			}
		}
		
		/* 底部工具栏 */
		.bottom-toolbar {
			position: fixed;
			left: 0;
			right: 0;
			bottom: 0;
			height: 100rpx;
			background: #FFFFFF;
			box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
			display: flex;
			align-items: center;
			padding: 0 30rpx;
			
			button {
				display: flex;
				align-items: center;
				justify-content: center;
				border: none;
				background: none;
				padding: 0;
				margin: 0;
				line-height: normal;
				
				&::after {
					border: none;
				}
				
				image {
					width: 40rpx;
					height: 40rpx;
					margin-right: 10rpx;
				}
				
				text {
					font-size: 28rpx;
				}
			}
			
			.ask-btn {
				flex: 1;
				height: 80rpx;
				background: linear-gradient(135deg, #FF8918 0%, #FFA94D 100%);
				border-radius: 40rpx;
				color: #FFFFFF;
				margin-right: 20rpx;
			}
			
			.share-btn {
				width: 180rpx;
				height: 80rpx;
				border: 1px solid #FF8918;
				border-radius: 40rpx;
				color: #FF8918;
			}
		}
	}
</style> 