/**
 * 节流函数 - 防止按钮重复点击
 * @param {Function} methods 需要执行的方法
 * @param {any} info 传递给方法的参数
 * @param {Number} delay 延迟时间，默认2000ms
 */
function throttle(methods, info, delay = 2000) {
    // 使用闭包存储状态，避免this指向问题
    const context = this;

    // 如果没有noClick属性或者noClick为true，则可以点击
    if (!context.hasOwnProperty('noClick') || context.noClick === true) {
        // 设置为不可点击状态
        context.noClick = false;

        // 执行传入的方法
        if ((info !== undefined && info !== '') || info === 0) {
            methods(info);
        } else {
            methods();
        }

        // 延迟后恢复可点击状态
        setTimeout(() => {
            context.noClick = true;
        }, delay);
    } else {
        // 如果当前不可点击，显示提示
        uni.showToast({
            title: '数据正在处理，请勿重复提交',
            icon: 'none'
        });
    }
}

/**
 * 防抖函数 - 用于防止按钮在短时间内被多次点击
 * @param {Function} fn 需要执行的函数
 * @param {Number} delay 延迟时间，默认500ms
 * @returns {Function} 返回一个新的函数
 */
function debounce(fn, delay = 500) {
    let timer = null;
    return function(...args) {
        if (timer) clearTimeout(timer);
        timer = setTimeout(() => {
            fn.apply(this, args);
            timer = null;
        }, delay);
    };
}

/**
 * 为按钮添加防重复点击功能
 * @param {HTMLElement} el 按钮元素
 * @param {Number} delay 延迟时间，默认2000ms
 */
function preventButtonReClick(el, delay = 2000) {
    if (el && !el._hasClickListener) {
        el._hasClickListener = true;
        const originalClick = el.onclick;

        el.onclick = function(event) {
            if (el.disabled) {
                // 如果按钮已经禁用，阻止事件
                event.stopPropagation();
                event.preventDefault();
                return false;
            }

            // 禁用按钮
            el.disabled = true;
            el.classList.add('is-disabled');

            // 执行原始点击事件
            if (typeof originalClick === 'function') {
                originalClick.call(this, event);
            }

            // 延迟后恢复按钮状态
            setTimeout(() => {
                el.disabled = false;
                el.classList.remove('is-disabled');
            }, delay);
        };
    }
}

//导出
export default {
    throttle,
    debounce,
    preventButtonReClick
}