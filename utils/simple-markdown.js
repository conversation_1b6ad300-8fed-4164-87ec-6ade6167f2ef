/**
 * 简化的 Markdown 解析器
 * 用于替代 markdown-it，减小包体积
 */

class SimpleMarkdown {
  constructor() {
    this.rules = [
      // 标题
      { pattern: /^### (.*$)/gim, replacement: '<h3>$1</h3>' },
      { pattern: /^## (.*$)/gim, replacement: '<h2>$1</h2>' },
      { pattern: /^# (.*$)/gim, replacement: '<h1>$1</h1>' },
      
      // 粗体和斜体
      { pattern: /\*\*(.*?)\*\*/g, replacement: '<strong>$1</strong>' },
      { pattern: /\*(.*?)\*/g, replacement: '<em>$1</em>' },
      
      // 链接
      { pattern: /\[([^\]]+)\]\(([^)]+)\)/g, replacement: '<a href="$2">$1</a>' },
      
      // 代码块
      { pattern: /```([\s\S]*?)```/g, replacement: '<pre><code>$1</code></pre>' },
      { pattern: /`([^`]+)`/g, replacement: '<code>$1</code>' },
      
      // 换行
      { pattern: /\n/g, replacement: '<br>' },
      
      // 段落
      { pattern: /^(?!<[h1-6]|<pre|<ul|<ol|<li|<blockquote)(.+)$/gim, replacement: '<p>$1</p>' }
    ];
  }

  render(markdown) {
    if (!markdown) return '';
    
    let html = markdown;
    
    // 应用所有规则
    this.rules.forEach(rule => {
      html = html.replace(rule.pattern, rule.replacement);
    });
    
    // 清理多余的空段落
    html = html.replace(/<p><\/p>/g, '');
    html = html.replace(/<p>\s*<\/p>/g, '');
    
    return html;
  }

  // 兼容 markdown-it 的接口
  utils = {
    escapeHtml: function(str) {
      return str
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;');
    }
  };
}

// 创建实例并导出
const simpleMarkdown = new SimpleMarkdown();

// 兼容 markdown-it 的调用方式
export default function(options = {}) {
  return simpleMarkdown;
}

// 也支持直接调用 render 方法
export { simpleMarkdown };
