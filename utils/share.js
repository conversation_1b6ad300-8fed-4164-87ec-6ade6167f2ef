/**
 * 全局分享配置
 * 支持微信小程序、APP等平台
 */

// 默认分享信息
const defaultShareInfo = {
  title: '飞鸿AI',
  path: '/pages/index/index',
  imageUrl: 'https://pic.kefeichangduo.top/qdkbm/share-invite/share-1.jpg',
  desc: '学业规划',
  content: '分享内容'
}

/**
 * 全局分享函数
 * @param {Object} shareInfo 自定义分享信息
 * @returns {Object} 返回分享配置对象
 */
function share(shareInfo = {}) {
  // 合并默认配置和自定义配置
  const shareData = {
    ...defaultShareInfo,
    ...shareInfo
  }
  
  return {
    title: shareData.title,
    path: shareData.path,
    imageUrl: shareData.imageUrl,
    desc: shareData.desc,
    content: shareData.content,
    success(res) {
      uni.showToast({
        title: '分享成功',
        icon: 'none'
      })
    },
    fail(err) {
      uni.showToast({
        title: '分享失败',
        icon: 'none'
      })
    }
  }
}

/**
 * 全局分享混入配置
 */
const shareMixin = {
  // 页面生命周期
  onLoad() {
    // #ifdef MP-WEIXIN
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
    // #endif
  },
  
  // 微信小程序专用分享方法
  onShareAppMessage(res) {
    // 如果页面有自定义的share方法，则使用页面的配置
    if (this.$options.share) {
      return this.$options.share.call(this, res)
    }
    
    // 否则使用默认分享配置
    return share()
  },
  
  // 分享到朋友圈（微信小程序）
  onShareTimeline() {
    // 如果页面有自定义的shareTimeline方法，则使用页面的配置
    if (this.$options.shareTimeline) {
      return this.$options.shareTimeline.call(this)
    }
    
    // 否则使用默认分享配置
    return {
      title: defaultShareInfo.title,
      path: defaultShareInfo.path,
      imageUrl: defaultShareInfo.imageUrl
    }
  }
}

export {
  share,
  shareMixin,
  defaultShareInfo
} 