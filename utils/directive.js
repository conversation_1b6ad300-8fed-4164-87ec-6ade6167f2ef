// 防止按钮重复点击的自定义指令
export const preventReClick = {
  install(Vue) {
    // 防止重复点击
    Vue.directive('prevent-re-click', {
      inserted(el, binding) {
        el.addEventListener('click', () => {
          if (!el.disabled) {
            el.disabled = true
            el.classList.add('is-disabled') // 可选：添加禁用样式
            setTimeout(() => {
              el.disabled = false
              el.classList.remove('is-disabled') // 可选：移除禁用样式
            }, binding.value || 2000) // 默认2000ms后恢复
          }
        })
      }
    })
  }
}
