# 防止按钮重复点击

本项目提供了多种方式来防止按钮被重复点击，以下是使用说明：

## 1. 使用自定义指令 `v-prevent-click`

这是最简单的使用方式，只需要在按钮上添加 `v-prevent-click` 指令即可：

```html
<!-- 默认2秒内防止重复点击 -->
<button v-prevent-click @click="handleSubmit">提交</button>

<!-- 自定义防抖时间（单位：毫秒） -->
<button v-prevent-click="3000" @click="handleSubmit">提交</button>
```

## 2. 使用自定义指令 `v-prevent-re-click`

这是另一种实现方式，功能类似：

```html
<button v-prevent-re-click @click="handleSubmit">提交</button>
```

## 3. 使用 common.js 中的 throttle 方法

如果需要更灵活的控制，可以使用 `throttle` 方法：

```html
<button @click="handleThrottleClick">提交</button>
```

```javascript
methods: {
  handleThrottleClick() {
    this.$common.throttle(() => {
      // 你的业务逻辑
      this.submitForm();
    });
  }
}
```

## 4. 使用 uView 按钮组件

项目已全局配置了 uView 按钮组件的 `throttleTime` 为 2000ms，直接使用即可：

```html
<u-button @click="handleSubmit">提交</u-button>
```

如果需要自定义节流时间，可以通过 props 覆盖全局配置：

```html
<u-button :throttleTime="3000" @click="handleSubmit">提交</u-button>
```

## 5. 使用 common.js 中的 preventButtonReClick 方法

如果需要在 JS 中动态为按钮添加防重复点击功能：

```javascript
// 获取按钮元素
const button = document.getElementById('myButton');
// 添加防重复点击功能，默认2000ms
this.$common.preventButtonReClick(button);
// 或者自定义时间
this.$common.preventButtonReClick(button, 3000);
```

## 示例

可以参考 `examples/prevent-click-example.vue` 文件中的示例代码。
