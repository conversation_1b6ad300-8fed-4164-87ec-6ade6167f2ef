<template>
  <view class="container">
    <view class="title">防止按钮重复点击示例</view>
    
    <!-- 使用自定义指令v-prevent-click -->
    <button class="btn" v-prevent-click @click="handleClick1">普通按钮 (2秒防抖)</button>
    
    <!-- 指定防抖时间 -->
    <button class="btn" v-prevent-click="3000" @click="handleClick2">自定义时间按钮 (3秒防抖)</button>
    
    <!-- 使用v-prevent-re-click指令 -->
    <button class="btn" v-prevent-re-click @click="handleClick3">另一种防抖按钮 (2秒防抖)</button>
    
    <!-- 使用uView按钮组件 -->
    <u-button class="u-btn" text="uView按钮" @click="handleClick4"></u-button>
    
    <!-- 使用common.js中的throttle方法 -->
    <button class="btn" @click="handleThrottleClick">使用throttle方法按钮</button>
    
    <view class="result">
      <text>点击结果: {{ result }}</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      result: '',
      noClick: true // 用于throttle方法
    }
  },
  methods: {
    handleClick1() {
      this.result = '点击了普通按钮 - ' + new Date().toLocaleTimeString()
      console.log('点击了普通按钮')
    },
    handleClick2() {
      this.result = '点击了自定义时间按钮 - ' + new Date().toLocaleTimeString()
      console.log('点击了自定义时间按钮')
    },
    handleClick3() {
      this.result = '点击了另一种防抖按钮 - ' + new Date().toLocaleTimeString()
      console.log('点击了另一种防抖按钮')
    },
    handleClick4() {
      this.result = '点击了uView按钮 - ' + new Date().toLocaleTimeString()
      console.log('点击了uView按钮')
    },
    handleThrottleClick() {
      // 使用common.js中的throttle方法
      this.$common.throttle(() => {
        this.result = '使用throttle方法点击 - ' + new Date().toLocaleTimeString()
        console.log('使用throttle方法点击')
      })
    }
  }
}
</script>

<style lang="scss">
.container {
  padding: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
  text-align: center;
}

.btn {
  margin: 20rpx 0;
  background-color: #2979ff;
  color: #ffffff;
  border-radius: 8rpx;
  font-size: 28rpx;
  height: 80rpx;
  line-height: 80rpx;
}

.u-btn {
  margin: 20rpx 0;
}

.result {
  margin-top: 40rpx;
  padding: 20rpx;
  border: 1px solid #eee;
  border-radius: 8rpx;
  min-height: 80rpx;
}

.is-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
