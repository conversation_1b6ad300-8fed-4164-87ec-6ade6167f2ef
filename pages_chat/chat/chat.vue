<template>
  <view class="chat-page">
    <!-- 顶部导航栏 -->
    <view class="header" :style="'padding-top:' + (titleTop-4) + 'px'">
      <view class="header-content">
        <view class="left" @tap="back">
          <view class="itemList">
            <view class="item">
              <button>
                <image style="width:20rpx;height:36rpx;"
                       src="https://pacetupian.yuyuexiaobao.com/PACE/mall/back-light.png">
                </image>
              </button>
            </view>
          </view>
        </view>
        <view class="title">智能问答</view>
        <view class="right-placeholder"></view>
      </view>
    </view>

    <!-- 聊天内容区域 -->
    <view class="chat-container" :style="{'padding-top': headHeight + 10 + 'px', 'padding-bottom': height + 'px'}">
      <scroll-view
          class="messages-container"
          id="scroll-view"
          scroll-y="true"
          :scroll-with-animation="true"
          :scroll-into-view="scrollIntoView"
          :scroll-top="scrollTop"
          show-scrollbar="false">

        <!-- 欢迎提示 -->
        <view class="welcome-tip">
          <view class="tip-content">
            作为您的志愿规划小伙伴，可以帮您解决一些高考志愿相关问题。
          </view>
        </view>

        <!-- 消息列表 -->
        <view class="message-list">
          <view
              v-for="(message, index) in messages"
              :key="index"
              :id="'msg-' + index"
              class="message-item"
              :class="message.sender">

            <!-- 头像 -->
            <view class="avatar">
              <image
                  v-if="message.sender === 'user'"
                  :src="imgUrl+'/qdkbm/newimage/fhui/用户头像.png'">
              </image>
              <image
                  v-else
                  :src="imgUrl+'/qdkbm/newimage/fhui/logos4.png'">
              </image>
            </view>

            <!-- 消息气泡 -->
            <view class="message-bubble" v-if="message.content">
              <view class="message-content">
                <!-- 用户消息直接显示文本 -->
                <view v-if="message.sender === 'user'">{{ message.content }}</view>

                <!-- AI消息使用markdown渲染 -->
                <view v-else>
                  <markdown-render :sourceMdContent="message.content"></markdown-render>
                </view>
              </view>
            </view>
          </view>

          <!-- 加载状态 -->
          <view v-if="isLoading" class="loading-message">
            <view class="avatar">
              <image :src="imgUrl+'/qdkbm/newimage/fhui/logos4.png'"></image>
            </view>
            <view class="message-bubble">
              <view class="loading-content">
                <image class="loading-icon" :src="imgUrl+'/qdkbm/newimage/fhpic/loading.png'"></image>
                <text>正在作出专业分析，耗时可能过长，请耐心等待...</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 底部锚点元素 -->
        <view id="msg-bottom" style="height: 1px;"></view>
      </scroll-view>
    </view>

    <!-- 底部输入区域 -->
    <view class="input-area">
      <view class="input-wrapper">
				<textarea class="message-input" v-model="newMessage" :placeholder="isLoading ? '系统思考中，请稍后提问...' : '请输入问题...'"
                  :disabled="isLoading"
                  :adjust-position="true" :cursor-spacing="120" auto-height
                  @focus="textareaFocus" @blur="textareaBlur">
				</textarea>
        <view class="send-button" :class="{ 'disabled': !newMessage.trim() || isLoading }" @tap="sendMessage">
          <text style="color: #ffffff;">发送</text>
        </view>
      </view>
    </view>

    <!-- 次数用完弹窗 -->
    <u-popup :round="8" mode="center" bgColor="transparent" :show="showbox">
      <view class="buy-popup">
        <view class="popup-close" @tap="closebox">
          <image :src="imgUrl+'/qdkbm/newimage/fhpic/fh-cancelbtn.png'"></image>
        </view>
        <view class="popup-header">
          <image class="diamond-icon" :src="imgUrl+'/qdkbm/newimage/fhui/icon-vip.png'" mode="widthFix"></image>
        </view>
        <view class="popup-content">
          <view class="popup-title">提问次数已用完！</view>
          <view class="popup-subtitle">请购买智能问答礼包</view>
          <view class="popup-buttons">
            <view class="button-group">
              <button class="primary-button" @tap="confirmPay">去购买</button>
              <button class="secondary-button" @tap="closebox">关闭页面</button>
            </view>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import Request from '@/utils/luch/index.js'
import markdownRender from './components/markdown-render/components/markdown-render/markdown-render.vue'

const http = new Request();
// import markdownit from 'markdown-it'
// import mpHtml from '@/uni_modules/mp-html/components/mp-html/mp-html.vue'
// import marked from 'marked';
export default {
  components: {
    // mpHtml,
    markdownRender
  },
  name: 'ChatInterface',
  data() {
    return {
      headHeight: 0, //顶部导航栏高度
      height: 0, //输入框高度
      htmlString: '',
      titleTop: 20,
      imgUrl: this.$base.uploadImgUrl,
      newMessage: '',
      messages: [],
      isLoading: false,
      lastRequestTime: 0, // 记录最后一次请求的时间
      pendingRequest: false, // 是否有未完成的请求
      pendingMessage: '', // 保存未发送成功的消息

      // 滚动控制变量
      scrollIntoView: '',
      scrollTop: 0,

      // 弹窗控制
      showbox: false
    }
  },
  watch: {
    messages: {
      handler() {
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      },
      deep: true
    }
  },
  onLoad() {
    this.height = 0
    // 获取系统信息，设置标题栏高度
    const systemInfo = uni.getSystemInfoSync()
    this.titleTop = systemInfo.statusBarHeight || 0

    // 初始化时设置滚动到底部
    this.$nextTick(() => {
      this.scrollToBottom()
    })

    const query = uni.createSelectorQuery().in(this)
    query.selectAll('.header').boundingClientRect(datas => {
      if (datas && datas.length > 0) {
        this.headHeight = datas[0].height
      } else {
        this.headHeight = 0
        // console.log('未找到header元素')
      }

      query.selectAll('.input-area').boundingClientRect(data => {
        if (data && data.length > 0) {
          this.height = data[0].height + 5
        } else {
          this.height = 0
          // console.log('未找到input-area元素')
        }
      }).exec()
    }).exec()
  },
  onShow() {
    // 检查登录状态
    let token = uni.getStorageSync('token')
    if (!token) {
      // 如果未登录，尝试自动登录
      this.autoLogin().then(loginSuccess => {
        if (loginSuccess) {
          // 自动登录成功后继续正常流程
          this.checkPendingMessages();
        } else {
          // 自动登录失败，不做任何处理
          // 继续加载消息，用户可以正常使用应用
          // 当用户发送消息时，系统会再次尝试自动登录
          this.checkPendingMessages();
        }
      });
    } else {
      // 已登录，继续正常流程
      this.checkPendingMessages();
    }
  },
  onHide() {
    // 页面隐藏时保存请求状态
    if (this.isLoading || this.pendingRequest) {
      uni.setStorageSync('chat_pending_state', JSON.stringify({
        pendingRequest: true,
        lastRequestTime: this.lastRequestTime || Date.now()
      }))
    }
  },
  onUnload() {
    // 页面卸载时保存请求状态
    if (this.isLoading || this.pendingRequest) {
      uni.setStorageSync('chat_pending_state', JSON.stringify({
        pendingRequest: true,
        lastRequestTime: this.lastRequestTime || Date.now()
      }))
    }
  },
  mounted() {
    // 组件挂载后设置滚动到底部
    this.$nextTick(() => {
      this.scrollToBottom()
    })
  },
  onReady() {
    // 页面就绪后设置滚动到底部
    this.scrollToBottom()
  },
  methods: {
    // 处理待发送的消息和未完成的请求
    checkPendingMessages() {
      // 检查是否有未发送的消息
      const pendingMessageData = uni.getStorageSync('chat_pending_message');
      if (pendingMessageData) {
        // 获取未发送的消息
        this.pendingMessage = pendingMessageData;
        // 清除存储
        uni.removeStorageSync('chat_pending_message');

        // 检查是否有足够的次数（通过获取用户信息）
        this.checkUserQuota().then(hasQuota => {
          if (hasQuota && this.pendingMessage) {
            // 如果有足够的次数且有未发送的消息，自动发送
            const messageToSend = this.pendingMessage;
            this.pendingMessage = ''; // 清空待发送消息

            // 延迟一点时间再发送，确保页面已完全加载
            setTimeout(() => {
              this.newMessage = messageToSend;
              this.sendMessage();
            }, 500);
          }
        });
      }

      // 检查是否有未完成的请求
      const pendingState = uni.getStorageSync('chat_pending_state')
      if (pendingState) {
        try {
          const state = JSON.parse(pendingState)
          const currentTime = Date.now()

          // 如果上次请求时间在5分钟内，显示加载状态
          if (state.pendingRequest && (currentTime - state.lastRequestTime < 300000)) {
            // 先获取消息列表，但不清除状态
            this.fetchMessagesWithoutClearState()

            // 设置加载状态
            this.isLoading = true
            this.pendingRequest = true
            this.lastRequestTime = state.lastRequestTime

            // 添加一个加载中的消息
            setTimeout(() => {
              // 检查最后一条消息是否是用户消息
              const lastMessageIsUser = this.messages.length > 0 &&
                  this.messages[this.messages.length - 1].sender === 'user'

              // 如果最后一条是用户消息，不需要添加助手消息占位符，使用加载状态替代
              if (lastMessageIsUser) {
                // 滚动到底部显示加载状态
                this.scrollToBottom()
              }

              // 显示加载提示
              uni.showToast({
                title: '正在获取回复...',
                icon: 'loading',
                duration: 2000
              })

              // 设置定时器，每30秒检查一次是否有新消息
              const checkInterval = 30000/2 // 30秒
              const maxChecks = 10 // 最多检查10次，总共5分钟
              let checkCount = 0

              const checkForNewMessages = () => {
                checkCount++

                // 获取消息但不清除状态
                this.$apis.conversationList({
                  pageNo: 1,
                  pageSize: 1000
                }).then(res => {
                  if (res.code === 0 && res.data && res.data.list) {
                    // 处理消息列表
                    const messageList = res.data.list.map(item => {
                      return {
                        id: item.id,
                        sender: item.roleId === 1 ? 'user' : 'assistant',
                        content: item.roleId === 1
                            ? (item.title || '')
                            : (item.systemMessage || item.content || ''),
                        timestamp: item.createTime
                      }
                    }).sort((a, b) => a.timestamp - b.timestamp)

                    // 检查最后一条消息是否是系统回复
                    const lastMessage = messageList[messageList.length - 1]
                    if (lastMessage && lastMessage.sender === 'assistant' && lastMessage.content) {
                      // 如果最后一条是系统回复且有内容，更新消息列表并停止检查
                      this.messages = messageList
                      this.isLoading = false
                      this.pendingRequest = false
                      uni.removeStorageSync('chat_pending_state')

                      // 滚动到底部
                      setTimeout(() => {
                        this.scrollToBottom()
                      }, 100)

                      return // 停止继续检查
                    }

                    // 如果已经检查了最大次数仍未获取到回复，停止检查
                    if (checkCount >= maxChecks) {
                      // 最后尝试一次完整刷新
                      this.fetchMessages()
                      return
                    }

                    // 继续检查
                    setTimeout(checkForNewMessages, checkInterval)
                  }
                }).catch(err => {
                  console.error('获取消息列表失败', err)

                  // 如果已经检查了最大次数，停止检查
                  if (checkCount >= maxChecks) {
                    this.fetchMessages()
                    return
                  }

                  // 继续检查
                  setTimeout(checkForNewMessages, checkInterval)
                })
              }

              // 开始第一次检查
              setTimeout(checkForNewMessages, checkInterval)
            }, 500)
          } else {
            // 超过5分钟，清除状态并正常获取消息
            uni.removeStorageSync('chat_pending_state')
            this.fetchMessages()
          }
        } catch (e) {
          console.error('解析请求状态失败', e)
          uni.removeStorageSync('chat_pending_state')
          this.fetchMessages()
        }
      } else {
        // 没有未完成的请求，正常获取消息
        this.fetchMessages()
      }
    },
    switchVoice() {

    },
    discard() {
      return;
    },
    eventHandle() {
    },
    textareaFocus(e) {
      // 键盘弹出时，滚动到底部确保输入框可见
      setTimeout(() => {
        this.scrollToBottom();
      }, 200);
    },
    textareaBlur(e) {
      // 键盘收起时，重新调整布局
      setTimeout(() => {
        this.scrollToBottom();
      }, 200);
    },
    bindInputFocus() {
    },
    confirmPay() {
      // 关闭弹窗
      this.showbox = false;

      // 跳转到购买页面
      uni.navigateTo({
        url: '/pages/plan/buycount'
      });
    },
    closebox() {
      this.showbox = false
      this.isLoading = false
    },
    openbox() {
      this.showbox = true
    },
    refresh() {
      this.fetchMessages()
    },
    back() {
      uni.navigateBack({
        delta: 1
      })
    },
    fetchMessagesWithoutClearState() {
      this.$apis.conversationList({
        pageNo: 1,
        pageSize: 1000
      }).then(res => {
        if (res.code === 0 && res.data && res.data.list) {
          // 处理消息列表
          const messageList = res.data.list.map(item => {
            return {
              id: item.id,
              sender: item.roleId === 1 ? 'user' : 'assistant',
              content: item.roleId === 1
                  ? (item.title || '')
                  : (item.systemMessage || item.content || ''),
              timestamp: item.createTime
            }
          })

          // 按时间排序，确保旧消息在前，新消息在后
          this.messages = messageList.sort((a, b) => a.timestamp - b.timestamp)
        }
      }).catch(err => {
        console.error('获取消息列表失败', err)
      })
    },
    fetchMessages() {
      this.isLoading = true
      this.pendingRequest = false
      // 清除请求状态
      uni.removeStorageSync('chat_pending_state')

      this.$apis.conversationList({
        pageNo: 1,
        pageSize: 1000
      }).then(res => {
        this.isLoading = false
        this.pendingRequest = false
        // 清除请求状态
        uni.removeStorageSync('chat_pending_state')

        if (res.code === 0 && res.data && res.data.list) {
          // 处理消息列表
          const messageList = res.data.list.map(item => {
            return {
              id: item.id,
              sender: item.roleId === 1 ? 'user' : 'assistant',
              content: item.roleId === 1
                  ? (item.title || '')
                  : (item.systemMessage || item.content || ''),
              timestamp: item.createTime
            }
          })

          // 按时间排序，确保旧消息在前，新消息在后
          this.messages = messageList.sort((a, b) => a.timestamp - b.timestamp)

          // 数据加载后，确保滚动到底部
          setTimeout(() => {
            this.scrollToBottom()
          }, 300)
        }
      }).catch(err => {
        console.error('获取消息列表失败', err)
        this.isLoading = false
        this.pendingRequest = false
        // 清除请求状态
        uni.removeStorageSync('chat_pending_state')
      })
    },
    processStreamResponse(chunks, messageIndex) {
      let currentIndex = 0;
      let currentContent = this.messages[messageIndex].content || '';

      const processChunk = () => {
        if (currentIndex >= chunks.length) {
          this.isLoading = false;
          this.pendingRequest = false;
          // 清除请求状态
          uni.removeStorageSync('chat_pending_state');

          // 如果内容为空，移除这条消息
          if (!currentContent.trim()) {
            // 移除空消息
            this.messages.splice(messageIndex, 1);

            // 显示错误提示
            uni.showToast({
              title: '未获取到有效回复',
              icon: 'none',
              duration: 2000
            });
          }

          // 刷新消息列表获取最新消息
          setTimeout(() => {
            this.fetchMessages();
          }, 500);
          return;
        }

        const chunk = chunks[currentIndex];
        if (!chunk.trim()) {
          currentIndex++;
          setTimeout(processChunk, 10);
          return;
        }

        try {
          // 尝试解析JSON
          const data = JSON.parse(chunk);
          if (data.content) {
            currentContent += data.content;
            this.$set(this.messages[messageIndex], 'content', currentContent);
          }
        } catch (err) {
          // 如果不是JSON，尝试提取内容
          try {
            const match = chunk.match(/data: (.+)/);
            if (match && match[1]) {
              const data = JSON.parse(match[1]);
              if (data.content) {
                currentContent += data.content;
                this.$set(this.messages[messageIndex], 'content', currentContent);
              }
            }
          } catch (err) {
            console.error('解析消息块失败:', err, chunk);
          }
        }

        currentIndex++;
        if (currentIndex == chunks.length) {
          this.isLoading = false;
          this.pendingRequest = false;
          // 清除请求状态
          uni.removeStorageSync('chat_pending_state');

          // 如果内容为空，移除这条消息
          if (!currentContent.trim()) {
            // 移除空消息
            this.messages.splice(messageIndex, 1);

            // 显示错误提示
            uni.showToast({
              title: '未获取到有效回复',
              icon: 'none',
              duration: 2000
            });
          }

          // 刷新消息列表获取最新消息
          setTimeout(() => {
            this.fetchMessages();
          }, 500);

          // 消息处理完成后滚动到底部
          setTimeout(() => {
            this.scrollToBottom()
          }, 100)
        }
        setTimeout(processChunk, 10);
      };

      processChunk();
    },
    postMsg(message) {
      this.isLoading = true;
      this.pendingRequest = true;
      this.lastRequestTime = Date.now();

      // 保存请求状态
      uni.setStorageSync('chat_pending_state', JSON.stringify({
        pendingRequest: true,
        lastRequestTime: this.lastRequestTime
      }));

      uni.request({
        url: this.$base.baseUrl + 'admin-api/system/ai/chat-conversation/create',
        method: 'POST',
        header: {
          'Authorization': 'Bearer ' + uni.getStorageSync('token'),
          'Content-Type': 'application/json'
        },
        data: {
          title: message
        },
        success: (res) => {
          // 清除请求状态
          this.pendingRequest = false;
          uni.removeStorageSync('chat_pending_state');

          if (res.data && res.data.code === 0) {
            // 处理成功响应
            if (res.data.data && res.data.data.chunks) {
              // 处理流式响应
              this.processStreamResponse(res.data.data.chunks, this.messages.length - 1);
            } else {
              // 处理非流式响应
              this.isLoading = false;

              // 刷新消息列表获取最新消息
              setTimeout(() => {
                this.fetchMessages();
              }, 500);
            }
          } else {
            // 处理错误响应
            this.isLoading = false;
            this.pendingRequest = false;
            uni.removeStorageSync('chat_pending_state');

            // 检查是否是次数用完的错误
            if (res.data && res.data.code === 404 && res.data.msg && res.data.msg.includes('免费可问次数已用完')) {
              // 保存未发送成功的消息
              uni.setStorageSync('chat_pending_message', message);

              // 显示购买弹窗
              this.showbox = true;
            }

            // 移除空的助手消息
            if (this.messages.length > 0 && this.messages[this.messages.length - 1].sender === 'assistant' && !this.messages[this.messages.length - 1].content) {
              this.messages.pop();
            }

            // 刷新消息列表获取最新消息
            setTimeout(() => {
              this.fetchMessages();
            }, 500);
          }
        },
        fail: (err) => {
          // 请求失败
          this.isLoading = false;
          this.pendingRequest = false;
          uni.removeStorageSync('chat_pending_state');

          // 显示错误提示
          uni.showToast({
            title: '网络请求失败',
            icon: 'none',
            duration: 2000
          });

          // 刷新消息列表获取最新消息
          setTimeout(() => {
            this.fetchMessages();
          }, 500);

          console.error('请求失败:', err);
        }
      });
    },
    checkUserQuota() {
      return new Promise((resolve) => {
        // 使用uni.request直接调用API检查用户配额
        uni.request({
          url: this.$base.baseUrl + 'admin-api/system/ai/chat-conversation/quota',
          method: 'GET',
          header: {
            'Authorization': 'Bearer ' + uni.getStorageSync('token')
          },
          success: (res) => {
            // 根据实际返回值格式: {"code":0,"data":1,"msg":""}
            // 次数直接在data中，是一个数字
            if (res.data && res.data.code === 0) {
              const remainingCount = parseInt(res.data.data);
              const hasQuota = remainingCount > 0;
              // console.log('剩余提问次数:', remainingCount);
              resolve(hasQuota);
            } else {
              // 如果无法获取配额信息，假设用户有配额
              // console.log('无法获取配额信息，默认允许发送');
              resolve(true);
            }
          },
          fail: () => {
            // 请求失败时，假设用户有配额，让他们尝试发送
            // console.log('配额请求失败，默认允许发送');
            resolve(true);
          }
        });
      });
    },
    // 自动登录方法
    autoLogin() {
      return new Promise((resolve) => {
        // 使用store中的gologinPage action进行登录
        this.$store.dispatch('gologinPage').then(res => {
          if (res) {
            // 登录成功
            uni.showToast({
              title: '已为您自动登录',
              icon: 'success',
              duration: 1500
            });
            resolve(true);
          } else {
            // 登录失败
            resolve(false);
          }
        }).catch(() => {
          // 登录出错
          resolve(false);
        });
      });
    },

    sendMessage() {
      if (!this.newMessage.trim() || this.isLoading) return;

      // 检查登录状态
      let token = uni.getStorageSync('token')
      if (!token) {
        // 尝试自动登录
        this.autoLogin().then(loginSuccess => {
          if (loginSuccess) {
            // 自动登录成功，重新尝试发送消息
            this.sendMessage();
          } else {
            // 自动登录失败，不做任何处理
            // 用户可以继续尝试发送消息，系统会再次尝试自动登录
          }
        });
        return;
      }

      // 保存当前消息，以便在次数不足时使用
      const currentMessage = this.newMessage.trim();

      // 先检查用户是否有足够的次数
      this.checkUserQuota().then(hasQuota => {
        if (!hasQuota) {
          // 如果用户没有足够的次数，显示购买弹窗
          uni.setStorageSync('chat_pending_message', currentMessage);
          this.showbox = true;
          return;
        }

        // 创建用户消息
        const userMessage = currentMessage;
        this.messages.push({
          sender: 'user',
          content: userMessage,
          id: Date.now() + '-user'
        });

        // 清空输入框
        this.newMessage = '';

        // 立即滚动到底部
        this.scrollToBottom()

        // 不需要创建助手消息占位符，使用加载状态替代

        // 设置加载状态
        this.isLoading = true;
        this.pendingRequest = true;
        this.lastRequestTime = Date.now();

        // 保存请求状态到本地存储
        uni.setStorageSync('chat_pending_state', JSON.stringify({
          pendingRequest: true,
          lastRequestTime: this.lastRequestTime
        }));

        // 再次滚动到底部确保显示加载状态
        this.scrollToBottom()

        // 发送消息到API
        this.postMsg(userMessage);
      });
    },
    scrollToLatestMessage() {
      const query = uni.createSelectorQuery().in(this)
      query.selectAll('.message').boundingClientRect(data => {
        if (data && data.length > 0) {
          const totalHeight = data.reduce((sum, item) => sum + item.height, 0)
        }
      }).exec()
    },
    // 修改 scrollToBottom 方法
    // 自动登录方法
    autoLogin() {
      return new Promise((resolve) => {
        // 使用store中的gologinPage action进行登录
        this.$store.dispatch('gologinPage').then(res => {
          if (res) {
            // 登录成功 - 无感登录，不显示提示
            resolve(true);
          } else {
            // 登录失败
            resolve(false);
          }
        }).catch(() => {
          // 登录出错
          resolve(false);
        });
      });
    },

    scrollToBottom() {
      // 使用多种方式确保滚动到底部
      this.$nextTick(() => {
        // 方法1: 使用scroll-into-view
        this.scrollIntoView = 'msg-bottom'

        // 方法2: 使用scroll-top (设置一个很大的值)
        setTimeout(() => {
          this.scrollTop = 99999
        }, 50)

        // 方法3: 使用uni.pageScrollTo
        setTimeout(() => {
          uni.pageScrollTo({
            scrollTop: 99999,
            duration: 0
          })
        }, 100)
      })
    },
  }
}
</script>

<style scoped lang="scss">
@import "@/static/css/style.scss";

.chat-page {
  background-color: #fffaed;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 顶部导航栏样式 */
.header {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 100;
  background: #ff9933;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 90rpx;
    padding: 0 30rpx;

    .left {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .itemList {
        display: flex;
        align-items: center;
        justify-content: center;

        .item {
          button {
            background-color: transparent;
            padding: 0;
            margin: 0;
            border: none;
            line-height: 1;

            image {
              width: 20rpx;
              height: 36rpx;
            }
          }
        }
      }
    }

    .title {
      color: #ffffff;
      font-size: 36rpx;
      font-weight: 500;
    }

    .right-placeholder {
      width: 60rpx;
    }
  }
}

/* 聊天容器样式 */
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.messages-container {
  flex: 1;
  height: 100vh;
  box-sizing: border-box;
  padding: 0 15rpx;
  position: relative;
}

/* 欢迎提示样式 */
.welcome-tip {
  margin: 30rpx 0;

  .tip-content {
    background-color: #ffffff;
    border-radius: 16rpx;
    padding: 20rpx 24rpx;
    font-size: 28rpx;
    color: #333;
    line-height: 1.5;
    text-align: center;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  }
}

/* 消息列表样式 */
.message-list {
  padding-bottom: 30rpx;
}

/* 消息项样式 */
.message-item {
  display: flex;
  margin-bottom: 30rpx;
  align-items: flex-start;

  &.user {
    flex-direction: row-reverse;
  }
}

/* 头像样式 */
.avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  margin: 0 16rpx;
  background-color: #e0e0e0;

  image {
    width: 100%;
    height: 100%;
  }
}

/* 消息气泡样式 */
.message-bubble {
  max-width: 75%;
  padding: 20rpx 24rpx;
  border-radius: 18rpx;
  font-size: 30rpx;
  line-height: 1.5;
  word-break: break-word;
  position: relative;

  .user & {
    background-color: #ff9933;
    color: #ffffff;
    margin-right: 10rpx;
    border-top-right-radius: 4rpx;

    &::after {
      content: '';
      position: absolute;
      right: -16rpx;
      top: 20rpx;
      border: 10rpx solid transparent;
      border-left-color: #ff9933;
    }
  }

  .assistant & {
    background-color: #ffffff;
    color: #333333;
    margin-left: 10rpx;
    border-top-left-radius: 4rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

    &::after {
      content: '';
      position: absolute;
      left: -16rpx;
      top: 20rpx;
      border: 10rpx solid transparent;
      border-right-color: #ffffff;
    }
  }
}

/* 确保空消息不显示气泡 */
.message-item.assistant .message-bubble:empty,
.message-item.assistant .message-bubble .message-content:empty {
  display: none;
}

/* 加载状态样式 */
.loading-message {
  display: flex;
  margin-bottom: 30rpx;
  align-items: flex-start;

  .avatar {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
    margin: 0 16rpx;
    background-color: #e0e0e0;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .message-bubble {
    background-color: #ffffff;
    margin-left: 10rpx;
    border-top-left-radius: 4rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    position: relative;

    &::after {
      content: '';
      position: absolute;
      left: -16rpx;
      top: 20rpx;
      border: 10rpx solid transparent;
      border-right-color: #ffffff;
    }

    .loading-content {
      display: flex;
      align-items: center;

      .loading-icon {
        width: 40rpx;
        height: 40rpx;
        margin-right: 10rpx;
        animation: rotate 1s linear infinite;
      }
    }
  }
}

/* 输入区样式 */
.input-area {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 90;

  .input-wrapper {
    display: flex;
    align-items: center;
    padding: 20rpx 30rpx;

    .message-input {
      flex: 1;
      min-height: 72rpx;
      max-height: 200rpx;
      background-color: #f5f5f5;
      border-radius: 36rpx;
      padding: 16rpx 24rpx;
      font-size: 28rpx;
      line-height: 1.5;
    }
  }

  .send-button {
    height: 72rpx;
    background-color: #ff9933;
    border-radius: 36rpx;
    padding: 0 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20rpx;

    text {
      font-size: 30rpx;
      color: #ffffff;
    }

    &.disabled {
      background-color: #e0e0e0;

      text {
        color: #999999;
      }
    }
  }
}

/* 次数用完弹窗样式 */
.buy-popup {
  width: 520rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.1);
}

.popup-close {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  z-index: 10;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-close image {
  width: 28rpx;
  height: 28rpx;
}

.popup-header {
  height: 180rpx;
  background-image: linear-gradient(to bottom, #FFECDA, #FFF9EF);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.popup-header::before {
  content: '';
  position: absolute;
  width: 300rpx;
  height: 300rpx;
  background: radial-gradient(circle, rgba(255, 153, 51, 0.3) 0%, rgba(255, 153, 51, 0) 70%);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.diamond-icon {
  width: 140rpx;
  z-index: 2;
  transform: translateY(10rpx);
  filter: drop-shadow(0 8rpx 16rpx rgba(255, 153, 51, 0.3));
}

.popup-content {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #FF9933;
  margin-bottom: 6rpx;
}

.popup-subtitle {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 30rpx;
}

.popup-buttons {
  width: 100%;
}

.button-group {
  display: flex;
  flex-direction: row;
  gap: 16rpx;
  width: 100%;
}

.popup-buttons button {
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  padding: 0 10rpx;
  flex: 1;
}

.popup-buttons button::after {
  border: none;
}

.primary-button {
  background-image: linear-gradient(to right, #FF9933, #FF7E30);
  color: #FFFFFF;
  box-shadow: 0 4rpx 8rpx rgba(255, 153, 51, 0.3);
}

.secondary-button {
  background-color: #F5F5F5;
  color: #666666;
}
</style>