{"id": "wyh-tree-select", "displayName": "分类选择", "version": "0.10", "description": "分类选择组件，支持单选、多选、全选、默认选中值、禁用项，建议搭配弹出层uni-popup使用", "keywords": ["分类选择"], "repository": "", "engines": {"HBuilderX": "^3.1.22"}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": "", "type": "component-vue"}, "uni_modules": {"platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "n"}, "client": {"App": {"app-vue": "y", "app-nvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "u", "IE": "u", "Edge": "u", "Firefox": "u", "Safari": "u"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y"}, "快应用": {"华为": "y", "联盟": "y"}, "Vue": {"vue2": "y", "vue3": "u"}}}}, "dependencies": {"dayjs": "^1.11.13", "mp-html": "^2.5.1", "vuex-persistedstate": "^4.1.0"}}