Component({
	data: {
		selected: 0,
		color: '#7A7E83',
		selectedColor: '#007FE4',
		list: [{
			id: 1,
			pagePath: '/pages/index/index',
			iconPath: 'https://pic.kefeichangduo.top/qdkbm/newimage/bottom-icon1.png',
			selectedIconPath: 'https://pic.kefeichangduo.top/qdkbm/newimage/bottom-icon1-cur.png',
			text: "首页"
		}, {
			id: 2,
			pagePath: '/pages/plan/plan',
			iconPath: 'https://pic.kefeichangduo.top/qdkbm/newimage/bottom-icon2.png',
			selectedIconPath: 'https://pic.kefeichangduo.top/qdkbm/newimage/bottom-icon2-cur.png',
			text: "规划报告"
		}, {
			id: 3,
			pagePath: '/pages/center/index',
			iconPath: 'https://pic.kefeichangduo.top/qdkbm/newimage/bottom-icon4.png',
			selectedIconPath: 'https://pic.kefeichangduo.top/qdkbm/newimage/bottom-icon4-cur.png',
			text: "我的"
		}]
	},
	attached() {},
	async onShow() {
		if (typeof this.$mp.page.getTabBar === 'function' && this.$mp.page.getTabBar()) {

			this.$mp.page.getTabBar().setData({
				selected: this.data.selected
			})
		}
	},
	methods: {
		switchTab(e) {
			const data = e.currentTarget.dataset
			if (data.index === this.data.selected) {
				return
			}
			const url = data.path
			wx.switchTab({
				url
			})
			this.setData({
				selected: data.index
			})
		}
	}
})