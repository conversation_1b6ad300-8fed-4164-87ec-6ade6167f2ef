<view class="tabBar">
    <view wx:for="{{list}}" wx:key="index" class="tabBarItem" data-path="{{item.pagePath}}" data-index="{{index}}" bindtap="switchTab">
        <image class="image" src="{{selected === index ? item.selectedIconPath : item.iconPath}}"></image>
        <view class="text" style="color: {{selected === index ? selectedColor : color}}">
            {{item.text}}
        </view>
    </view>
</view>