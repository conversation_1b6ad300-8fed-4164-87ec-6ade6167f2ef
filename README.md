### 分类选择
	分类选择组件，支持单选、多选、搜索、默认选中值、禁用项，建议搭配弹出层uni-popup使用
##  注意事项
	组件需要依赖 scss 插件, 或自己去手动修改css样式（很简单）
##  属性 Props
|  属性名		| 类型		| 默认值|    说明										|
|  ----			| ----		| ----	|  ----											|
| showNav		| Boolean	|false	| 是否显示标题栏									|
| title			| String	| 标题	| 标题文字										|
| cancelText	| String	|取消	| 取消按钮文字									|
| confirmText	| String	|确认	| 确认按钮文字									|
| activeIndex	| Number	|0		| 左侧选中的索引									|
| items			| Array		|[ ]	| 为分类显示所需的数据，数据格式见下方示例			|
| activeIds		| Array		| [ ]	| 表示右侧高亮选项的id							|
| max			| Number	| 0		| 表示右侧项最大选中个数, 为1时表示单选				|
| selectedIcon	| String	|''		| 自定义右侧栏选中状态的图标,暂只支持相对路径		|
| selectAll	    | Boolean	|false	| 是否支持全选，默认为false						|
| defaultActiveColor	| String	| #FC2A57	| 默认重点颜色						|    
| isSearch	    | Boolean	|false	| 是否支持搜索，默认为false						|
| searchText	| String	|搜索	| 搜索按钮文字									|
| searchPlaceholder	| String	|请输入关键词	| 搜索框placeholder						|
###   item
|  属性名		| 类型		| 默认值|    说明										|
|  ----			| ----		| ----	|  ----											|
| badge		    | String	|	''	| 左侧徽标	支持：dot 圆点；	number 数字		     |
| disabled	    | String	|false	| 是否禁用选项									|
	
##  方法  Events
|  属性名		| 返回值   	|    说明										        |
|  ----			| ----	    |  ----											        |
| onChange		|所有选中ids Array	|	showNav 为true时有效， 点击确认按钮时触发		|
| clickCancel	| 	 						| showNav 为true时有效，点击取消按钮时触发	|
| clickNav	    |被点击的导航的索引 Number	| 点击左侧导航时触发				        |
| clickItem	    |该点击项的数据	 Object	    | 点击右侧选择项时触发				    |
	    	
	
### 代码示例
```
<template>
	<view class="test">
		<view class="title">
			1、单选 -- {{JSON.stringify(city_ids1)}}
		</view>
		<view class="val">
			{{getCityNames(this.city_ids1)}}
		</view>
		<wyh-tree-select :items="items" :activeIds="city_ids1" :max="1" @clickItem="onItem" @onChange="bindChange($event, 'city_ids1')"/>
		<!-- <view class="title">
			2、多选 -- {{JSON.stringify(city_ids2)}}
		</view>
		<view class="val">
			{{getCityNames(this.city_ids2)}}
		</view>
		<wyh-tree-select :items="items" :activeIds="city_ids2" :max="2" @onChange="bindChange($event, 'city_ids2')"/>
		
		<view class="title">
			3、全选 -- {{JSON.stringify(city_ids5)}}
		</view>
		<view class="val">
			{{getCityNames(this.city_ids5)}}
		</view>
		<wyh-tree-select :items="items" :activeIds="city_ids5" :selectAll="true" @onChange="bindChange($event, 'city_ids5')"/>
		 -->
		<view class="title">
			4、搭配弹出层 -- {{JSON.stringify(city_ids3)}}
		</view>
		<view class="select" @click="$refs.popup_citys.open()">
			<view class="title">选择城市</view>
			<view class="val">{{getCityNames(this.city_ids3)}}</view>
		</view>
		<uni-popup class="citys_win popup_win" ref="popup_citys" type="bottom">
			<wyh-tree-select :items="items" :activeIds="city_ids3" :title="'选择地区'" @onChange="bindChange($event, 'city_ids3')"/>
		</uni-popup>
		<view class="title">
			5、搭配弹出层(showNav) -- {{JSON.stringify(city_ids4)}}
		</view>
		<view class="select" @click="$refs.popup_citys2.open()">
			<view class="title">选择城市</view>
			<view class="val">{{getCityNames(this.city_ids4)}}</view>
		</view>
		<uni-popup class="citys_win popup_win" ref="popup_citys2" type="bottom">
			<wyh-tree-select :items="items" :activeIds="city_ids4" :title="'选择地区'" :showNav="true" @clickCancel="onCancel('popup_citys2')" @onChange="bindChange($event, 'city_ids4', 'popup_citys2')"/>
		</uni-popup>
		
		<view class="title">
			6、搭配弹出层-全选(showNav) -- {{JSON.stringify(city_ids6)}}
		</view>
		<view class="select" @click="$refs.popup_citys3.open()">
			<view class="title">选择城市</view>
			<view class="val">{{getCityNames(this.city_ids6)}}</view>
		</view>
		<uni-popup class="citys_win popup_win" ref="popup_citys3" type="bottom">
			<wyh-tree-select :items="items" :activeIds="city_ids6" :title="'选择地区'" :showNav="true" :selectAll="true" @clickCancel="onCancel('popup_citys3')" @onChange="bindChange($event, 'city_ids6', 'popup_citys3')"/>
		</uni-popup>
		
		<view class="title">
			7、搭配弹出层-搜索、全选(showNav) -- {{JSON.stringify(city_ids7)}}
		</view>
		<view class="select" @click="$refs.popup_citys4.open()">
			<view class="title">选择城市</view>
			<view class="val">{{getCityNames(this.city_ids7)}}</view>
		</view>
		<uni-popup class="citys_win popup_win" ref="popup_citys4" type="bottom">
			<wyh-tree-select :items="items" :activeIds="city_ids7" :title="'选择地区'" :defaultActiveColor="'red'" :showNav="true" :selectAll="true" :isSearch="true" @clickCancel="onCancel('popup_citys4')" @onChange="bindChange($event, 'city_ids7', 'popup_citys4')"/>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				items: [{
						text: '浙江',
						children: [
							{
								// 名称
								text: '温州',
								// id，作为匹配选中状态的标识符
								id: 1,
								// 禁用选项
								disabled: true,
							},
							{
								text: '杭州',
								id: 2,
							},{
								text: '宁波',
								id: 3
							}
						],
						badge: 'dot'
					},
					{
						text: '江苏',
						children: [
							{
								text: '南京',
								id: 4,
								// 禁用选项
								disabled: true,
							},
							{
								text: '无锡',
								id: 5,
							},
							{
								text: '九江',
								id: 6,
							},
							{
								text: '常州',
								id: 7,
							}
						],
						badge: 'number'
					},
					{
						text: '福建',
						disabled: true,
						children: [
							{
								text: '厦门',
								id: 8,
							},
							{
								text: '泉州',
								id: 9,
							}
						]
					},
					{
						text: '湖北',
						children: [
							{
								text: '武汉',
								id: 10,
							},
							{
								text: '黄冈',
								id: 11,
							},
							{
								text: '襄阳',
								id: 12,
							}
						]
					}
				],
				city_ids1: [],
				city_ids2: [3],
				city_ids3: [2],
				city_ids4: [],
				city_ids5: [2,3],
				city_ids6: [2],
				city_ids7: [7]
			}
		},
		methods: {
			onItem(ret) {
				console.log(ret);
			},
			bindChange(ids, key, popupRef) {
				this[key] = ids;
				if(popupRef) {
					this.$refs[popupRef].close();
				}
			},
			getCityNames(ids) {
				if(!ids) return;
				if(ids.length == 0) {
					return '';
				}
				let city_name = [];
				this.items.map(function (item, index) {
					item.children.map(function (city, ind) {
						ids.map(function (id, i) {
							if(id == city.id) {
								city_name.push(city.text);
							}
						})
					})
				})
				return city_name.join(',');
			},
			onCancel(popupRef) {
				this.$refs[popupRef].close();
			}
		},
		onLoad() {
			// 模拟接口数据
			setTimeout(() => {
				this.city_ids1 = [5]
			}, 1000)
		}
	}
</script>

<style lang="scss" scoped>
	.test {
		min-height: calc(100vh - var(--window-top));
		background: #f4f4f4;
		padding-bottom: 800rpx;
		.title {
			padding: 20rpx;
			font-weight: 700;
		}
		.val {
			display: flex;
			align-items: center;
			height: 80rpx;
			padding: 0 20rpx;
			background: #ffffff;
			margin-bottom: 20rpx;
		}
	}
</style>
```