import base from './baseUrl.js'
import Request from '@/utils/luch/index.js'
import store from '../store/index.js'
const http = new Request({
	baseURL: base.baseUrl,
	timeout: 300000, // 响应时间为5秒
})
http.interceptors.request.use(async (config) => {
	let token = uni.getStorageSync('token')
	if (token) {
		config.header['Authorization'] = 'Bearer ' + token
	}
	if (config.method === 'POST' || config.method == "PUT" || config.method == "DELETE") {
		config.header['Content-Type'] = 'application/json'
	} else {
		config.header['Content-Type'] = 'application/x-www-form-urlencoded'
	}
	return config
}, err => {
	// console.log(err)
	// return Promise.reject(err)
})

// 防止重复刷新标志位
let isRefreshing = false
// 重试队列，每一项将是一个待执行的函数形式
let requests = []

// 响应拦截
http.interceptors.response.use((response) => {
	if (response) {

		if (response.data.code == 0) {
			return response.data
		} else {
			uni.hideLoading()
			if(response.config.params && response.config.params.source === 'tab' && response.config.url === 'admin-api/version/wish-version/getWishIntroduce'){

			} else{
				// 如果是账号未登录错误，尝试自动登录
				if (response.data.msg == '账号未登录') {
					// 尝试使用store自动登录
					const store = require('../store/index.js').default;

					// 防止重复刷新
					if (!isRefreshing) {
						isRefreshing = true;

						store.dispatch('gologinPage').then(res => {
							isRefreshing = false;

							if (res) {
								// 登录成功，尝试重新发起请求 - 无感登录，不显示提示
								console.log('自动登录成功，将执行等待中的请求');

								// 执行队列中等待的请求
								requests.forEach(callback => callback());
								// 清空队列
								requests = [];

								// 刷新用户信息
								store.dispatch('getuseInfo');
							} else {
								// 自动登录失败，不做任何处理
								// 请求会继续被拒绝，业务逻辑会处理这个错误
								console.log('自动登录失败');

								// 清空队列
								requests = [];
							}
						}).catch(err => {
							isRefreshing = false;
							console.error('自动登录出错:', err);

							// 清空队列
							requests = [];
						});
					}

					// 将当前请求添加到队列中
					const retryOriginalRequest = new Promise(resolve => {
						requests.push(() => {
							// 重新发起请求
							const config = response.config;
							// 更新token
							config.header['Authorization'] = 'Bearer ' + uni.getStorageSync('token');

							// 使用原始配置重新发起请求
							http.request(config).then(res => {
								resolve(res);
							}).catch(err => {
								resolve(Promise.reject(err));
							});
						});
					});

					return retryOriginalRequest;
				} else if (response.data.msg == '今日免费可看次数已用完') {
					// 如果是次数用完的错误，直接返回
					uni.showModal({
						title: '提示',
						content: response.data.msg,
						showCancel: false,
						success(res) {
							if (res.confirm) {
								uni.navigateBack({delta:1})
							}
						}
					});
				} else {
					// 其他错误正常显示
					uni.showModal({
						title: '提示',
						content: response.data.msg,
						showCancel: false,
						success(res) {}
					});
				}
			}


			return Promise.reject(response.data)
		}
	}
}, err => {
	uni.hideLoading()

	return Promise.reject(err.data)
})
export default http