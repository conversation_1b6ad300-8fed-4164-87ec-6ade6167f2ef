<template><view>
	<u-popup :round="8" mode="center" :safe-area-inset-bottom="false"  :show="showbox">
		
		<view class="mybox">
			<view class="tit">每日免费3次，已用完！</view>
			<view class="desc">会员可不限次数进行查看！</view>
			<view class="btn">
				<button @tap="openVip">开通会员</button>
			</view>
			<view class="btn2">
				<button @tap="closebox">取消</button>
			</view>
			<view class="close-icon" @tap="closebox">
				<text>×</text>
			</view>
		</view>
	</u-popup>
	</view>
</template>
<script>
	export default {
		props: {
			showbox: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
			};
		},
		mounted() {
			// 不执行任何可能导致optDown错误的代码
		},
		computed: {
		},
		methods: {
			openVip(){
				this.$emit('openVip','')
			},
			closebox(){
				this.$emit('closebox',false)
			}
		}
	}
</script>
<style lang="scss" scoped>
.mybox {
	position: relative;
	padding: 40rpx 30rpx;
	width: 560rpx;
	background: #fff;
	border-radius: 16rpx;
	
	.tit {
		font-size: 36rpx;
		font-weight: bold;
		text-align: center;
		margin-bottom: 20rpx;
	}
	
	.desc {
		font-size: 28rpx;
		color: #666;
		text-align: center;
		margin-bottom: 40rpx;
	}
	
	.btn {
		margin-bottom: 20rpx;
		
		button {
			background: #FF8918;
			color: #fff;
			border-radius: 40rpx;
			font-size: 30rpx;
			height: 80rpx;
			line-height: 80rpx;
		}
	}
	
	.btn2 {
		button {
			background: #f5f5f5;
			color: #666;
			border-radius: 40rpx;
			font-size: 30rpx;
			height: 80rpx;
			line-height: 80rpx;
		}
	}
	
	.close-icon {
		position: absolute;
		top: 10rpx;
		right: 20rpx;
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		
		text {
			font-size: 40rpx;
			color: #999;
		}
	}
}
</style>