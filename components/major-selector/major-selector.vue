<template>
  <view class="major-selector">
    <!-- 左侧分类列表 -->
    <view class="category-list">
      <scroll-view scroll-y="true" class="scroll-view">
        <view 
          v-for="(category, index) in categories" 
          :key="index"
          class="category-item"
          :class="{ 'active': currentCategoryIndex === index }"
          @click="selectCategory(index)"
        >
          {{ category.name }}
        </view>
      </scroll-view>
    </view>
    
    <!-- 右侧专业列表 -->
    <view class="major-list">
      <scroll-view scroll-y="true" class="scroll-view">
        <view v-if="currentCategory && currentCategory.children">
          <view 
            v-for="(majorGroup, groupIndex) in currentCategory.children" 
            :key="groupIndex"
            class="major-group"
          >
            <view class="group-title">
              <text>{{ majorGroup.name }}</text>
              <text class="major-count">{{ majorGroup.children.length }}个专业</text>
            </view>
            
            <view class="major-items">
              <view 
                v-for="(major, majorIndex) in majorGroup.children" 
                :key="majorIndex"
                class="major-item"
                :class="{ 'selected': isSelected(major.id) }"
                @click="toggleMajor(major)"
              >
                <!-- 选中标记 -->
                <view v-if="isSelected(major.id)" class="selected-tag">已选</view>
                
                <!-- 专业名称 -->
                <view class="major-name" :class="{ 'selected-text': isSelected(major.id) }">
                  {{ major.name }}
                </view>
                
                <!-- 专业代码 -->
                <view class="major-code">
                  {{ major.code || '' }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'MajorSelector',
  props: {
    // 专业数据
    data: {
      type: Object,
      default: () => ({})
    },
    // 已选专业ID数组
    selectedIds: {
      type: Array,
      default: () => []
    },
    // 最大选择数量
    maxSelection: {
      type: Number,
      default: 3
    }
  },
  data() {
    return {
      currentCategoryIndex: 0,
      categories: [],
      selectedMajors: [...this.selectedIds]
    }
  },
  computed: {
    currentCategory() {
      return this.categories[this.currentCategoryIndex] || null;
    }
  },
  watch: {
    data: {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.tree && newVal.tree.categories) {
          this.categories = newVal.tree.categories;
        }
      }
    },
    selectedIds: {
      handler(newVal) {
        this.selectedMajors = [...newVal];
      }
    }
  },
  methods: {
    // 选择分类
    selectCategory(index) {
      this.currentCategoryIndex = index;
    },
    
    // 检查专业是否已选中
    isSelected(id) {
      return this.selectedMajors.includes(id);
    },
    
    // 切换专业选中状态
    toggleMajor(major) {
      const index = this.selectedMajors.indexOf(major.id);
      
      if (index > -1) {
        // 已选中，取消选择
        this.selectedMajors.splice(index, 1);
        this.$emit('unselect', major);
        uni.showToast({
          title: '已取消选择: ' + major.name,
          icon: 'none',
          duration: 1000
        });
      } else {
        // 未选中，添加选择
        if (this.selectedMajors.length < this.maxSelection) {
          this.selectedMajors.push(major.id);
          this.$emit('select', major);
          uni.showToast({
            title: '已选择: ' + major.name,
            icon: 'none',
            duration: 1000
          });
        } else {
          // 超出最大选择数量
          uni.showToast({
            title: `最多只能选择${this.maxSelection}个专业`,
            icon: 'none',
            duration: 2000
          });
        }
      }
      
      // 发送更新事件
      this.$emit('update:selectedIds', this.selectedMajors);
      this.$emit('change', this.selectedMajors);
    }
  }
}
</script>

<style scoped>
.major-selector {
  display: flex;
  height: 600rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #fff;
}

.category-list {
  width: 200rpx;
  background-color: #f5f5f5;
}

.scroll-view {
  height: 100%;
}

.category-item {
  padding: 30rpx 20rpx;
  font-size: 26rpx;
  text-align: center;
  color: #666;
  border-bottom: 1rpx solid #eee;
}

.category-item.active {
  background-color: #fff;
  color: #FF6600;
  font-weight: bold;
  position: relative;
}

.category-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 30%;
  height: 40%;
  width: 6rpx;
  background-color: #FF6600;
}

.major-list {
  flex: 1;
  padding: 0 20rpx;
}

.major-group {
  margin-bottom: 30rpx;
}

.group-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  font-weight: bold;
}

.major-count {
  font-size: 24rpx;
  color: #fff;
  background-color: #FF8510;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  font-weight: normal;
}

.major-items {
  display: flex;
  flex-wrap: wrap;
}

.major-item {
  width: 48%;
  margin: 1%;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  position: relative;
  transition: all 0.3s;
}

.major-item.selected {
  background-color: #FFEB3B;
  border: 2rpx solid #FFC107;
  box-shadow: 0 4rpx 12rpx rgba(255, 193, 7, 0.5);
}

.selected-tag {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background-color: #FF6600;
  color: white;
  padding: 5rpx 10rpx;
  border-radius: 10rpx;
  font-size: 20rpx;
}

.major-name {
  font-size: 28rpx;
  margin-bottom: 10rpx;
  color: #333;
}

.major-name.selected-text {
  color: #FF6600;
  font-weight: bold;
}

.major-code {
  font-size: 24rpx;
  color: #999;
}
</style>
