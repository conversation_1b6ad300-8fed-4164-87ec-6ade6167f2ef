<template>
	<view class="tabbar-container">
		<view class="tabbar-item" v-for="(item, index) in tabbar" :key="index"
			:class="currentPage == item.id ? 'item-active' : ''" @click="changeItem(item)">
			<view class="item-top">
				<image :src="currentPage == item.id ? item.selectedIconPath : item.iconPath"></image>
			</view>
			<view class="item-bottom">
				<view>{{ item.text }}</view>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		props: {
			currentPage: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				token: {},
				tabbar: []
			};
		},
		mounted() {
			this.tabbar = [{
				id: 1,
				pagePath: '/pages/index/index',
				iconPath: 'https://pic.kefeichangduo.top/qdkbm/newimage/fhui/new-icon1.png',
				selectedIconPath: 'https://pic.kefeichangduo.top/qdkbm/newimage/fhui/new-icon1-current.png',
				text: "首页"
			}, {
				id: 2,
				pagePath: '/pages/plan/plan',
				iconPath: 'https://pic.kefeichangduo.top/qdkbm/newimage/fhui/new-icon2.png',
				selectedIconPath: 'https://pic.kefeichangduo.top/qdkbm/newimage/fhui/new-icon2-current.png',
				text: "规划报告"
			}, {
				id: 4,
				pagePath: '/pages/goldBowl/index',
				iconPath: 'https://pic.kefeichangduo.top/qdkbm/newimage/fhui/new-icon4.png',
				selectedIconPath: 'https://pic.kefeichangduo.top/qdkbm/newimage/fhui/new-icon4-current.png',
				text: "金饭碗"
			}, {
				id: 3,
				pagePath: '/pages/center/index',
				iconPath: 'https://pic.kefeichangduo.top/qdkbm/newimage/fhui/new-icon3.png',
				selectedIconPath: 'https://pic.kefeichangduo.top/qdkbm/newimage/fhui/new-icon3-current.png',
				text: "我的"
			}]
		},
		computed: {
		},
		methods: {
			changeItem(item) {
				const pages = getCurrentPages();
				const currentPage = pages[pages.length - 1];
				
				// 如果当前页面就是目标页面，则不进行跳转
				if (currentPage && currentPage.route === item.pagePath.replace(/^\//, '')) {
					return;
				}
				
				uni.switchTab({
					url: item.pagePath,
					success: function(e) {
						var page = getCurrentPages()[0];
						if (page == undefined || page == null) return;	
					}
				})
			}
		}
	}
</script>