<template>
	<view>
		<u-popup :round="8" mode="center" :safe-area-inset-bottom="false" :show="showbox">

			<view class="mybox">
				<view class="tit">提示</view>
				<view class="desc" style="margin-bottom: 40rpx;">你是否确定退出登录？</view>
				<view style="display: flex;flex-direction: row;justify-content: space-between;width:100%">
					<view class="btn" style="margin:0 15rpx">
						<button style="font-size:30rpx;width:220rpx;height:74rpx;line-height: 72rpx;font-weight: 700;" @tap="signoutSystem">确定</button>
					</view>
					<view class="btn2" style="margin:0 15rpx;">
						<button style="font-size:30rpx;width:220rpx;height:72rpx;font-weight:700;line-height:66rpx;border:2rpx solid #FF8918;color:#FF8918;"
							@tap="closebox">取消</button>
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>
<script>
	export default {
		props: {
			showbox: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {};
		},
		mounted() {
			console.log('444')
		},
		computed: {},
		methods: {
			signoutSystem() {
				this.$emit('signoutSystem', '')
			},
			closebox() {
				this.$emit('closebox', false)
			}
		}
	}
</script>