<template>
	<view>
		<u-popup :round="8" mode="center" :safe-area-inset-bottom="false" :show="showbox">
			<view class="mybox">
				<view class="tit">权益兑换</view>
				<view class="desc">请输入16位兑换码</view>
				<view class="input-container">
					<input 
						class="redemption-input" 
						type="text" 
						v-model="redemptionCode" 
						placeholder="请输入16位兑换码"
						maxlength="16"
						@input="formatRedemptionCode"
					/>
				</view>
				<view style="display: flex;flex-direction: row;justify-content: space-between;width:100%">
					<view class="btn" style="margin:0 15rpx">
						<button style="font-size:30rpx;width:220rpx;height:74rpx;line-height: 72rpx;font-weight: 700;" @tap="submitRedemption">确定</button>
					</view>
					<view class="btn2" style="margin:0 15rpx;">
						<button style="font-size:30rpx;width:220rpx;height:72rpx;font-weight:700;line-height:66rpx;border:2rpx solid #FF8918;color:#FF8918;"
							@tap="closebox">取消</button>
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		props: {
			showbox: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				redemptionCode: ''
			};
		},
		methods: {
			formatRedemptionCode(e) {
				// Remove any non-alphanumeric characters
				let code = this.redemptionCode.replace(/[^a-zA-Z0-9]/g, '');
				
				// Convert to uppercase for UUID consistency
				code = code.toUpperCase();
				
				// Limit to 16 characters
				if (code.length > 16) {
					code = code.substring(0, 16);
				}
				
				this.redemptionCode = code;
			},
			submitRedemption() {
				if (!this.redemptionCode) {
					uni.showToast({
						title: '请输入兑换码',
						icon: 'none'
					});
					return;
				}
				
				if (this.redemptionCode.length !== 16) {
					uni.showToast({
						title: '请输入16位兑换码',
						icon: 'none'
					});
					return;
				}
				
				// Emit the redemption code to the parent component
				this.$emit('submitRedemption', this.redemptionCode);
				
				// Clear the input field
				this.redemptionCode = '';
			},
			closebox() {
				// Clear the input field when closing
				this.redemptionCode = '';
				this.$emit('closebox', false);
			}
		}
	}
</script>

<style scoped>
	.mybox {
		width: 600rpx;
		padding: 50rpx 30rpx;
		text-align: center;
	}
	
	.tit {
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
	}
	
	.desc {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 30rpx;
	}
	
	.input-container {
		margin-bottom: 40rpx;
	}
	
	.redemption-input {
		width: 100%;
		height: 80rpx;
		border: 1px solid #ddd;
		border-radius: 8rpx;
		padding: 0 20rpx;
		box-sizing: border-box;
		font-size: 28rpx;
	}
	
	.btn button {
		background: linear-gradient(90deg, #FF8918, #FF5B03);
		color: #fff;
		border-radius: 37rpx;
		border: none;
	}
	
	.btn2 button {
		background: #fff;
		border-radius: 37rpx;
	}
</style>
