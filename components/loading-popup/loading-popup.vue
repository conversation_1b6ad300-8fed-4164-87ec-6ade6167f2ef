<template>
	<u-popup :round="8" mode="center" bgColor="transparent" :show="show" :closeOnClickOverlay="true" @close="onClose">
		<view class="picbox" :style="{'background-image': 'url('+imgUrl+'/qdkbm/newimage/fhpic/pop-bg.png)'}">
			<view class="closeicon" @tap="onClose">
				<image :src="imgUrl+'/qdkbm/newimage/fhpic/fh-cancelbtn.png'" style="width:40rpx;height:40rpx;"></image>
			</view>

			<view class="myboxs">
				<view class="tit2">内容生成中</view>
				<view class="cont">
					<text>可能需要几分钟，请耐心等待</text>
				</view>
				<view class="jiazaizhong">
					<image :src="imgUrl+'/qdkbm/newimage/fhui/loadingpic2.gif'" style="width:500rpx;height:60rpx">
					</image>
				</view>
				<view class="btn-actions">
					<button class="action-btn cancel-btn" @tap="onClose">取消</button>
					<button class="action-btn back-btn" @tap="onBack">返回上一页</button>
				</view>
			</view>
		</view>
	</u-popup>
</template>

<script>
	export default {
		name: 'loading-popup',
		props: {
			show: {
				type: Boolean,
				default: false
			},
			imgUrl: {
				type: String,
				default: ''
			}
		},
		methods: {
			onClose() {
				this.$emit('close');
				// 点击取消时返回上一页
				uni.navigateBack({
					delta: 1
				});
			},
			onBack() {
				this.$emit('back');
				uni.navigateBack({
					delta: 1
				});
			}
		}
	}
</script>

<style lang="scss">
	.closeicon {
		position: absolute;
		right: -25rpx;
		top: 5rpx;

		image {
			width: 64rpx;
			height: 64rpx;
		}
	}

	.picbox {
		background-repeat: no-repeat;
		background-size: 100% 100%;
		width: 600rpx;
	}

	.myboxs {
		margin: 50rpx 30rpx 20rpx 30rpx;
		height: 320rpx;
		flex-direction: column;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;

		.tit2 {
			margin-bottom: 20rpx;
			color: #AA7248;
			margin-top: 30rpx;
			font-size: 42rpx;
			font-weight: 700;
		}

		.cont {
			margin-bottom: 20rpx;

			text {
				font-weight: 700;
				letter-spacing: 0rpx;
				font-size: 26rpx;
				color: #AA7248;
				line-height: 42rpx;

				.bold {
					color: #e6702b;
					margin: 0 4rpx;
				}
			}
		}

		.jiazaizhong {
			margin-bottom: 30rpx;
		}

		.btn-actions {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			width: 100%;
			padding: 0 20rpx;

			.action-btn {
				font-size: 28rpx;
				height: 70rpx;
				line-height: 70rpx;
				padding: 0 30rpx;
				border-radius: 35rpx;
				margin: 0 10rpx;
				box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
				transition: all 0.3s ease;

				&::after {
					border: none;
				}

				&:active {
					transform: scale(0.95);
				}

				&.cancel-btn {
					background-color: #F5F5F5;
					color: #666666;
				}

				&.back-btn {
					background-image: linear-gradient(90deg, #FF8C38 0, #FD5819 100%);
					color: #FFFFFF;
				}
			}
		}
	}
</style>
