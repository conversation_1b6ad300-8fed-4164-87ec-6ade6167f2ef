<template>
	<view class="tree_select_con">
		<view class="title_con" v-if="selfShowNav">
			<text class="cancel" @click="bindCancel">
				{{cancelText || cancel_text}}
			</text>
			<text class="title">
				{{title}}
			</text>
			<text class="confirm" @click="bindConfirm" :style="{'color':defaultActiveColor}">
				{{confirmText || confirm_text}}
			</text>
		</view>

		<!-- 搜索框 - 隐藏但保留结构以便未来可能的启用 -->
		<view class="search_box" style="display: none;">
		</view>

		<view class="tree_select" v-if="list.length" :style="{'height':'calc(100vh - '+height+'px )'}">
			<view class="select_l">
				<scroll-view class="scroll_view" scroll-y="true" :style="{'height':'calc(100vh - '+height+'px )'}">
					<block v-for="(item, ind) in list" :key="ind">
						<view class="item" :class="[active_index == ind?'active':'', item.disabled?'disabled':'']"
							:key="ind" @click="bindNav(ind)">
							<view class="br" :style="{'background':defaultActiveColor}"></view>
							<view class="txt">
								<text class="text">{{item.text}}</text>
								<view class="dot" v-if="item.badge === 'dot' && item.selectCount"
									:style="{'background':defaultActiveColor}"></view>
								<text class="badge" v-if="item.badge === 'number' && item.selectCount"
									:style="{'background':defaultActiveColor}">{{item.selectCount}}</text>
							</view>
						</view>
					</block>
				</scroll-view>
			</view>
			<view class="select_r">
				<scroll-view class="scroll_view" scroll-y="true" :style="{'height':'calc(100vh - '+height+'px )'}">
					<block v-for="(item,ind) in list[active_index].children" :key="ind">
						<view class="myitem">
							<view class="tit2" @tap="collageNextItem(item)">
								<view class="left">
									{{item.text}}
								</view>
								<view class="right">
									<text>{{item.children.length}}个专业</text>
									<u-icon :name="item.isshow?'arrow-up':'arrow-down'" size="16"></u-icon>
								</view>
							</view>
							<block v-if="item.isshow">
							<view class="cont2" >
								<block v-for="(newitem,newind) in item.children" :key="newind">
									<!-- 修改这里，点击直接跳转到详情页 -->
									<view class="myitem2" :class="{'active': isHighlighted(newitem.id), 'disabled': newitem.disabled}"
										@click="navigateToDetail(newitem)">
										{{newitem.text}}
									</view>
								</block>
							</view>
							</block>
						</view>
					</block>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "wyh-tree-select-detail",
		props: {
			showNav: {
				type: Boolean,
				default: false
			},
			defaultActiveColor: {
				type: String,
				default: '#FB6B3D'
			},
			title: {
				type: String,
				default: '标题'
			},
			cancel_text: {
				type: String,
				default: '取消'
			},
			cancelText: {
				type: String,
				default: '取消'
			},
			confirm_text: {
				type: String,
				default: '确认'
			},
			confirmText: {
				type: String,
				default: '确认'
			},
			// 分类显示所需的数据
			items: {
				type: Array,
				default () {
					return []
				}
			},
			height: {
				type: Number | String,
				default: 0
			},
			// 左侧选中项的索引
			activeIndex: {
				type: Number | String,
				default: 0
			},
			// 高亮显示的专业ID
			highlightedIds: {
				type: Array,
				default () {
					return []
				}
			}
		},
		data() {
			return {
				list: this.items,
				imgUrl: this.$base.uploadImgUrl,
				active_index: this.activeIndex
			};
		},
		watch: {
			items(ret) {
				this.list = ret;
				// 不在这里自动重置active_index，而是由父组件决定何时重置
			},
			highlightedIds(val) {
				// 当高亮ID变化时可能需要更新UI
				this.$forceUpdate();
			}
		},
		computed: {
			selfShowNav() {
				return this.showNav;
			}
		},
		methods: {
			collageNextItem(item){
				item.isshow = !item.isshow;
			},
			// 确认
			bindConfirm() {
				this.$emit('confirm');
			},
			// 取消
			bindCancel() {
				this.$emit('cancel');
			},
			// 左侧点击
			bindNav(ind) {
				if (!this.list[ind].disabled) {
					this.active_index = ind;
				}
				this.$emit('clickNav', ind);
			},
			// 检查专业是否应该高亮显示
			isHighlighted(id) {
				return this.highlightedIds.includes(id);
			},
			// 导航到专业详情页
			navigateToDetail(item) {
				// 触发点击事件，让父组件知道用户点击了哪个专业
				this.$emit('clickItem', item);

				// 直接导航到详情页，使用navigateTo而不是redirectTo，
				// 这样返回时会保留当前页面的状态
				// 如果有id，优先传递majorId，否则传递majorName
				let url = '/pages/plan/majorDetail?majorName=' + item.name;
				if (item.id) {
					url += '&majorId=' + item.id;
				}
				uni.navigateTo({
					url: url
				});
			},

			// 重置左侧导航选中状态为第一项
			resetActiveIndex() {
				this.active_index = 0;
			}
		},
		created() {
			// 初始化
		}
	}
</script>

<style lang="scss" scoped>
	.myitem {
		margin: 0 30rpx;

		.cont2 {
			display: flex;
			flex-direction: row;
			flex-wrap: wrap;
			margin: 0 -20rpx;

			.myitem2 {
				margin: 0 10rpx;
				margin-bottom: 20rpx;
				width: calc(50% - 20rpx);
				background: #fff;
				border-radius: 8rpx;
				height: 70rpx;
				line-height: 70rpx;
				font-size: 28rpx;
				text-align: center;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 1;
				overflow: hidden;
				text-overflow: ellipsis;
				position: relative;

				&.active {
					background: #FFF0E0;
					color: #FF8918;
					border: 1px solid #FF8918;
				}

				&:active {
					opacity: 0.8;
					transform: scale(0.98);
				}
			}
		}

		.tit2 {
			width: 100%;
			padding: 20rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.left {
				font-size: 32rpx;
				font-weight: 700;
				color: #000;
				max-width: 250rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				line-height: 44rpx;
				height: 44rpx;
				white-space: nowrap;
			}

			.right {
				display: flex;
				flex-direction: row;

				text {
					display: inline-flex;
					padding: 10rpx 20rpx;
					color: #fff;
					font-size: 28rpx;
					border-radius: 40rpx;
					margin-right: 10rpx;
					background-image: linear-gradient(180deg, #FF8510 0, #FFBD73 100%);
				}
			}
		}
	}

	.tree_select_con {
		.title_con {
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-between;
			height: 100rpx;
			background: #ffffff;
			color: #333333;
			border-radius: 30rpx 30rpx 0 0;

			.title {
				font-size: 30rpx;
				font-weight: 700;
			}

			.cancel {
				color: #999999;
				padding: 20rpx;
				font-size: 28rpx;
			}

			.confirm {
				padding: 20rpx;
				font-size: 28rpx;
			}
		}

		.search_box {
			display: flex;
			flex-direction: row;
			padding: 20rpx;
			background: #fff;
		}
	}

	.tree_select {
		position: relative;
		display: flex;
		flex-direction: row;
		overflow: hidden;
		color: #333333;

		.select_l {
			flex: 1;
			background: #f9f9f9;
			overflow: hidden;

			.item {
				position: relative;
				display: flex;
				flex-direction: row;
				align-items: center;
				color: #666;

				.txt {
					display: flex;
					flex-direction: row;
					align-items: center;
					position: relative;
					padding: 20rpx 10rpx 20rpx 15rpx;
					width: 100%;
					justify-content: center;

					.text {
						font-size: 28rpx;
						position: relative;
						width: 100%;
						text-align: center;
						white-space: normal;
						word-break: break-all;
						line-height: 1.2;
					}

					.dot {
						position: absolute;
						top: 16rpx;
						right: 10rpx;
						width: 14rpx;
						height: 14rpx;
						border-radius: 7rpx;
					}

					.badge {
						position: absolute;
						top: 10rpx;
						right: 0;
						padding: 0 10rpx;
						border-radius: 50rpx;
						font-size: 24rpx;
						color: #ffffff;
					}
				}

				.br {
					position: absolute;
					top: 0;
					left: 0;
					/* #ifndef APP-NVUE */
					height: 100%;
					/* #endif */
					width: 4rpx;
					opacity: 0;
				}

				&.active {
					background: #fff;
					color: #FF8918;

					.txt {
						.text {
							font-weight: 700;

							&::before {
								position: absolute;
								content: "";
								width: 6rpx;
								height: 100%;
								left: -40rpx;
								top: 0;
								background: #ff8918;
							}
						}
					}
				}

				&.disabled {
					/* #ifndef APP-NVUE */
					cursor: not-allowed;
					/* #endif */
					.text {
						color: #aaa;
					}
				}
			}
		}

		.select_r {
			flex: 2;
			overflow: hidden;
		}
	}
</style>
