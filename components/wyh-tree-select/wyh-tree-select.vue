<template>
	<view class="tree_select_con">
		<view class="title_con" v-if="selfShowNav">
			<text class="cancel" @click="bindCancel">
				{{cancelText || cancel_text}}
			</text>
			<text class="title">
				{{title}}
			</text>
			<text class="confirm" @click="bindConfirm" :style="{'color':defaultActiveColor}">
				{{confirmText || confirm_text}}
			</text>
		</view>
		<view class="search_box" v-if="isSearch">
			<uni-easyinput v-model="search_key" :prefixIcon="'search'" :confirmType="'search'" :trim="'both'" :placeholder="searchPlaceholder" @input="bindInput" @confirm="bindSearch"/>
			<view class="search" @click="bindSearch" :style="{'background':defaultActiveColor}">
				<text class="text">{{searchText}}</text>
			</view>
		</view>
		
		<view class="tree_select" v-if="list.length">
			<view class="select_l">
				<scroll-view class="scroll_view" scroll-y="true" >
					<template v-for="(item, ind) in list">
						<view class="item" :class="[active_index == ind?'active':'', item.disabled?'disabled':'']" :key="ind" @click="bindNav(ind)">
							<view class="br" :style="{'background':defaultActiveColor}"></view>
							<view class="txt">
								<text class="text">{{item.text}}</text>
								<view class="dot" v-if="item.badge === 'dot' && item.selectCount" :style="{'background':defaultActiveColor}"></view>
								<text class="badge" v-if="item.badge === 'number' && item.selectCount" :style="{'background':defaultActiveColor}">{{item.selectCount}}</text>
							</view>
						</view>
					</template>
				</scroll-view>
			</view>
			<view class="select_r">
				<scroll-view class="scroll_view" scroll-y="true" >
					<view class="item" v-if="selectAll" :class="[isAll?'active':'']"  @click="bindAll()">
						<text class="txt" :style="{'color':isAll?defaultActiveColor:'initial'}">
							全选
						</text>
						<view class="ic_con">
							<image class="icon" :src="selectedIcon" mode="mode" v-if="selectedIcon"></image>
							<uni-icons class="ic" :type="'checkmarkempty'" :size="20" :color="defaultActiveColor" v-else></uni-icons>
						</view>
					</view>
					<template v-for="(item,ind) in list[active_index].children">
						<!-- <view class="item disabled" v-if="item.disabled" @click="bindItem(ind, item)" :key="ind">
							<view class="txt">
								{{item.text}}
							</view>
							<view class="ic_con">
								<image class="icon" :src="selectedIcon" mode="mode" v-if="selectedIcon"></image>
								<view class="ic" v-else></view>
							</view>
						</view> -->
						<view class="item" :class="[isHave(item.id)?'active':'', item.disabled?'disabled':'']" @click="bindItem(ind, item)" :key="ind" :style="isHave(item.id) ? 'background-color: #FB6B3D; border-radius: 8rpx; margin: 5rpx 0;' : ''">
							<text class="txt" :style="{'color': item.disabled ? '#aaa' : isHave(item.id) ? '#FFFFFF' : '#333'}">
								{{item.text}} <text v-if="isHave(item.id)" style="font-size: 20rpx; background: #FFD700; color: #8B4513; border-radius: 4rpx; padding: 0 6rpx; margin-left: 5rpx;">已选</text>
							</text>
							<view class="ic_con">
								<image class="icon" :src="selectedIcon" mode="mode" v-if="selectedIcon"></image>
								<uni-icons class="ic" :type="'checkmarkempty'" :size="20" :color="isHave(item.id) ? '#FFFFFF' : defaultActiveColor" v-else></uni-icons>
							</view>
						</view>
					</template>
				</scroll-view>
			</view>
			<view class="search_result" v-if="isSearch" :style="{'height': search_height}">
				<scroll-view class="scroll_view" scroll-y="true" >
					<template v-if="search_list.length">
						<view class="search_list">
							<view class="item" :class="[isHave(item.id)?'active':'',item.disabled?'disabled':'']" v-for="(item, ind) in search_list" :key="ind" @click="bindItem(ind, item)">
								<text class="txt" :style="{'color':item.disabled?'#aaa':isHave(item.id)?defaultActiveColor:'initial'}">{{item.all_text}}</text>
								<view class="ic_con">
									<image class="icon" :src="selectedIcon" mode="mode" v-if="selectedIcon"></image>
									<uni-icons class="ic" :type="'checkmarkempty'" :size="20" :color="defaultActiveColor" v-else></uni-icons>
								</view>
							</view>
						</view>
						<view class="close_btn" :style="{'background':defaultActiveColor}" @click="bindClose">
							<text class="text">关闭</text>
						</view>
					</template>
					<template v-else>
						<view class="empty">
							<text class="txt">暂无相关数据</text>
							<text class="close" :style="{'background':defaultActiveColor}" @click="bindClose">关闭</text>
						</view>
					</template>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name:"wyh-tree-select",
		props: {
			showNav: {
				type: Boolean,
				default: false
			},
			defaultActiveColor: {
				type: String,
				default: '#FB6B3D'
			},
			title: {
				type: String,
				default: '标题'
			},
			cancel_text: {
				type: String,
				default: '取消'
			},
			cancelText: {
				type: String,
				default: '取消'
			},
			confirm_text: {
				type: String,
				default: '确认'
			},
			confirmText: {
				type: String,
				default: '确认'
			},
			// 分类显示所需的数据
			items: {
				type: Array,
				default () {
					return []
				}
			},
			// 左侧选中项的索引
			activeIndex: {
				type: Number | String,
				default: 0
			},
			// 右侧选中项的 id，支持传入数组
			activeIds: {
				type: Array,
				default (){
					return []
				}
			},
			// 右侧项最大选中个数
			max: {
				type: Number,
				default: 0
			},
			// 自定义右侧栏选中状态的图标
			selectedIcon: {
				type: String,
				default: ''
			},
			// 是否支持全选
			selectAll: {
				type: Boolean,
				default: false
			},
			// 是否支持搜索
			isSearch: {
				type: Boolean,
				default: false
			},
			searchPlaceholder: {
				type: String,
				default: '请输入关键词'
			},
			searchText: {
				type: String,
				default: '搜索'
			}
		},
		data() {
			return {
				list: this.items,
				active_index: this.activeIndex,
				active_ids: this.activeIds,
				search_key: '',		//搜索关键词
				search_list: [],	//搜索结果列表
				search_height: 0
			};
		},
		watch: {
			items(ret) {
				this.list = ret;
				this.initBadge();
			},
			activeIds(val) {
				this.active_ids = val;
				this.initBadge();
			}
		},
		computed: {
			selfShowNav() {
				if(this.showNav) {
					let arr = [].concat(this.activeIds);
					this.active_ids = arr;
				}
				return this.showNav;
			},
			// 判断是否全选
			isAll() {
				let that = this;
				let len = 0;
				let len2 = 0;
				let _ind = this.active_index;
				this.list[_ind].children.map(function (item, index) {
					if(!item.disabled) {
						len += 1;
					}
					if(that.isHave(item.id)) {
						len2 += 1;
					}
				})
				if(len == len2 && len > 0) {
					return true;
				} else {
					return false;
				}
			}
		},
		methods: {
			// 确认
			bindConfirm() {
				this.$emit('onChange', this.active_ids);
			},
			// 全选
			bindAll() {
				let that = this;
				let ind = this.active_index;
				if(this.isAll) {
					this.list[ind].children.map(function(item,index) {
						if( that.isHave(item.id) ) {
							let index = that.active_ids.indexOf(item.id);
							that.active_ids.splice(index, 1);
						}
					})
				} else {
					this.list[ind].children.map(function(item,index) {
						let ind = that.active_ids.indexOf(item.id);
						if(!item.disabled && ind == -1) {
							that.active_ids.push(item.id);
						}
					})
				}
				this.initBadge();
				if(!this.showNav) {
					this.$emit('onChange', this.active_ids);
				} 
			},
			// 取消
			bindCancel() {
				this.$emit('clickCancel');
			},
			// 左侧点击
			bindNav(ind) {
				if(!this.list[ind].disabled) {
					this.active_index = ind;
				}
				this.$emit('clickNav', ind);
			},
			// 右侧点击
			bindItem(ind,item) {
				if(item.disabled) {
					return this.$emit('clickItem', item);			
				}
				if(this.max == 1) {
					// 单选
					this.$set(this.active_ids, 0, item.id);
				} else {
					if(this.isHave(item.id)) {
						let index = this.active_ids.indexOf(item.id);
						this.active_ids.splice(index, 1);
						// console.log(this.activeIds);
					} else if(this.max > 0 && !(this.active_ids.length < this.max)) {
						uni.showToast({
						    title: '最大选中数为'+this.max,
						    duration: 2000,
							icon: 'none'
						});
					} else {
						this.active_ids.push(item.id);
						// console.log(this.activeIds);
					}
				}
				this.initBadge();
				this.$emit('clickItem', item);
				if(!this.showNav) {
					this.$emit('onChange', this.active_ids);
				}
			},
			// 是否已经选中
			isHave(id) {
				if(this.active_ids.indexOf(id) == -1) {
					return false;
				} else {
					return true;
				}
			},
			// 计算徽标
			initBadge() {
				let that = this;
				let ind = this.active_index;
				this.list.map(function (item, index) {
					if(item.badge === 'number') {
						// 数字徽标
						item.selectCount = 0;
						item.children.map(function (child, key) {
							if(that.isHave(child.id)) {
								item.selectCount += 1;
							}
						})
					} 
					if(item.badge === 'dot') {
						// 圆点徽标
						item.selectCount = 0;
						item.children.map(function (child, key) {
							if(that.isHave(child.id)) {
								item.selectCount += 1;
							}
						})
					}
				})
				// console.log('list', this.list)
			},
			// 输入
			bindInput() {
				if(!this.search_key) {
					this.search_height = 0;
					this.search_list = [];
				}
			},
			bindClose() {
				this.search_key = '';
				this.search_height = 0;
				this.search_list = [];
			},
			// 搜索
			bindSearch() {
				let that = this;
				if(!this.search_key) {
					uni.showToast({
						title: '请输入关键词',
						icon: 'none'
					});
					return false;
				}
				this.search_height = '600rpx';
				this.search_list = [];
				this.list.map(function (item, index) {
					if(!item.disabled) {
						item.children.map(function (child, key) {
							if(child.text.indexOf(that.search_key) !== -1) {
								child.all_text = `${item.text} -- ${child.text}`;
								that.search_list.push(child);
							}
						})
					}
				})
				// console.log(this.search_list);
			}
		},
		created() {
			this.initBadge();
		}
	}
</script>

<style lang="scss" scoped>
	.tree_select_con {
		.title_con {
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-between;
			height: 100rpx;
			background: #ffffff;
			color: #333333;
			border-radius: 30rpx 30rpx 0 0;
			.title {
				font-size: 30rpx;
				font-weight: 700;
			}
			.cancel {
				color: #999999;
				padding: 20rpx;
				font-size: 28rpx;
			}
			.confirm {
				padding: 20rpx;
				font-size: 28rpx;
			}
		}
		.search_box {
			display: flex;
			flex-direction: row;
			padding: 20rpx;
			background: #fff;
			.search {
				/* #ifndef APP-NVUE */
				flex-shrink: 0;
				letter-spacing: 2px;
				/* #endif */
				display: flex;
				flex-direction: row;
				align-items: center;
				padding: 0 30rpx;
				border-radius: 10rpx;
				margin-left: 20rpx;
				.text {
					font-size: 30rpx;
					font-weight: 700;
					color: #fff;
				}
			}
		}
	}
	.tree_select {
		position: relative;
		display: flex;
		flex-direction: row;
		height: calc(100vh - 550rpx);
		overflow: hidden;
		background: #ffffff;
		color: #333333;
		.scroll_view {
			height: calc(100vh - 550rpx);
		}
		.select_l {
			flex: 1;
			background: #f6f6f6;
			border-radius: 60rpx 0 0 60rpx;
			overflow: hidden;
			.item {
				position: relative;
				display: flex;
				flex-direction: row;
				align-items: center;
				color:#999;
				.txt {
					display: flex;
					flex-direction: row;
					align-items: center;
					position: relative;
					
					padding: 25rpx 26rpx 25rpx 40rpx;
					.text {
						font-size: 28rpx;
					}
					.dot {
						position: absolute;
						top: 16rpx;
						right: 10rpx;
						width: 14rpx;
						height: 14rpx;
						border-radius: 7rpx;
					}
					.badge {
						position: absolute;
						top: 10rpx;
						right: 0;
						padding: 0 10rpx;
						border-radius: 50rpx;
						font-size: 24rpx;
						color: #ffffff;
					}
				}
				.br {
					position: absolute;
					top: 0;
					left: 0;
					/* #ifndef APP-NVUE */
					height: 100%;
					/* #endif */
					width: 4rpx;
					opacity: 0;
				}
				&.active {
					border-radius: 60rpx 0 0 60rpx;
					background: #FFF0EB;color:#FB6B3D;
					.br {
						// opacity: 1;
					}
				}
				&.disabled {
					/* #ifndef APP-NVUE */
					cursor: not-allowed;
					/* #endif */
					.text {
						color: #aaa;
					}
				}
			}
		}
		.select_r {
			flex: 2;
			overflow: hidden;
			.item {
				padding: 20rpx;
				padding-left:40rpx;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: space-between;
				.txt {
					font-size: 30rpx;
					color:#333;
					// font-weight: 700;
				}
				.ic_con {
					width: 40rpx;
					height: 40rpx;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: center;
				}
				.ic {
					opacity: 0;
					font-weight: normal;
				}
				.icon {
					width: 36rpx;
					height: 36rpx;
					opacity: 0;
				}
				&.active {
					.ic {
						opacity: 1;
					}
					.icon {
						opacity: 1;
					}
				}
				&.disabled {
					/* #ifndef APP-NVUE */
					cursor: not-allowed;
					/* #endif */
				}
			}
		}
		.search_result {
			position: absolute;
			top: 0;
			left: 0;
			background: #fff;
			/* #ifdef APP-NVUE */
			width: 750rpx;
			transition: height 0.3s;
			/* #endif */
			/* #ifndef APP-NVUE */
			width: 100%;
			transition: all 0.3s;
			/* #endif */
			overflow: hidden;
			.search_list {
				padding-bottom: 90rpx;
				.item {
					padding: 15rpx 20rpx;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: space-between;
					.txt {
						font-size: 30rpx;
					}
					.ic_con {
						width: 40rpx;
						height: 40rpx;
						display: flex;
						flex-direction: row;
						align-items: center;
						justify-content: center;
					}
					.ic {
						opacity: 0;
						font-weight: normal;
					}
					.icon {
						width: 36rpx;
						height: 36rpx;
						opacity: 0;
					}
					&.active {
						.ic {
							opacity: 1;
						}
						.icon {
							opacity: 1;
						}
					}
					&.disabled {
						/* #ifndef APP-NVUE */
						cursor: not-allowed;
						/* #endif */
					}
				}
			}
			.close_btn {
				position: fixed;
				bottom: 10rpx;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: center;
				/* #ifdef APP-NVUE */
				left: 190rpx;
				width: 370rpx;
				/* #endif */
				/* #ifndef APP-NVUE */
				left: 25%;
				width: 50%;
				letter-spacing: 4px;
				/* #endif */
				height: 70rpx;
				margin-bottom: constant(safe-area-inset-bottom); /* 兼容 iOS 设备 */
				margin-bottom: env(safe-area-inset-bottom); /* 兼容 iPhone X 及以上设备 */
				border-radius: 35rpx;
				box-shadow: 1px 4px 4px rgba(252,42,87,0.3);
				.text {
					font-size: 30rpx;
					color: #fff;
				}
			}
			.empty {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				.txt {
					font-size: 40rpx;
					color: #bbb;
					margin: 100rpx 0;
				}
				.close {
					color: #fff;
					margin: 0;
					padding: 15rpx 40rpx;
					border-radius: 10rpx;
					font-size: 30rpx;
					/* #ifndef APP-NVUE */
					letter-spacing: 2px;
					/* #endif */
				}
			}
		}
	}
</style>
