<template>
	<view class="tree_select_con">
		<view class="title_con" v-if="selfShowNav">
			<text class="cancel" @click="bindCancel">
				{{cancelText || cancel_text}}
			</text>
			<text class="title">
				{{title}}
			</text>
			<text class="confirm" @click="bindConfirm" :style="{'color':defaultActiveColor}">
				{{confirmText || confirm_text}}
			</text>
		</view>
		<view class="search_box" style="display: none;">

		</view>

		<view class="tree_select" v-if="list.length" :style="{'height':'calc(100vh - '+height+'px )'}">
			<view class="select_l">
				<scroll-view class="scroll_view" scroll-y="true" :style="{'height':'calc(100vh - '+height+'px )'}">
					<block v-for="(item, ind) in list" :key="ind">
						<view class="item" :class="[active_index == ind?'active':'', item.disabled?'disabled':'']"
							:key="ind" @click="bindNav(ind)">
							<view class="br" :style="{'background':defaultActiveColor}"></view>
							<view class="txt">
								<text class="text">{{item.text}}</text>
								<view class="dot" v-if="item.badge === 'dot' && item.selectCount"
									:style="{'background':defaultActiveColor}"></view>
								<text class="badge" v-if="item.badge === 'number' && item.selectCount"
									:style="{'background':defaultActiveColor}">{{item.selectCount}}</text>
							</view>
						</view>
					</block>
				</scroll-view>
			</view>
			<view class="select_r">
				<scroll-view class="scroll_view" scroll-y="true" :style="{'height':'calc(100vh - '+height+'px )'}">
					<view class="item" v-if="selectAll" :class="[isAll?'active':'']" @click="bindAll()">
						<text class="txt" :style="{'color':isAll?defaultActiveColor:'initial'}">
							全选
						</text>
						<view class="ic_con">
							<image class="icon" :src="selectedIcon" mode="mode" v-if="selectedIcon"></image>
							<uni-icons class="ic" :type="'checkmarkempty'" :size="20" :color="defaultActiveColor"
								v-else></uni-icons>
						</view>
					</view>
					<block v-for="(item,ind) in list[active_index].children" :key="ind">
						<view class="myitem">
							<view class="tit2" @tap="collageNextItem(item)">
								<view class="left">
									{{item.text}}
								</view>
								<view class="right">
									<text>{{item.children.length}}个专业</text>
									<u-icon :name="item.isshow?'arrow-up':'arrow-down'" size="16"></u-icon>
								</view>

							</view>
							<block v-if="item.isshow">
							<view class="cont2" >
								<block v-for="(newitem,newind) in item.children" :key="newind">
									<view class="myitem2" :class="[isHave(newitem.id)?'active':'', newitem.disabled?'disabled':'']" @click="bindItem(newind, newitem)">{{newitem.text}}</view>
								</block>
							</view>
							</block>
						</view>
					</block>
				</scroll-view>
			</view>
			<view class="search_result" v-if="isSearch" :style="{'height': search_height}">
				<scroll-view class="scroll_view" scroll-y="true">
					<template v-if="search_list.length">
						<view class="search_list">
							<view class="item" :class="[isHave(item.id)?'active':'',item.disabled?'disabled':'']"
								v-for="(item, ind) in search_list" :key="ind" @click="bindItem(ind, item)">
								<text class="txt"
									:style="{'color':item.disabled?'#aaa':isHave(item.id)?defaultActiveColor:'initial'}">{{item.all_text}}</text>
								<view class="ic_con">
									<image class="icon" :src="selectedIcon" mode="mode" v-if="selectedIcon"></image>
									<uni-icons class="ic" :type="'checkmarkempty'" :size="20"
										:color="defaultActiveColor" v-else></uni-icons>
								</view>
							</view>
						</view>
						<view class="close_btn" :style="{'background':defaultActiveColor}" @click="bindClose">
							<text class="text">关闭</text>
						</view>
					</template>
					<template v-else>
						<view class="empty">
							<text class="txt">暂无相关数据</text>
							<text class="close" :style="{'background':defaultActiveColor}" @click="bindClose">关闭</text>
						</view>
					</template>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "wyh-tree-select",
		props: {
			showNav: {
				type: Boolean,
				default: false
			},
			defaultActiveColor: {
				type: String,
				default: '#FB6B3D'
			},
			title: {
				type: String,
				default: '标题'
			},
			cancel_text: {
				type: String,
				default: '取消'
			},
			cancelText: {
				type: String,
				default: '取消'
			},
			confirm_text: {
				type: String,
				default: '确认'
			},
			confirmText: {
				type: String,
				default: '确认'
			},
			// 分类显示所需的数据
			items: {
				type: Array,
				default () {
					return []
				}
			},
			height: {
				type: Number | String,
				default: 0
			},
			// 左侧选中项的索引
			activeIndex: {
				type: Number | String,
				default: 0
			},
			// 右侧选中项的 id，支持传入数组
			activeIds: {
				type: Array,
				default () {
					return []
				}
			},
			// 右侧项最大选中个数
			max: {
				type: Number,
				default: 0
			},
			// 自定义右侧栏选中状态的图标
			selectedIcon: {
				type: String,
				default: ''
			},
			// 是否支持全选
			selectAll: {
				type: Boolean,
				default: false
			},
			// 是否支持搜索
			isSearch: {
				type: Boolean,
				default: false
			},
			searchPlaceholder: {
				type: String,
				default: '请输入关键词'
			},
			searchText: {
				type: String,
				default: '搜索'
			}
		},
		data() {
			return {
				list: this.items,
				imgUrl: this.$base.uploadImgUrl,
				active_index: this.activeIndex,
				active_ids: this.activeIds,
				search_key: '', //搜索关键词
				search_list: [], //搜索结果列表
				search_height: 0
			};
		},
		watch: {
			items(ret) {
				this.list = ret;
				this.initBadge();
			},
			activeIds(val) {
				this.active_ids = val;
				this.initBadge();
			}
		},
		computed: {
			selfShowNav() {
				if (this.showNav) {
					let arr = [].concat(this.activeIds);
					this.active_ids = arr;
				}
				return this.showNav;
			},
			// 判断是否全选
			isAll() {
				let that = this;
				let len = 0;
				let len2 = 0;
				let _ind = this.active_index;
				if (this.list && this.list[_ind] && this.list[_ind].children.length > 0) {
					this.list[_ind].children.map(function(item, index) {
						if (!item.disabled) {
							len += 1;
						}
						if (that.isHave(item.id)) {
							len2 += 1;
						}
					})
					if (len == len2 && len > 0) {
						return true;
					} else {
						return false;
					}
				}
			}
		},
		methods: {
			collageNextItem(item){
				console.log(item)
				item.isshow = !item.isshow
			},
			// 确认
			bindConfirm() {
				this.$emit('onChange', this.active_ids);
			},
			// 全选
			bindAll() {
				let that = this;
				let ind = this.active_index;
				if (this.isAll) {
					this.list[ind].children.map(function(item, index) {
						if (that.isHave(item.id)) {
							let index = that.active_ids.indexOf(item.id);
							that.active_ids.splice(index, 1);
						}
					})
				} else {
					this.list[ind].children.map(function(item, index) {
						let ind = that.active_ids.indexOf(item.id);
						if (!item.disabled && ind == -1) {
							that.active_ids.push(item.id);
						}
					})
				}
				this.initBadge();
				if (!this.showNav) {
					this.$emit('onChange', this.active_ids);
				}
			},
			// 取消
			bindCancel() {
				this.$emit('clickCancel');
			},
			// 左侧点击
			bindNav(ind) {
				if (!this.list[ind].disabled) {
					this.active_index = ind;
				}
				this.$emit('clickNav', ind);
			},
			// 右侧点击
			bindItem(ind, item) {
				// 保存当前选择状态，以便在父组件取消时能够恢复
				const originalActiveIds = [...this.active_ids];

				// 先触发clickItem事件，传递当前点击的专业信息
				this.$emit('clickItem', item);

				if (this.max == 1) {
					// 单选
					// 如果已经选中，则取消选择
					if (this.isHave(item.id)) {
						this.active_ids = [];
					} else {
						// 否则选中新专业
						this.$set(this.active_ids, 0, item.id);
					}
				} else {
					if (this.isHave(item.id)) {
						let index = this.active_ids.indexOf(item.id);
						this.active_ids.splice(index, 1);
					} else if (this.max > 0 && !(this.active_ids.length < this.max)) {
						uni.showToast({
							title: '最大选中数为' + this.max,
							duration: 2000,
							icon: 'none'
						});
						return; // 不执行后续操作
					} else {
						this.active_ids.push(item.id);
					}
				}
				this.initBadge();

				// 始终触发onChange事件，无论是否显示导航
				// 将原始选择状态作为参数传递给父组件，以便在取消时恢复
				this.$emit('onChange', this.active_ids, originalActiveIds);
			},
			// 是否已经选中
			isHave(id) {
				if (this.active_ids.indexOf(id) == -1) {
					return false;
				} else {
					return true;
				}
			},
			// 计算徽标
			initBadge() {
				let that = this;
				let ind = this.active_index;
				this.list.map(function(item, index) {
					if (item.badge === 'number') {
						// 数字徽标
						item.selectCount = 0;
						item.children.map(function(child, key) {
							if (that.isHave(child.id)) {
								item.selectCount += 1;
							}
						})
					}
					if (item.badge === 'dot') {
						// 圆点徽标
						item.selectCount = 0;
						item.children.map(function(child, key) {
							if (that.isHave(child.id)) {
								item.selectCount += 1;
							}
						})
					}
				})
			},
			// 输入
			bindInput() {
				if (!this.search_key) {
					this.search_height = 0;
					this.search_list = [];
				}
			},
			bindClose() {
				this.search_key = '';
				this.search_height = 0;
				this.search_list = [];
			},
			// 搜索
			bindSearch() {
				let that = this;
				if (!this.search_key) {
					uni.showToast({
						title: '请输入关键词',
						icon: 'none'
					});
					return false;
				}
				this.search_height = '600rpx';
				this.search_list = [];
				this.list.map(function(item, index) {
					if (!item.disabled) {
						item.children.map(function(child, key) {
							if (child.text.indexOf(that.search_key) !== -1) {
								child.all_text = `${item.text} -- ${child.text}`;
								that.search_list.push(child);
							}
						})
					}
				})
			}
		},
		created() {
			this.initBadge();
		}
	}
</script>

<style lang="scss" scoped>
	.myitem {
		margin: 0 30rpx;

		.cont2 {
			display: flex;
			flex-direction: row;
			flex-wrap: wrap;
			margin: 0 -20rpx;

			.myitem2 {
				margin: 0 10rpx;
				margin-bottom: 20rpx;
				width: calc(50% - 20rpx);
				background: #fff;
				border-radius: 8rpx;
				height: 70rpx;
				line-height: 70rpx;
				font-size: 28rpx;
				text-align: center;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 1;
				overflow: hidden;
				text-overflow: ellipsis
			}
		}

		.tit2 {
			width: 100%;
			padding: 20rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.left {
				font-size: 32rpx;
				font-weight: 700;
				color: #000;
				max-width: 250rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				line-height: 44rpx;
				height: 44rpx;
				white-space: nowrap;
			}

			.right {
				display: flex;
				flex-direction: row;

				text {
					display: inline-flex;
					padding: 10rpx 20rpx;
					color: #fff;
					font-size: 28rpx;
					border-radius: 40rpx;
					margin-right: 10rpx;
					background-image: linear-gradient(180deg, #FF8510 0, #FFBD73 100%);
				}
			}
		}
	}

	.tree_select_con {
		.title_con {
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-between;
			height: 100rpx;
			background: #ffffff;
			color: #333333;
			border-radius: 30rpx 30rpx 0 0;

			.title {
				font-size: 30rpx;
				font-weight: 700;
			}

			.cancel {
				color: #999999;
				padding: 20rpx;
				font-size: 28rpx;
			}

			.confirm {
				padding: 20rpx;
				font-size: 28rpx;
			}
		}

		.search_box {
			display: flex;
			flex-direction: row;
			padding: 20rpx;
			background: #fff;

			.search-input {
				width: 100%;
				align-items: center;
				height: 72rpx;
				background: #f1f1f1;
				border-radius: 48rpx;
				display: flex;
				flex-direction: row;

				.icon {
					width: 100rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					image {
						width: 40rpx;
						height: 40rpx;
					}
				}

				.inputwrap {
					flex: 1;
					position: relative;

					.inputstyle {
						width: 100%;
						height: 72rpx;
						line-height: 72rpx;
						font-size: 28rpx;
					}

					button {
						z-index: 99;
						position: absolute;
						right: 0;
						top: 0;
						height: 72rpx;
						line-height: 72rpx;
						color: #fff;
						font-size: 32rpx;
						border-radius: 48rpx;
						background: #FF8918;
						padding: 0 50rpx;
						margin: 0;
						min-width: inherit;

						&::after {
							border: none
						}
					}
				}
			}

			.search {
				/* #ifndef APP-NVUE */
				flex-shrink: 0;
				letter-spacing: 2px;
				/* #endif */
				display: flex;
				flex-direction: row;
				align-items: center;
				padding: 0 30rpx;
				border-radius: 10rpx;
				margin-left: 20rpx;

				.text {
					font-size: 30rpx;
					font-weight: 700;
					color: #fff;
				}
			}
		}
	}

	.tree_select {
		position: relative;
		display: flex;
		flex-direction: row;

		overflow: hidden;
		color: #333333;

		.scroll_view {

		}

		.select_l {
			flex: 1;
			background: #f9f9f9;
			overflow: hidden;

			.item {
				position: relative;
				display: flex;
				flex-direction: row;
				align-items: center;
				color: #666;

				.txt {
					display: flex;
					flex-direction: row;
					align-items: center;
					position: relative;

					padding: 20rpx 10rpx 20rpx 15rpx;
					width: 100%;
					justify-content: center;

					.text {
						font-size: 28rpx;
						position: relative;
						width: 100%;
						text-align: center;
						white-space: normal;
						word-break: break-all;
						line-height: 1.2;
					}

					.dot {
						position: absolute;
						top: 16rpx;
						right: 10rpx;
						width: 14rpx;
						height: 14rpx;
						border-radius: 7rpx;
					}

					.badge {
						position: absolute;
						top: 10rpx;
						right: 0;
						padding: 0 10rpx;
						border-radius: 50rpx;
						font-size: 24rpx;
						color: #ffffff;
					}
				}

				.br {
					position: absolute;
					top: 0;
					left: 0;
					/* #ifndef APP-NVUE */
					height: 100%;
					/* #endif */
					width: 4rpx;
					opacity: 0;
				}

				&.active {
					background: #fff;
					color: #FF8918;

					.txt {
						.text {
							font-weight: 700;

							&::before {
								position: absolute;
								content: "";
								width: 6rpx;
								height: 100%;
								left: -40rpx;
								top: 0;
								background: #ff8918;
							}
						}

					}

					.br {
					}
				}

				&.disabled {
					/* #ifndef APP-NVUE */
					cursor: not-allowed;

					/* #endif */
					.text {
						color: #aaa;
					}
				}
			}
		}

		.select_r {
			flex: 2;
			overflow: hidden;

			.item {
				padding: 20rpx;
				padding-left: 40rpx;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: space-between;

				.txt {
					font-size: 34rpx;
					color: #333;
				}

				.ic_con {
					width: 40rpx;
					height: 40rpx;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: center;
				}

				.ic {
					opacity: 0;
					font-weight: normal;
				}

				.icon {
					width: 36rpx;
					height: 36rpx;
					opacity: 0;
				}

				&.active {
					.ic {
						opacity: 1;
					}

					.icon {
						opacity: 1;
					}
				}

				&.disabled {
					/* #ifndef APP-NVUE */
					cursor: not-allowed;
					/* #endif */
				}
			}
		}

		.search_result {
			position: absolute;
			top: 0;
			left: 0;
			background: #fff;
			/* #ifdef APP-NVUE */
			width: 750rpx;
			transition: height 0.3s;
			/* #endif */
			/* #ifndef APP-NVUE */
			width: 100%;
			transition: all 0.3s;
			/* #endif */
			overflow: hidden;

			.search_list {
				padding-bottom: 90rpx;

				.item {
					padding: 15rpx 20rpx;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: space-between;

					.txt {
						font-size: 30rpx;
					}

					.ic_con {
						width: 40rpx;
						height: 40rpx;
						display: flex;
						flex-direction: row;
						align-items: center;
						justify-content: center;
					}

					.ic {
						opacity: 0;
						font-weight: normal;
					}

					.icon {
						width: 36rpx;
						height: 36rpx;
						opacity: 0;
					}

					&.active {
						.ic {
							opacity: 1;
						}

						.icon {
							opacity: 1;
						}
					}

					&.disabled {
						/* #ifndef APP-NVUE */
						cursor: not-allowed;
						/* #endif */
					}
				}
			}

			.close_btn {
				position: fixed;
				bottom: 10rpx;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: center;
				/* #ifdef APP-NVUE */
				left: 190rpx;
				width: 370rpx;
				/* #endif */
				/* #ifndef APP-NVUE */
				left: 25%;
				width: 50%;
				letter-spacing: 4px;
				/* #endif */
				height: 70rpx;
				margin-bottom: constant(safe-area-inset-bottom);
				margin-bottom: env(safe-area-inset-bottom);
				border-radius: 35rpx;
				box-shadow: 1px 4px 4px rgba(252, 42, 87, 0.3);

				.text {
					font-size: 30rpx;
					color: #fff;
				}
			}

			.empty {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;

				.txt {
					font-size: 40rpx;
					color: #bbb;
					margin: 100rpx 0;
				}

				.close {
					color: #fff;
					margin: 0;
					padding: 15rpx 40rpx;
					border-radius: 10rpx;
					font-size: 30rpx;
					/* #ifndef APP-NVUE */
					letter-spacing: 2px;
					/* #endif */
				}
			}
		}
	}
</style>