<script>
	export default {
		onLaunch: function() {
			let that = this;
			// getTheme().then(resP => {
			// 	Cache.set('theme', `theme${Number(resP.data.value)}`);
			// 	that.globalData.theme = `theme${Number(resP.data.value)}`;
			// 	// // #ifdef H5
			// 	// window.document.documentElement.setAttribute('data-theme', that.globalData.theme);
			// 	// // #endif
			// })

			const updateManager = uni.getUpdateManager()
			updateManager.onCheckForUpdate(function(res) {
				if (res.hasUpdate) {
					updateManager.onUpdateReady(function() {
						uni.showModal({
							title: '更新提示',
							content: '新版本已经更新完毕，点击立即启用',
							showCancel: false,
							success: function(res) {
								if (res.confirm) {
									updateManager.applyUpdate()
								}
							}
						})
					})
					updateManager.onUpdateFailed(function() {
						uni.showModal({
							title: '温馨提示',
							content: '新版本已经上线，请您重启微信或手机后再试',
						})
					})
				}
			})
		},
		methods: {
		},
		onShow() {
			// uni.hideTabBar()
			const updateManager = uni.getUpdateManager()
			updateManager.onCheckForUpdate(res => {
				console.log(res.hasUpdate)
				if (res.hasUpdate) {
					uni.showModal({
						content: '新版本已经准备好，是否重启应用？',
						showCancel: false,
						confirmText: '确定',
						success: res => {
							if (res.confirm) {
								updateManager.onUpdateReady(res => {
									updateManager.applyUpdate()
								})
								updateManager.onUpdateFailed(res => {
									uni.showModal({
										content: '下载失败，请删除当前小程序后重新打开',
										showCancel: false,
										confirmText: '知道了'
									})
								})
							}
						}
					})
				} else {}
			})
			// #ifndef MP-WEIXIN
			uni.hideTabBar()
			// #endif
		},
		globalData: {

		},
		methods: {

		},
		onHide: function() {
			// console.log('App Hide')
		}
	}
</script>

<style lang="scss">
	@import '@/uni_modules/uview-ui/index.scss';

	.scroll {
		width: 100%;
		white-space: nowrap; //标签不换行

		view {
			display: inline-block;
		}
	}

	.mybox {
		flex-direction: column;
		padding: 40rpx 60rpx;
		width: 480rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.tit {
			font-weight: 700;
			font-size: 36rpx;
			color: #000;
			margin-top: 10rpx;
			margin-bottom: 25rpx;
		}

		.desc {
			font-size: 32rpx;
			color: #FF5500;
			margin-bottom: 30rpx;
		}

		.btn {

			margin-bottom: 15rpx;

			button {
				font-size: 28rpx;
				width: 290rpx;
				padding: 0;
				margin: 0 auto;
				height: 72rpx;
				line-height: 72rpx;
				color: #fff;
				background-image: linear-gradient(180deg, #FF8510 0, #FFBD73 100%);
				border-radius: 50rpx;

				&::after {
					border: none;
				}
			}
		}

		.btn2 {

			margin-bottom: 0rpx;

			button {
				font-size: 28rpx;
				width: 290rpx;
				padding: 0;
				margin: 0 auto;
				height: 72rpx;
				line-height: 72rpx;
				color: #666;
				background: #fff;
				border-radius: 50rpx;

				&::after {
					border: none;
				}
			}
		}
	}

	.tabbar-container {
		position: fixed;
		bottom: -2rpx;
		left: 0;
		width: 100%;
		padding-top: 10rpx;
		padding-bottom: 10rpx;
		padding-bottom: calc(constant(safe-area-inset-bottom) + 10rpx);
		padding-bottom: calc(env(safe-area-inset-bottom) + 10rpx);
		display: flex;
		z-index: 99;
		align-items: center;
		justify-content: space-around;
		color: #9a9a9a;
		border-top: 2rpx solid #efefef;
		background: #fff;
	}

	.tabbar-container .tabbar-item {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		text-align: center;
	}

	.tabbar-container .item-active {
		color: #ff942b;
	}

	.tabbar-container .center-item {
		display: block;
		position: relative;
	}

	.tabbar-container .tabbar-item .item-top {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
	}

	.tabbar-container .tabbar-item .item-top image {
		width: 64rpx;
		height: 64rpx;
	}

	.tabbar-container .tabbar-item .item-bottom {
		font-size: 22rpx;
		width: 100%;
	}

	.header1 {
		display: flex;
		flex-direction: row;
		position: relative;
		justify-content: center;
		align-items: center;
		line-height: 88rpx;
	}

	.head {
		position: sticky;
		left: 0;

		top: 0;
		width: 100%;
		flex-direction: column;
		background: #FF8B1B;
		color: #fff;
		z-index: 99;

		display: flex;

		.text {
			font-size: 36rpx;
			color: #fff;
			font-weight: 400;
		}

		.left {
			position: absolute;
			left: 0rpx;
			padding: 0 20rpx;
			top: 0;
			display: flex;
			align-items: flex-end;
			justify-content: flex-end;
			height: 100%;

			.itemList {
				padding: 0rpx;
				height: 64rpx;
				margin-bottom: 15rpx;
				display: flex;
				// min-width: 110rpx;
				flex-direction: row;
				align-items: center;
				justify-content: center;
				// background-color: rgba(0, 0, 0, .1);
				// border: 1rpx solid hsla(0, 0%, 100%, .15);
				// border-radius: 60rpx;


				.item {
					position: relative;
					flex: 1;
					height: 64rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					button {
						display: flex;
						flex-direction: row;
						align-items: center;
						border: none;
						justify-content: center;
						height: 64rpx;
						padding: 0;
						width: 64rpx;
						line-height: 64rpx;
						background: none;

						&::after {
							border: none;
						}
					}

					&::after {
						position: absolute;
						right: 0;
						top: 50%;
						height: 36rpx;
						margin-top: -18rpx;
						background-color: hsla(0, 0%, 100%, .05);
						content: "";
						width: 4rpx;
					}

					&:last-child {
						&::after {
							background: none;
						}
					}

					image {
						width: 36rpx;
						height: 36rpx;
					}
				}
			}
		}


	}
</style>