<template>
	<view class="container">
		<view class="questionnaire-container">
			<!-- 问卷标题 -->
			<view class="questionnaire-title">
				<text>{{ title }}</text>
			</view>
			
			<!-- 问题区域 -->
			<view class="question-area">
				<!-- 问题内容 -->
				<view class="question-content">
					<view v-if="questions.length>0">
						<view class="question-title">
							<text>{{ currentQuestion + 1 }}. {{ questions[currentQuestion].title }}</text>
						</view>
						
						<!-- 问题描述 -->
						<view class="question-desc" v-if="questions[currentQuestion].desc">
							<text>{{ questions[currentQuestion].desc }}</text>
						</view>
						
						<!-- 根据问题类型显示不同的选项 -->
						
						<!-- 单选题 -->
						<view v-if="questions.length>0 && questions[currentQuestion].type === 'radio'">
							<radio-group @change="radioChange">
								<label class="radio-item" v-for="(option, index) in questions[currentQuestion].options" :key="index">
									<view class="radio-label">
										<radio :value="option.value" :checked="answers[currentQuestion] === option.value" />
										<text>{{ option.label }}</text>
									</view>
								</label>
							</radio-group>
						</view>
						
						<!-- 多选题 -->
						<view v-if="questions.length>0 && questions[currentQuestion].type === 'checkbox'">
							<checkbox-group @change="checkboxChange">
								<label class="checkbox-item" v-for="(option, index) in questions[currentQuestion].options" :key="index">
									<view class="checkbox-label">
										<checkbox :value="option.value" :checked="answers[currentQuestion] && answers[currentQuestion].includes(option.value)" />
										<text>{{ option.label }}</text>
									</view>
								</label>
							</checkbox-group>
						</view>
						
						<!-- 输入框 -->
						<view v-if="questions.length>0 && questions[currentQuestion].type === 'input'">
							<input class="input-field" type="text" v-model="inputValue" placeholder="请输入" @blur="inputChange" />
						</view>
						
						<!-- 专业选择 -->
						<view v-if="questions.length>0 && questions[currentQuestion].id === 7">
							<view class="test tree-select-container">
								<!-- 搜索框 -->
								<view class="search-box">
									<view class="search-input">
										<input type="text" placeholder="请输入专业名称" placeholder-class="placeholder" />
									</view>
								</view>
								
								<wyh-tree-select2 ref="treeSelect" :height="300" :items="formatTreeData(items)" :activeIds="yxzy_ids" :max="3" @clickItem="onItem" @onChange="bindChange($event, 'yxzy_ids')" @clickNav="handleCategoryClick" class="small-font-tree-select">
									<!-- 自定义右侧专业列表模板 -->
									<template #right-item="{ item }">
										<!-- 使用条件样式而不是v-if -->
										<view style="padding: 20rpx; margin-bottom: 20rpx; border-radius: 10rpx; position: relative;" 
											:style="yxzy_ids.includes(Number(item.id)) ? 'background-color: #FFEB3B; border: 2rpx solid #FFC107;' : 'background-color: #f8f8f8;'" 
											@click="directSelectMajor(item)">
											
											<!-- 选中状态，只在选中时显示 -->
											<view v-if="yxzy_ids.includes(Number(item.id))" style="position: absolute; top: 10rpx; right: 10rpx; background-color: #FF6600; color: white; padding: 5rpx 10rpx; border-radius: 10rpx; font-size: 20rpx;">已选</view>
											
											<view style="font-size: 28rpx; margin-bottom: 10rpx; display: flex; justify-content: space-between; align-items: center;" 
												:style="yxzy_ids.includes(Number(item.id)) ? 'color: #FF6600; font-weight: bold;' : 'color: #333;'">
												
												{{ item.text }}
												<view style="display: flex; align-items: center; justify-content: center;" v-if="yxzy_ids.includes(Number(item.id))">
													<image :src="imgUrl+'/qdkbm/newimage/fhpic/fh-duihao.png'" style="width:40rpx;height:40rpx;"></image>
												</view>
											</view>
											<view class="major-info">
												<view class="info-row">
													<view class="info-label">修学年限</view>
													<view class="info-value">四年</view>
												</view>
												<view class="info-row">
													<view class="info-label">授予学位</view>
													<view class="info-value">医学学士</view>
												</view>
												<view class="info-row">
													<view class="info-label">平均薪资</view>
													<view class="info-value">￥10.1万</view>
												</view>
												<view class="info-row">
													<view class="info-label">专业代码</view>
													<view class="info-value">{{ item.code || '100301K' }}</view>
												</view>
											</view>
										</view>
									</template>
								</wyh-tree-select2>
							</view>
						</view>
						
						<!-- 其他问题类型... -->
						
					</view>
				</view>
			</view>
			
			<!-- 导航按钮 -->
			<view class="nav-buttons">
				<button class="nav-button prev-button" @click="prevQuestion" v-if="currentQuestion > 0">上一题</button>
				<button class="nav-button next-button" :class="{ disabled: !isCurrentQuestionAnswered }" @click="nextQuestion" v-if="currentQuestion < questions.length - 1">下一题</button>
				<button class="nav-button next-button" :class="{ disabled: !isCurrentQuestionAnswered }" @click="submitQuestionnaire" v-if="currentQuestion === questions.length - 1">提交</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				title: '职业规划问卷',
				currentQuestion: 0,
				questions: [
					// 问题列表...
				],
				answers: {},
				inputValue: '',
				isCurrentQuestionAnswered: false,
				
				// 专业选择相关数据
				yxzy_ids: [], // 已选专业ID数组
				items: [], // 专业数据
				imgUrl: 'https://fh.hbsxedu.com',
			}
		},
		methods: {
			// 直接处理专业选择的方法
			directSelectMajor(item) {
				console.log('直接点击专业项:', item);
				
				// 先强制初始化 yxzy_ids 数组
				if (!this.yxzy_ids) {
					this.yxzy_ids = [];
				}
				
				// 将item.id转换为数字类型
				const itemId = Number(item.id);
				
				// 检查是否已经选中
				const index = this.yxzy_ids.findIndex(id => id === itemId);
				const isSelected = index !== -1;
				console.log('当前专业是否选中:', isSelected, '专业ID:', itemId, '类型:', typeof itemId);
				console.log('当前选中的专业列表:', this.yxzy_ids);
				
				if (isSelected) {
					// 如果已经选中，则取消选择
					this.yxzy_ids.splice(index, 1);
					console.log('取消选择专业:', item.text);
					uni.showToast({
						title: '已取消选择: ' + item.text,
						icon: 'none',
						duration: 1000
					});
				} else {
					// 如果未选中且未达到最大选择数，则选中
					if (this.yxzy_ids.length < 3) { // 修改为最多选择3个专业
						// 添加到数组中，确保使用数字类型
						this.yxzy_ids.push(itemId);
						console.log('选中专业:', item.text);
						uni.showToast({
							title: '已选择: ' + item.text,
							icon: 'none',
							duration: 1000
						});
					} else {
						uni.showToast({
							title: '最多只能选择3个专业', // 修改提示文本
							icon: 'none',
							duration: 2000
						});
						return;
					}
				}
				
				// 同步更新组件的activeIds
				if (this.$refs.treeSelect) {
					this.$refs.treeSelect.active_ids = [...this.yxzy_ids];
				}
				
				// 强制刷新组件
				this.$forceUpdate();
				
				// 更新答案并检查状态
				this.$set(this.answers, 7, this.yxzy_ids);
				this.checkAnswerStatus();
				console.log('更新后的专业选择:', this.yxzy_ids);
				
				// 添加延时刷新，确保视图更新
				setTimeout(() => {
					this.$forceUpdate();
				}, 100);
			},
			
			// 其他方法...
		}
	}
</script>

<style>
	/* 样式... */
	
	/* 专业项的默认样式 */
	.major-item {
		padding: 20rpx; 
		margin-bottom: 20rpx; 
		border-radius: 10rpx; 
		position: relative;
		background-color: #f8f8f8;
		transition: all 0.3s ease;
	}
	
	/* 选中的专业项样式 */
	.major-item-selected {
		background-color: #FFEB3B !important;
		border: 2rpx solid #FFC107 !important;
		box-shadow: 0 4rpx 12rpx rgba(255, 193, 7, 0.5) !important;
	}
	
	/* 选中标记 */
	.selected-tag {
		position: absolute; 
		top: 10rpx; 
		right: 10rpx; 
		background-color: #FF6600; 
		color: white; 
		padding: 5rpx 10rpx; 
		border-radius: 10rpx; 
		font-size: 20rpx;
	}
	
	/* 专业标题 */
	.major-title {
		font-size: 28rpx; 
		margin-bottom: 10rpx; 
		display: flex; 
		justify-content: space-between; 
		align-items: center;
		color: #333;
	}
	
	/* 选中的专业标题 */
	.major-title-selected {
		color: #FF6600 !important;
		font-weight: bold !important;
	}
</style>
